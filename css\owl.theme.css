/*
* 	Owl Carousel Owl Demo Theme 
*	v1.3.3
*/
#owl-demo-2 .owl-controls .owl-page{
	display:none;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
	margin-top:20px;
}
#owl-demo-2 .item{
    margin: 0px;
}
#owl-demo-2 .item img{
    display: block;
    width:100%;
}
#owl-demo-3 .item{
    margin: 5px;
}
#owl-demo-3 .item img{
    display: block;
    width:100%;
}
#owl-demo-3 .owl-controls .owl-page{
	display:none;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
	margin-top:20px;
}
#owl-demo-3.owl-theme .owl-controls .owl-buttons div {
   color: #FFF;
    display: inline-block;
    zoom: 1;
    margin: 5px;
    padding: 5px 15px;
    font-size: 18px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    background: #ADADAD;
    border: 1px solid #969696;
    box-shadow: 1px 2px 2px #B3B3B3;
	transition:all 0.5s ease-in-out;
}
#owl-demo-3.owl-theme .owl-controls .owl-buttons div:hover,#owl-demo-3.owl-theme .owl-controls .owl-buttons div:focus {
    color: #FFF;
    background:#499202;
}
#owl-demo-4 .item{
    margin: 5px;
}
#owl-demo-4 .item img{
    display: block;
    width:100%;
}
#owl-demo-4 .owl-controls .owl-page{
	display:none;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
	margin-top:20px;
}
#owl-demo-4.owl-theme .owl-controls .owl-buttons div {
   color: #FFF;
    display: inline-block;
    zoom: 1;
    margin: 5px;
    padding: 5px 15px;
    font-size: 18px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    background: #ADADAD;
    border: 1px solid #969696;
    box-shadow: 1px 2px 2px #B3B3B3;
	transition:all 0.5s ease-in-out;
}
#owl-demo-4.owl-theme .owl-controls .owl-buttons div:hover,#owl-demo-4.owl-theme .owl-controls .owl-buttons div:focus {
    color: #FFF;
    background:#499202;
}
#owl-demo-1 .owl-controls .owl-buttons div {
    color: #FFF;
    display: inline-block;
    zoom: 1;
    margin: 5px;
    padding: 10px 10px;
    font-size: 12px;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    border-radius: 3px;
    background: #ABABAB;
}
#owl-demo-1 .item{
	padding:10px 30px;
}
.owl-theme .owl-controls{
	margin-top: 10px;
	text-align: center;
}
#owl-demo-1 .owl-controls .owl-page{
	display:none;
}
/* Styling Next and Prev buttons */

.owl-theme .owl-controls .owl-buttons div{
	color: #fff;
    display: inline-block;
    zoom: 1;
    margin: 5px;
    padding: 5px 12px;
    font-size: 18px;
    -webkit-border-radius: 5px;
    -moz-border-radius:5px;
   border-radius: 5px;
    background: #b9b9b9;
	
}
/* Clickable class fix problem with hover on touch devices */
/* Use it for non-touch hover action */
.owl-theme .owl-controls.clickable .owl-buttons div:hover{
	filter: Alpha(Opacity=100);/*IE7 fix*/
	opacity: 1;
	text-decoration: none;
}

/* Styling Pagination*/
#owl-demo .owl-controls .owl-page{
	display:none;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
	margin-top:20px;
}
.owl-theme .owl-controls .owl-page{
	display: inline-block;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
	margin-top:20px;
}
.owl-theme .owl-controls .owl-page span{
	display: block;
	width: 12px;
	height: 12px;
	margin: 5px 7px;
	filter: Alpha(Opacity=50);/*IE7 fix*/
	opacity: 0.5;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	border-radius: 20px;
	background: #869791;
}

.owl-theme .owl-controls .owl-page.active span,
.owl-theme .owl-controls.clickable .owl-page:hover span{
	filter: Alpha(Opacity=100);/*IE7 fix*/
	opacity: 1;
}

/* If PaginationNumbers is true */

.owl-theme .owl-controls .owl-page span.owl-numbers{
	height: auto;
	width: auto;
	color: #FFF;
	padding: 2px 10px;
	font-size: 12px;
	-webkit-border-radius: 30px;
	-moz-border-radius: 30px;
	border-radius: 30px;
}

/* preloading images */
.owl-item.loading{
	min-height: 150px;
	background: url(AjaxLoader.gif) no-repeat center center
}