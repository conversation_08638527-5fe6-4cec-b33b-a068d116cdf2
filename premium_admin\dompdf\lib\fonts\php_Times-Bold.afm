$this->fonts[$font]=array (
  'FontName' => 'Times-Bold',
  'FullName' => 'Times Bold',
  'FamilyName' => 'Times',
  'Weight' => 'Bold',
  'ItalicAngle' => '0',
  'IsFixedPitch' => 'false',
  'CharacterSet' => 'ExtendedRoman',
  'FontBBox' => 
  array (
    0 => '-168',
    1 => '-218',
    2 => '1000',
    3 => '935',
  ),
  'UnderlinePosition' => '-100',
  'UnderlineThickness' => '50',
  'Version' => '002.000',
  'EncodingScheme' => 'AdobeStandardEncoding',
  'CapHeight' => '676',
  'XHeight' => '461',
  'Ascender' => '683',
  'Descender' => '-217',
  'StdHW' => '44',
  'StdVW' => '139',
  'StartCharMetrics' => '315',
  'C' => 
  array (
    32 => 
    array (
      'C' => '32',
      'WX' => '250',
      'N' => 'space',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
    'space' => 
    array (
      'C' => '32',
      'WX' => '250',
      'N' => 'space',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
    33 => 
    array (
      'C' => '33',
      'WX' => '333',
      'N' => 'exclam',
      'B' => 
      array (
        0 => '81',
        1 => '-13',
        2 => '251',
        3 => '691',
      ),
    ),
    'exclam' => 
    array (
      'C' => '33',
      'WX' => '333',
      'N' => 'exclam',
      'B' => 
      array (
        0 => '81',
        1 => '-13',
        2 => '251',
        3 => '691',
      ),
    ),
    34 => 
    array (
      'C' => '34',
      'WX' => '555',
      'N' => 'quotedbl',
      'B' => 
      array (
        0 => '83',
        1 => '404',
        2 => '472',
        3 => '691',
      ),
    ),
    'quotedbl' => 
    array (
      'C' => '34',
      'WX' => '555',
      'N' => 'quotedbl',
      'B' => 
      array (
        0 => '83',
        1 => '404',
        2 => '472',
        3 => '691',
      ),
    ),
    35 => 
    array (
      'C' => '35',
      'WX' => '500',
      'N' => 'numbersign',
      'B' => 
      array (
        0 => '4',
        1 => '0',
        2 => '496',
        3 => '700',
      ),
    ),
    'numbersign' => 
    array (
      'C' => '35',
      'WX' => '500',
      'N' => 'numbersign',
      'B' => 
      array (
        0 => '4',
        1 => '0',
        2 => '496',
        3 => '700',
      ),
    ),
    36 => 
    array (
      'C' => '36',
      'WX' => '500',
      'N' => 'dollar',
      'B' => 
      array (
        0 => '29',
        1 => '-99',
        2 => '472',
        3 => '750',
      ),
    ),
    'dollar' => 
    array (
      'C' => '36',
      'WX' => '500',
      'N' => 'dollar',
      'B' => 
      array (
        0 => '29',
        1 => '-99',
        2 => '472',
        3 => '750',
      ),
    ),
    37 => 
    array (
      'C' => '37',
      'WX' => '1000',
      'N' => 'percent',
      'B' => 
      array (
        0 => '124',
        1 => '-14',
        2 => '877',
        3 => '692',
      ),
    ),
    'percent' => 
    array (
      'C' => '37',
      'WX' => '1000',
      'N' => 'percent',
      'B' => 
      array (
        0 => '124',
        1 => '-14',
        2 => '877',
        3 => '692',
      ),
    ),
    38 => 
    array (
      'C' => '38',
      'WX' => '833',
      'N' => 'ampersand',
      'B' => 
      array (
        0 => '62',
        1 => '-16',
        2 => '787',
        3 => '691',
      ),
    ),
    'ampersand' => 
    array (
      'C' => '38',
      'WX' => '833',
      'N' => 'ampersand',
      'B' => 
      array (
        0 => '62',
        1 => '-16',
        2 => '787',
        3 => '691',
      ),
    ),
    39 => 
    array (
      'C' => '39',
      'WX' => '333',
      'N' => 'quoteright',
      'B' => 
      array (
        0 => '79',
        1 => '356',
        2 => '263',
        3 => '691',
      ),
    ),
    'quoteright' => 
    array (
      'C' => '39',
      'WX' => '333',
      'N' => 'quoteright',
      'B' => 
      array (
        0 => '79',
        1 => '356',
        2 => '263',
        3 => '691',
      ),
    ),
    40 => 
    array (
      'C' => '40',
      'WX' => '333',
      'N' => 'parenleft',
      'B' => 
      array (
        0 => '46',
        1 => '-168',
        2 => '306',
        3 => '694',
      ),
    ),
    'parenleft' => 
    array (
      'C' => '40',
      'WX' => '333',
      'N' => 'parenleft',
      'B' => 
      array (
        0 => '46',
        1 => '-168',
        2 => '306',
        3 => '694',
      ),
    ),
    41 => 
    array (
      'C' => '41',
      'WX' => '333',
      'N' => 'parenright',
      'B' => 
      array (
        0 => '27',
        1 => '-168',
        2 => '287',
        3 => '694',
      ),
    ),
    'parenright' => 
    array (
      'C' => '41',
      'WX' => '333',
      'N' => 'parenright',
      'B' => 
      array (
        0 => '27',
        1 => '-168',
        2 => '287',
        3 => '694',
      ),
    ),
    42 => 
    array (
      'C' => '42',
      'WX' => '500',
      'N' => 'asterisk',
      'B' => 
      array (
        0 => '56',
        1 => '255',
        2 => '447',
        3 => '691',
      ),
    ),
    'asterisk' => 
    array (
      'C' => '42',
      'WX' => '500',
      'N' => 'asterisk',
      'B' => 
      array (
        0 => '56',
        1 => '255',
        2 => '447',
        3 => '691',
      ),
    ),
    43 => 
    array (
      'C' => '43',
      'WX' => '570',
      'N' => 'plus',
      'B' => 
      array (
        0 => '33',
        1 => '0',
        2 => '537',
        3 => '506',
      ),
    ),
    'plus' => 
    array (
      'C' => '43',
      'WX' => '570',
      'N' => 'plus',
      'B' => 
      array (
        0 => '33',
        1 => '0',
        2 => '537',
        3 => '506',
      ),
    ),
    44 => 
    array (
      'C' => '44',
      'WX' => '250',
      'N' => 'comma',
      'B' => 
      array (
        0 => '39',
        1 => '-180',
        2 => '223',
        3 => '155',
      ),
    ),
    'comma' => 
    array (
      'C' => '44',
      'WX' => '250',
      'N' => 'comma',
      'B' => 
      array (
        0 => '39',
        1 => '-180',
        2 => '223',
        3 => '155',
      ),
    ),
    45 => 
    array (
      'C' => '45',
      'WX' => '333',
      'N' => 'hyphen',
      'B' => 
      array (
        0 => '44',
        1 => '171',
        2 => '287',
        3 => '287',
      ),
    ),
    'hyphen' => 
    array (
      'C' => '45',
      'WX' => '333',
      'N' => 'hyphen',
      'B' => 
      array (
        0 => '44',
        1 => '171',
        2 => '287',
        3 => '287',
      ),
    ),
    46 => 
    array (
      'C' => '46',
      'WX' => '250',
      'N' => 'period',
      'B' => 
      array (
        0 => '41',
        1 => '-13',
        2 => '210',
        3 => '156',
      ),
    ),
    'period' => 
    array (
      'C' => '46',
      'WX' => '250',
      'N' => 'period',
      'B' => 
      array (
        0 => '41',
        1 => '-13',
        2 => '210',
        3 => '156',
      ),
    ),
    47 => 
    array (
      'C' => '47',
      'WX' => '278',
      'N' => 'slash',
      'B' => 
      array (
        0 => '-24',
        1 => '-19',
        2 => '302',
        3 => '691',
      ),
    ),
    'slash' => 
    array (
      'C' => '47',
      'WX' => '278',
      'N' => 'slash',
      'B' => 
      array (
        0 => '-24',
        1 => '-19',
        2 => '302',
        3 => '691',
      ),
    ),
    48 => 
    array (
      'C' => '48',
      'WX' => '500',
      'N' => 'zero',
      'B' => 
      array (
        0 => '24',
        1 => '-13',
        2 => '476',
        3 => '688',
      ),
    ),
    'zero' => 
    array (
      'C' => '48',
      'WX' => '500',
      'N' => 'zero',
      'B' => 
      array (
        0 => '24',
        1 => '-13',
        2 => '476',
        3 => '688',
      ),
    ),
    49 => 
    array (
      'C' => '49',
      'WX' => '500',
      'N' => 'one',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '442',
        3 => '688',
      ),
    ),
    'one' => 
    array (
      'C' => '49',
      'WX' => '500',
      'N' => 'one',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '442',
        3 => '688',
      ),
    ),
    50 => 
    array (
      'C' => '50',
      'WX' => '500',
      'N' => 'two',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '478',
        3 => '688',
      ),
    ),
    'two' => 
    array (
      'C' => '50',
      'WX' => '500',
      'N' => 'two',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '478',
        3 => '688',
      ),
    ),
    51 => 
    array (
      'C' => '51',
      'WX' => '500',
      'N' => 'three',
      'B' => 
      array (
        0 => '16',
        1 => '-14',
        2 => '468',
        3 => '688',
      ),
    ),
    'three' => 
    array (
      'C' => '51',
      'WX' => '500',
      'N' => 'three',
      'B' => 
      array (
        0 => '16',
        1 => '-14',
        2 => '468',
        3 => '688',
      ),
    ),
    52 => 
    array (
      'C' => '52',
      'WX' => '500',
      'N' => 'four',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '475',
        3 => '688',
      ),
    ),
    'four' => 
    array (
      'C' => '52',
      'WX' => '500',
      'N' => 'four',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '475',
        3 => '688',
      ),
    ),
    53 => 
    array (
      'C' => '53',
      'WX' => '500',
      'N' => 'five',
      'B' => 
      array (
        0 => '22',
        1 => '-8',
        2 => '470',
        3 => '676',
      ),
    ),
    'five' => 
    array (
      'C' => '53',
      'WX' => '500',
      'N' => 'five',
      'B' => 
      array (
        0 => '22',
        1 => '-8',
        2 => '470',
        3 => '676',
      ),
    ),
    54 => 
    array (
      'C' => '54',
      'WX' => '500',
      'N' => 'six',
      'B' => 
      array (
        0 => '28',
        1 => '-13',
        2 => '475',
        3 => '688',
      ),
    ),
    'six' => 
    array (
      'C' => '54',
      'WX' => '500',
      'N' => 'six',
      'B' => 
      array (
        0 => '28',
        1 => '-13',
        2 => '475',
        3 => '688',
      ),
    ),
    55 => 
    array (
      'C' => '55',
      'WX' => '500',
      'N' => 'seven',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '477',
        3 => '676',
      ),
    ),
    'seven' => 
    array (
      'C' => '55',
      'WX' => '500',
      'N' => 'seven',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '477',
        3 => '676',
      ),
    ),
    56 => 
    array (
      'C' => '56',
      'WX' => '500',
      'N' => 'eight',
      'B' => 
      array (
        0 => '28',
        1 => '-13',
        2 => '472',
        3 => '688',
      ),
    ),
    'eight' => 
    array (
      'C' => '56',
      'WX' => '500',
      'N' => 'eight',
      'B' => 
      array (
        0 => '28',
        1 => '-13',
        2 => '472',
        3 => '688',
      ),
    ),
    57 => 
    array (
      'C' => '57',
      'WX' => '500',
      'N' => 'nine',
      'B' => 
      array (
        0 => '26',
        1 => '-13',
        2 => '473',
        3 => '688',
      ),
    ),
    'nine' => 
    array (
      'C' => '57',
      'WX' => '500',
      'N' => 'nine',
      'B' => 
      array (
        0 => '26',
        1 => '-13',
        2 => '473',
        3 => '688',
      ),
    ),
    58 => 
    array (
      'C' => '58',
      'WX' => '333',
      'N' => 'colon',
      'B' => 
      array (
        0 => '82',
        1 => '-13',
        2 => '251',
        3 => '472',
      ),
    ),
    'colon' => 
    array (
      'C' => '58',
      'WX' => '333',
      'N' => 'colon',
      'B' => 
      array (
        0 => '82',
        1 => '-13',
        2 => '251',
        3 => '472',
      ),
    ),
    59 => 
    array (
      'C' => '59',
      'WX' => '333',
      'N' => 'semicolon',
      'B' => 
      array (
        0 => '82',
        1 => '-180',
        2 => '266',
        3 => '472',
      ),
    ),
    'semicolon' => 
    array (
      'C' => '59',
      'WX' => '333',
      'N' => 'semicolon',
      'B' => 
      array (
        0 => '82',
        1 => '-180',
        2 => '266',
        3 => '472',
      ),
    ),
    60 => 
    array (
      'C' => '60',
      'WX' => '570',
      'N' => 'less',
      'B' => 
      array (
        0 => '31',
        1 => '-8',
        2 => '539',
        3 => '514',
      ),
    ),
    'less' => 
    array (
      'C' => '60',
      'WX' => '570',
      'N' => 'less',
      'B' => 
      array (
        0 => '31',
        1 => '-8',
        2 => '539',
        3 => '514',
      ),
    ),
    61 => 
    array (
      'C' => '61',
      'WX' => '570',
      'N' => 'equal',
      'B' => 
      array (
        0 => '33',
        1 => '107',
        2 => '537',
        3 => '399',
      ),
    ),
    'equal' => 
    array (
      'C' => '61',
      'WX' => '570',
      'N' => 'equal',
      'B' => 
      array (
        0 => '33',
        1 => '107',
        2 => '537',
        3 => '399',
      ),
    ),
    62 => 
    array (
      'C' => '62',
      'WX' => '570',
      'N' => 'greater',
      'B' => 
      array (
        0 => '31',
        1 => '-8',
        2 => '539',
        3 => '514',
      ),
    ),
    'greater' => 
    array (
      'C' => '62',
      'WX' => '570',
      'N' => 'greater',
      'B' => 
      array (
        0 => '31',
        1 => '-8',
        2 => '539',
        3 => '514',
      ),
    ),
    63 => 
    array (
      'C' => '63',
      'WX' => '500',
      'N' => 'question',
      'B' => 
      array (
        0 => '57',
        1 => '-13',
        2 => '445',
        3 => '689',
      ),
    ),
    'question' => 
    array (
      'C' => '63',
      'WX' => '500',
      'N' => 'question',
      'B' => 
      array (
        0 => '57',
        1 => '-13',
        2 => '445',
        3 => '689',
      ),
    ),
    64 => 
    array (
      'C' => '64',
      'WX' => '930',
      'N' => 'at',
      'B' => 
      array (
        0 => '108',
        1 => '-19',
        2 => '822',
        3 => '691',
      ),
    ),
    'at' => 
    array (
      'C' => '64',
      'WX' => '930',
      'N' => 'at',
      'B' => 
      array (
        0 => '108',
        1 => '-19',
        2 => '822',
        3 => '691',
      ),
    ),
    65 => 
    array (
      'C' => '65',
      'WX' => '722',
      'N' => 'A',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '689',
        3 => '690',
      ),
    ),
    'A' => 
    array (
      'C' => '65',
      'WX' => '722',
      'N' => 'A',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '689',
        3 => '690',
      ),
    ),
    66 => 
    array (
      'C' => '66',
      'WX' => '667',
      'N' => 'B',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '619',
        3 => '676',
      ),
    ),
    'B' => 
    array (
      'C' => '66',
      'WX' => '667',
      'N' => 'B',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '619',
        3 => '676',
      ),
    ),
    67 => 
    array (
      'C' => '67',
      'WX' => '722',
      'N' => 'C',
      'B' => 
      array (
        0 => '49',
        1 => '-19',
        2 => '687',
        3 => '691',
      ),
    ),
    'C' => 
    array (
      'C' => '67',
      'WX' => '722',
      'N' => 'C',
      'B' => 
      array (
        0 => '49',
        1 => '-19',
        2 => '687',
        3 => '691',
      ),
    ),
    68 => 
    array (
      'C' => '68',
      'WX' => '722',
      'N' => 'D',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '690',
        3 => '676',
      ),
    ),
    'D' => 
    array (
      'C' => '68',
      'WX' => '722',
      'N' => 'D',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '690',
        3 => '676',
      ),
    ),
    69 => 
    array (
      'C' => '69',
      'WX' => '667',
      'N' => 'E',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '641',
        3 => '676',
      ),
    ),
    'E' => 
    array (
      'C' => '69',
      'WX' => '667',
      'N' => 'E',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '641',
        3 => '676',
      ),
    ),
    70 => 
    array (
      'C' => '70',
      'WX' => '611',
      'N' => 'F',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '583',
        3 => '676',
      ),
    ),
    'F' => 
    array (
      'C' => '70',
      'WX' => '611',
      'N' => 'F',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '583',
        3 => '676',
      ),
    ),
    71 => 
    array (
      'C' => '71',
      'WX' => '778',
      'N' => 'G',
      'B' => 
      array (
        0 => '37',
        1 => '-19',
        2 => '755',
        3 => '691',
      ),
    ),
    'G' => 
    array (
      'C' => '71',
      'WX' => '778',
      'N' => 'G',
      'B' => 
      array (
        0 => '37',
        1 => '-19',
        2 => '755',
        3 => '691',
      ),
    ),
    72 => 
    array (
      'C' => '72',
      'WX' => '778',
      'N' => 'H',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '759',
        3 => '676',
      ),
    ),
    'H' => 
    array (
      'C' => '72',
      'WX' => '778',
      'N' => 'H',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '759',
        3 => '676',
      ),
    ),
    73 => 
    array (
      'C' => '73',
      'WX' => '389',
      'N' => 'I',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '370',
        3 => '676',
      ),
    ),
    'I' => 
    array (
      'C' => '73',
      'WX' => '389',
      'N' => 'I',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '370',
        3 => '676',
      ),
    ),
    74 => 
    array (
      'C' => '74',
      'WX' => '500',
      'N' => 'J',
      'B' => 
      array (
        0 => '3',
        1 => '-96',
        2 => '479',
        3 => '676',
      ),
    ),
    'J' => 
    array (
      'C' => '74',
      'WX' => '500',
      'N' => 'J',
      'B' => 
      array (
        0 => '3',
        1 => '-96',
        2 => '479',
        3 => '676',
      ),
    ),
    75 => 
    array (
      'C' => '75',
      'WX' => '778',
      'N' => 'K',
      'B' => 
      array (
        0 => '30',
        1 => '0',
        2 => '769',
        3 => '676',
      ),
    ),
    'K' => 
    array (
      'C' => '75',
      'WX' => '778',
      'N' => 'K',
      'B' => 
      array (
        0 => '30',
        1 => '0',
        2 => '769',
        3 => '676',
      ),
    ),
    76 => 
    array (
      'C' => '76',
      'WX' => '667',
      'N' => 'L',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '638',
        3 => '676',
      ),
    ),
    'L' => 
    array (
      'C' => '76',
      'WX' => '667',
      'N' => 'L',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '638',
        3 => '676',
      ),
    ),
    77 => 
    array (
      'C' => '77',
      'WX' => '944',
      'N' => 'M',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '921',
        3 => '676',
      ),
    ),
    'M' => 
    array (
      'C' => '77',
      'WX' => '944',
      'N' => 'M',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '921',
        3 => '676',
      ),
    ),
    78 => 
    array (
      'C' => '78',
      'WX' => '722',
      'N' => 'N',
      'B' => 
      array (
        0 => '16',
        1 => '-18',
        2 => '701',
        3 => '676',
      ),
    ),
    'N' => 
    array (
      'C' => '78',
      'WX' => '722',
      'N' => 'N',
      'B' => 
      array (
        0 => '16',
        1 => '-18',
        2 => '701',
        3 => '676',
      ),
    ),
    79 => 
    array (
      'C' => '79',
      'WX' => '778',
      'N' => 'O',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '743',
        3 => '691',
      ),
    ),
    'O' => 
    array (
      'C' => '79',
      'WX' => '778',
      'N' => 'O',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '743',
        3 => '691',
      ),
    ),
    80 => 
    array (
      'C' => '80',
      'WX' => '611',
      'N' => 'P',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '600',
        3 => '676',
      ),
    ),
    'P' => 
    array (
      'C' => '80',
      'WX' => '611',
      'N' => 'P',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '600',
        3 => '676',
      ),
    ),
    81 => 
    array (
      'C' => '81',
      'WX' => '778',
      'N' => 'Q',
      'B' => 
      array (
        0 => '35',
        1 => '-176',
        2 => '743',
        3 => '691',
      ),
    ),
    'Q' => 
    array (
      'C' => '81',
      'WX' => '778',
      'N' => 'Q',
      'B' => 
      array (
        0 => '35',
        1 => '-176',
        2 => '743',
        3 => '691',
      ),
    ),
    82 => 
    array (
      'C' => '82',
      'WX' => '722',
      'N' => 'R',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '715',
        3 => '676',
      ),
    ),
    'R' => 
    array (
      'C' => '82',
      'WX' => '722',
      'N' => 'R',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '715',
        3 => '676',
      ),
    ),
    83 => 
    array (
      'C' => '83',
      'WX' => '556',
      'N' => 'S',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '513',
        3 => '692',
      ),
    ),
    'S' => 
    array (
      'C' => '83',
      'WX' => '556',
      'N' => 'S',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '513',
        3 => '692',
      ),
    ),
    84 => 
    array (
      'C' => '84',
      'WX' => '667',
      'N' => 'T',
      'B' => 
      array (
        0 => '31',
        1 => '0',
        2 => '636',
        3 => '676',
      ),
    ),
    'T' => 
    array (
      'C' => '84',
      'WX' => '667',
      'N' => 'T',
      'B' => 
      array (
        0 => '31',
        1 => '0',
        2 => '636',
        3 => '676',
      ),
    ),
    85 => 
    array (
      'C' => '85',
      'WX' => '722',
      'N' => 'U',
      'B' => 
      array (
        0 => '16',
        1 => '-19',
        2 => '701',
        3 => '676',
      ),
    ),
    'U' => 
    array (
      'C' => '85',
      'WX' => '722',
      'N' => 'U',
      'B' => 
      array (
        0 => '16',
        1 => '-19',
        2 => '701',
        3 => '676',
      ),
    ),
    86 => 
    array (
      'C' => '86',
      'WX' => '722',
      'N' => 'V',
      'B' => 
      array (
        0 => '16',
        1 => '-18',
        2 => '701',
        3 => '676',
      ),
    ),
    'V' => 
    array (
      'C' => '86',
      'WX' => '722',
      'N' => 'V',
      'B' => 
      array (
        0 => '16',
        1 => '-18',
        2 => '701',
        3 => '676',
      ),
    ),
    87 => 
    array (
      'C' => '87',
      'WX' => '1000',
      'N' => 'W',
      'B' => 
      array (
        0 => '19',
        1 => '-15',
        2 => '981',
        3 => '676',
      ),
    ),
    'W' => 
    array (
      'C' => '87',
      'WX' => '1000',
      'N' => 'W',
      'B' => 
      array (
        0 => '19',
        1 => '-15',
        2 => '981',
        3 => '676',
      ),
    ),
    88 => 
    array (
      'C' => '88',
      'WX' => '722',
      'N' => 'X',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '699',
        3 => '676',
      ),
    ),
    'X' => 
    array (
      'C' => '88',
      'WX' => '722',
      'N' => 'X',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '699',
        3 => '676',
      ),
    ),
    89 => 
    array (
      'C' => '89',
      'WX' => '722',
      'N' => 'Y',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '699',
        3 => '676',
      ),
    ),
    'Y' => 
    array (
      'C' => '89',
      'WX' => '722',
      'N' => 'Y',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '699',
        3 => '676',
      ),
    ),
    90 => 
    array (
      'C' => '90',
      'WX' => '667',
      'N' => 'Z',
      'B' => 
      array (
        0 => '28',
        1 => '0',
        2 => '634',
        3 => '676',
      ),
    ),
    'Z' => 
    array (
      'C' => '90',
      'WX' => '667',
      'N' => 'Z',
      'B' => 
      array (
        0 => '28',
        1 => '0',
        2 => '634',
        3 => '676',
      ),
    ),
    91 => 
    array (
      'C' => '91',
      'WX' => '333',
      'N' => 'bracketleft',
      'B' => 
      array (
        0 => '67',
        1 => '-149',
        2 => '301',
        3 => '678',
      ),
    ),
    'bracketleft' => 
    array (
      'C' => '91',
      'WX' => '333',
      'N' => 'bracketleft',
      'B' => 
      array (
        0 => '67',
        1 => '-149',
        2 => '301',
        3 => '678',
      ),
    ),
    92 => 
    array (
      'C' => '92',
      'WX' => '278',
      'N' => 'backslash',
      'B' => 
      array (
        0 => '-25',
        1 => '-19',
        2 => '303',
        3 => '691',
      ),
    ),
    'backslash' => 
    array (
      'C' => '92',
      'WX' => '278',
      'N' => 'backslash',
      'B' => 
      array (
        0 => '-25',
        1 => '-19',
        2 => '303',
        3 => '691',
      ),
    ),
    93 => 
    array (
      'C' => '93',
      'WX' => '333',
      'N' => 'bracketright',
      'B' => 
      array (
        0 => '32',
        1 => '-149',
        2 => '266',
        3 => '678',
      ),
    ),
    'bracketright' => 
    array (
      'C' => '93',
      'WX' => '333',
      'N' => 'bracketright',
      'B' => 
      array (
        0 => '32',
        1 => '-149',
        2 => '266',
        3 => '678',
      ),
    ),
    94 => 
    array (
      'C' => '94',
      'WX' => '581',
      'N' => 'asciicircum',
      'B' => 
      array (
        0 => '73',
        1 => '311',
        2 => '509',
        3 => '676',
      ),
    ),
    'asciicircum' => 
    array (
      'C' => '94',
      'WX' => '581',
      'N' => 'asciicircum',
      'B' => 
      array (
        0 => '73',
        1 => '311',
        2 => '509',
        3 => '676',
      ),
    ),
    95 => 
    array (
      'C' => '95',
      'WX' => '500',
      'N' => 'underscore',
      'B' => 
      array (
        0 => '0',
        1 => '-125',
        2 => '500',
        3 => '-75',
      ),
    ),
    'underscore' => 
    array (
      'C' => '95',
      'WX' => '500',
      'N' => 'underscore',
      'B' => 
      array (
        0 => '0',
        1 => '-125',
        2 => '500',
        3 => '-75',
      ),
    ),
    96 => 
    array (
      'C' => '96',
      'WX' => '333',
      'N' => 'quoteleft',
      'B' => 
      array (
        0 => '70',
        1 => '356',
        2 => '254',
        3 => '691',
      ),
    ),
    'quoteleft' => 
    array (
      'C' => '96',
      'WX' => '333',
      'N' => 'quoteleft',
      'B' => 
      array (
        0 => '70',
        1 => '356',
        2 => '254',
        3 => '691',
      ),
    ),
    97 => 
    array (
      'C' => '97',
      'WX' => '500',
      'N' => 'a',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '488',
        3 => '473',
      ),
    ),
    'a' => 
    array (
      'C' => '97',
      'WX' => '500',
      'N' => 'a',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '488',
        3 => '473',
      ),
    ),
    98 => 
    array (
      'C' => '98',
      'WX' => '556',
      'N' => 'b',
      'B' => 
      array (
        0 => '17',
        1 => '-14',
        2 => '521',
        3 => '676',
      ),
    ),
    'b' => 
    array (
      'C' => '98',
      'WX' => '556',
      'N' => 'b',
      'B' => 
      array (
        0 => '17',
        1 => '-14',
        2 => '521',
        3 => '676',
      ),
    ),
    99 => 
    array (
      'C' => '99',
      'WX' => '444',
      'N' => 'c',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '430',
        3 => '473',
      ),
    ),
    'c' => 
    array (
      'C' => '99',
      'WX' => '444',
      'N' => 'c',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '430',
        3 => '473',
      ),
    ),
    100 => 
    array (
      'C' => '100',
      'WX' => '556',
      'N' => 'd',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '534',
        3 => '676',
      ),
    ),
    'd' => 
    array (
      'C' => '100',
      'WX' => '556',
      'N' => 'd',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '534',
        3 => '676',
      ),
    ),
    101 => 
    array (
      'C' => '101',
      'WX' => '444',
      'N' => 'e',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '426',
        3 => '473',
      ),
    ),
    'e' => 
    array (
      'C' => '101',
      'WX' => '444',
      'N' => 'e',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '426',
        3 => '473',
      ),
    ),
    102 => 
    array (
      'C' => '102',
      'WX' => '333',
      'N' => 'f',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '389',
        3 => '691',
      ),
      'L' => 
      array (
        0 => 'l',
        1 => 'fl',
      ),
    ),
    'f' => 
    array (
      'C' => '102',
      'WX' => '333',
      'N' => 'f',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '389',
        3 => '691',
      ),
      'L' => 
      array (
        0 => 'l',
        1 => 'fl',
      ),
    ),
    103 => 
    array (
      'C' => '103',
      'WX' => '500',
      'N' => 'g',
      'B' => 
      array (
        0 => '28',
        1 => '-206',
        2 => '483',
        3 => '473',
      ),
    ),
    'g' => 
    array (
      'C' => '103',
      'WX' => '500',
      'N' => 'g',
      'B' => 
      array (
        0 => '28',
        1 => '-206',
        2 => '483',
        3 => '473',
      ),
    ),
    104 => 
    array (
      'C' => '104',
      'WX' => '556',
      'N' => 'h',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '534',
        3 => '676',
      ),
    ),
    'h' => 
    array (
      'C' => '104',
      'WX' => '556',
      'N' => 'h',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '534',
        3 => '676',
      ),
    ),
    105 => 
    array (
      'C' => '105',
      'WX' => '278',
      'N' => 'i',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '255',
        3 => '691',
      ),
    ),
    'i' => 
    array (
      'C' => '105',
      'WX' => '278',
      'N' => 'i',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '255',
        3 => '691',
      ),
    ),
    106 => 
    array (
      'C' => '106',
      'WX' => '333',
      'N' => 'j',
      'B' => 
      array (
        0 => '-57',
        1 => '-203',
        2 => '263',
        3 => '691',
      ),
    ),
    'j' => 
    array (
      'C' => '106',
      'WX' => '333',
      'N' => 'j',
      'B' => 
      array (
        0 => '-57',
        1 => '-203',
        2 => '263',
        3 => '691',
      ),
    ),
    107 => 
    array (
      'C' => '107',
      'WX' => '556',
      'N' => 'k',
      'B' => 
      array (
        0 => '22',
        1 => '0',
        2 => '543',
        3 => '676',
      ),
    ),
    'k' => 
    array (
      'C' => '107',
      'WX' => '556',
      'N' => 'k',
      'B' => 
      array (
        0 => '22',
        1 => '0',
        2 => '543',
        3 => '676',
      ),
    ),
    108 => 
    array (
      'C' => '108',
      'WX' => '278',
      'N' => 'l',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '255',
        3 => '676',
      ),
    ),
    'l' => 
    array (
      'C' => '108',
      'WX' => '278',
      'N' => 'l',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '255',
        3 => '676',
      ),
    ),
    109 => 
    array (
      'C' => '109',
      'WX' => '833',
      'N' => 'm',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '814',
        3 => '473',
      ),
    ),
    'm' => 
    array (
      'C' => '109',
      'WX' => '833',
      'N' => 'm',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '814',
        3 => '473',
      ),
    ),
    110 => 
    array (
      'C' => '110',
      'WX' => '556',
      'N' => 'n',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '539',
        3 => '473',
      ),
    ),
    'n' => 
    array (
      'C' => '110',
      'WX' => '556',
      'N' => 'n',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '539',
        3 => '473',
      ),
    ),
    111 => 
    array (
      'C' => '111',
      'WX' => '500',
      'N' => 'o',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '476',
        3 => '473',
      ),
    ),
    'o' => 
    array (
      'C' => '111',
      'WX' => '500',
      'N' => 'o',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '476',
        3 => '473',
      ),
    ),
    112 => 
    array (
      'C' => '112',
      'WX' => '556',
      'N' => 'p',
      'B' => 
      array (
        0 => '19',
        1 => '-205',
        2 => '524',
        3 => '473',
      ),
    ),
    'p' => 
    array (
      'C' => '112',
      'WX' => '556',
      'N' => 'p',
      'B' => 
      array (
        0 => '19',
        1 => '-205',
        2 => '524',
        3 => '473',
      ),
    ),
    113 => 
    array (
      'C' => '113',
      'WX' => '556',
      'N' => 'q',
      'B' => 
      array (
        0 => '34',
        1 => '-205',
        2 => '536',
        3 => '473',
      ),
    ),
    'q' => 
    array (
      'C' => '113',
      'WX' => '556',
      'N' => 'q',
      'B' => 
      array (
        0 => '34',
        1 => '-205',
        2 => '536',
        3 => '473',
      ),
    ),
    114 => 
    array (
      'C' => '114',
      'WX' => '444',
      'N' => 'r',
      'B' => 
      array (
        0 => '29',
        1 => '0',
        2 => '434',
        3 => '473',
      ),
    ),
    'r' => 
    array (
      'C' => '114',
      'WX' => '444',
      'N' => 'r',
      'B' => 
      array (
        0 => '29',
        1 => '0',
        2 => '434',
        3 => '473',
      ),
    ),
    115 => 
    array (
      'C' => '115',
      'WX' => '389',
      'N' => 's',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '361',
        3 => '473',
      ),
    ),
    's' => 
    array (
      'C' => '115',
      'WX' => '389',
      'N' => 's',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '361',
        3 => '473',
      ),
    ),
    116 => 
    array (
      'C' => '116',
      'WX' => '333',
      'N' => 't',
      'B' => 
      array (
        0 => '20',
        1 => '-12',
        2 => '332',
        3 => '630',
      ),
    ),
    't' => 
    array (
      'C' => '116',
      'WX' => '333',
      'N' => 't',
      'B' => 
      array (
        0 => '20',
        1 => '-12',
        2 => '332',
        3 => '630',
      ),
    ),
    117 => 
    array (
      'C' => '117',
      'WX' => '556',
      'N' => 'u',
      'B' => 
      array (
        0 => '16',
        1 => '-14',
        2 => '537',
        3 => '461',
      ),
    ),
    'u' => 
    array (
      'C' => '117',
      'WX' => '556',
      'N' => 'u',
      'B' => 
      array (
        0 => '16',
        1 => '-14',
        2 => '537',
        3 => '461',
      ),
    ),
    118 => 
    array (
      'C' => '118',
      'WX' => '500',
      'N' => 'v',
      'B' => 
      array (
        0 => '21',
        1 => '-14',
        2 => '485',
        3 => '461',
      ),
    ),
    'v' => 
    array (
      'C' => '118',
      'WX' => '500',
      'N' => 'v',
      'B' => 
      array (
        0 => '21',
        1 => '-14',
        2 => '485',
        3 => '461',
      ),
    ),
    119 => 
    array (
      'C' => '119',
      'WX' => '722',
      'N' => 'w',
      'B' => 
      array (
        0 => '23',
        1 => '-14',
        2 => '707',
        3 => '461',
      ),
    ),
    'w' => 
    array (
      'C' => '119',
      'WX' => '722',
      'N' => 'w',
      'B' => 
      array (
        0 => '23',
        1 => '-14',
        2 => '707',
        3 => '461',
      ),
    ),
    120 => 
    array (
      'C' => '120',
      'WX' => '500',
      'N' => 'x',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '484',
        3 => '461',
      ),
    ),
    'x' => 
    array (
      'C' => '120',
      'WX' => '500',
      'N' => 'x',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '484',
        3 => '461',
      ),
    ),
    121 => 
    array (
      'C' => '121',
      'WX' => '500',
      'N' => 'y',
      'B' => 
      array (
        0 => '16',
        1 => '-205',
        2 => '480',
        3 => '461',
      ),
    ),
    'y' => 
    array (
      'C' => '121',
      'WX' => '500',
      'N' => 'y',
      'B' => 
      array (
        0 => '16',
        1 => '-205',
        2 => '480',
        3 => '461',
      ),
    ),
    122 => 
    array (
      'C' => '122',
      'WX' => '444',
      'N' => 'z',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '420',
        3 => '461',
      ),
    ),
    'z' => 
    array (
      'C' => '122',
      'WX' => '444',
      'N' => 'z',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '420',
        3 => '461',
      ),
    ),
    123 => 
    array (
      'C' => '123',
      'WX' => '394',
      'N' => 'braceleft',
      'B' => 
      array (
        0 => '22',
        1 => '-175',
        2 => '340',
        3 => '698',
      ),
    ),
    'braceleft' => 
    array (
      'C' => '123',
      'WX' => '394',
      'N' => 'braceleft',
      'B' => 
      array (
        0 => '22',
        1 => '-175',
        2 => '340',
        3 => '698',
      ),
    ),
    124 => 
    array (
      'C' => '124',
      'WX' => '220',
      'N' => 'bar',
      'B' => 
      array (
        0 => '66',
        1 => '-218',
        2 => '154',
        3 => '782',
      ),
    ),
    'bar' => 
    array (
      'C' => '124',
      'WX' => '220',
      'N' => 'bar',
      'B' => 
      array (
        0 => '66',
        1 => '-218',
        2 => '154',
        3 => '782',
      ),
    ),
    125 => 
    array (
      'C' => '125',
      'WX' => '394',
      'N' => 'braceright',
      'B' => 
      array (
        0 => '54',
        1 => '-175',
        2 => '372',
        3 => '698',
      ),
    ),
    'braceright' => 
    array (
      'C' => '125',
      'WX' => '394',
      'N' => 'braceright',
      'B' => 
      array (
        0 => '54',
        1 => '-175',
        2 => '372',
        3 => '698',
      ),
    ),
    126 => 
    array (
      'C' => '126',
      'WX' => '520',
      'N' => 'asciitilde',
      'B' => 
      array (
        0 => '29',
        1 => '173',
        2 => '491',
        3 => '333',
      ),
    ),
    'asciitilde' => 
    array (
      'C' => '126',
      'WX' => '520',
      'N' => 'asciitilde',
      'B' => 
      array (
        0 => '29',
        1 => '173',
        2 => '491',
        3 => '333',
      ),
    ),
    161 => 
    array (
      'C' => '161',
      'WX' => '333',
      'N' => 'exclamdown',
      'B' => 
      array (
        0 => '82',
        1 => '-203',
        2 => '252',
        3 => '501',
      ),
    ),
    'exclamdown' => 
    array (
      'C' => '161',
      'WX' => '333',
      'N' => 'exclamdown',
      'B' => 
      array (
        0 => '82',
        1 => '-203',
        2 => '252',
        3 => '501',
      ),
    ),
    162 => 
    array (
      'C' => '162',
      'WX' => '500',
      'N' => 'cent',
      'B' => 
      array (
        0 => '53',
        1 => '-140',
        2 => '458',
        3 => '588',
      ),
    ),
    'cent' => 
    array (
      'C' => '162',
      'WX' => '500',
      'N' => 'cent',
      'B' => 
      array (
        0 => '53',
        1 => '-140',
        2 => '458',
        3 => '588',
      ),
    ),
    163 => 
    array (
      'C' => '163',
      'WX' => '500',
      'N' => 'sterling',
      'B' => 
      array (
        0 => '21',
        1 => '-14',
        2 => '477',
        3 => '684',
      ),
    ),
    'sterling' => 
    array (
      'C' => '163',
      'WX' => '500',
      'N' => 'sterling',
      'B' => 
      array (
        0 => '21',
        1 => '-14',
        2 => '477',
        3 => '684',
      ),
    ),
    164 => 
    array (
      'C' => '164',
      'WX' => '167',
      'N' => 'fraction',
      'B' => 
      array (
        0 => '-168',
        1 => '-12',
        2 => '329',
        3 => '688',
      ),
    ),
    'fraction' => 
    array (
      'C' => '164',
      'WX' => '167',
      'N' => 'fraction',
      'B' => 
      array (
        0 => '-168',
        1 => '-12',
        2 => '329',
        3 => '688',
      ),
    ),
    165 => 
    array (
      'C' => '165',
      'WX' => '500',
      'N' => 'yen',
      'B' => 
      array (
        0 => '-64',
        1 => '0',
        2 => '547',
        3 => '676',
      ),
    ),
    'yen' => 
    array (
      'C' => '165',
      'WX' => '500',
      'N' => 'yen',
      'B' => 
      array (
        0 => '-64',
        1 => '0',
        2 => '547',
        3 => '676',
      ),
    ),
    166 => 
    array (
      'C' => '166',
      'WX' => '500',
      'N' => 'florin',
      'B' => 
      array (
        0 => '0',
        1 => '-155',
        2 => '498',
        3 => '706',
      ),
    ),
    'florin' => 
    array (
      'C' => '166',
      'WX' => '500',
      'N' => 'florin',
      'B' => 
      array (
        0 => '0',
        1 => '-155',
        2 => '498',
        3 => '706',
      ),
    ),
    167 => 
    array (
      'C' => '167',
      'WX' => '500',
      'N' => 'section',
      'B' => 
      array (
        0 => '57',
        1 => '-132',
        2 => '443',
        3 => '691',
      ),
    ),
    'section' => 
    array (
      'C' => '167',
      'WX' => '500',
      'N' => 'section',
      'B' => 
      array (
        0 => '57',
        1 => '-132',
        2 => '443',
        3 => '691',
      ),
    ),
    168 => 
    array (
      'C' => '168',
      'WX' => '500',
      'N' => 'currency',
      'B' => 
      array (
        0 => '-26',
        1 => '61',
        2 => '526',
        3 => '613',
      ),
    ),
    'currency' => 
    array (
      'C' => '168',
      'WX' => '500',
      'N' => 'currency',
      'B' => 
      array (
        0 => '-26',
        1 => '61',
        2 => '526',
        3 => '613',
      ),
    ),
    169 => 
    array (
      'C' => '169',
      'WX' => '278',
      'N' => 'quotesingle',
      'B' => 
      array (
        0 => '75',
        1 => '404',
        2 => '204',
        3 => '691',
      ),
    ),
    'quotesingle' => 
    array (
      'C' => '169',
      'WX' => '278',
      'N' => 'quotesingle',
      'B' => 
      array (
        0 => '75',
        1 => '404',
        2 => '204',
        3 => '691',
      ),
    ),
    170 => 
    array (
      'C' => '170',
      'WX' => '500',
      'N' => 'quotedblleft',
      'B' => 
      array (
        0 => '32',
        1 => '356',
        2 => '486',
        3 => '691',
      ),
    ),
    'quotedblleft' => 
    array (
      'C' => '170',
      'WX' => '500',
      'N' => 'quotedblleft',
      'B' => 
      array (
        0 => '32',
        1 => '356',
        2 => '486',
        3 => '691',
      ),
    ),
    171 => 
    array (
      'C' => '171',
      'WX' => '500',
      'N' => 'guillemotleft',
      'B' => 
      array (
        0 => '23',
        1 => '36',
        2 => '473',
        3 => '415',
      ),
    ),
    'guillemotleft' => 
    array (
      'C' => '171',
      'WX' => '500',
      'N' => 'guillemotleft',
      'B' => 
      array (
        0 => '23',
        1 => '36',
        2 => '473',
        3 => '415',
      ),
    ),
    172 => 
    array (
      'C' => '172',
      'WX' => '333',
      'N' => 'guilsinglleft',
      'B' => 
      array (
        0 => '51',
        1 => '36',
        2 => '305',
        3 => '415',
      ),
    ),
    'guilsinglleft' => 
    array (
      'C' => '172',
      'WX' => '333',
      'N' => 'guilsinglleft',
      'B' => 
      array (
        0 => '51',
        1 => '36',
        2 => '305',
        3 => '415',
      ),
    ),
    173 => 
    array (
      'C' => '173',
      'WX' => '333',
      'N' => 'guilsinglright',
      'B' => 
      array (
        0 => '28',
        1 => '36',
        2 => '282',
        3 => '415',
      ),
    ),
    'guilsinglright' => 
    array (
      'C' => '173',
      'WX' => '333',
      'N' => 'guilsinglright',
      'B' => 
      array (
        0 => '28',
        1 => '36',
        2 => '282',
        3 => '415',
      ),
    ),
    174 => 
    array (
      'C' => '174',
      'WX' => '556',
      'N' => 'fi',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '536',
        3 => '691',
      ),
    ),
    'fi' => 
    array (
      'C' => '174',
      'WX' => '556',
      'N' => 'fi',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '536',
        3 => '691',
      ),
    ),
    175 => 
    array (
      'C' => '175',
      'WX' => '556',
      'N' => 'fl',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '536',
        3 => '691',
      ),
    ),
    'fl' => 
    array (
      'C' => '175',
      'WX' => '556',
      'N' => 'fl',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '536',
        3 => '691',
      ),
    ),
    177 => 
    array (
      'C' => '177',
      'WX' => '500',
      'N' => 'endash',
      'B' => 
      array (
        0 => '0',
        1 => '181',
        2 => '500',
        3 => '271',
      ),
    ),
    'endash' => 
    array (
      'C' => '177',
      'WX' => '500',
      'N' => 'endash',
      'B' => 
      array (
        0 => '0',
        1 => '181',
        2 => '500',
        3 => '271',
      ),
    ),
    178 => 
    array (
      'C' => '178',
      'WX' => '500',
      'N' => 'dagger',
      'B' => 
      array (
        0 => '47',
        1 => '-134',
        2 => '453',
        3 => '691',
      ),
    ),
    'dagger' => 
    array (
      'C' => '178',
      'WX' => '500',
      'N' => 'dagger',
      'B' => 
      array (
        0 => '47',
        1 => '-134',
        2 => '453',
        3 => '691',
      ),
    ),
    179 => 
    array (
      'C' => '179',
      'WX' => '500',
      'N' => 'daggerdbl',
      'B' => 
      array (
        0 => '45',
        1 => '-132',
        2 => '456',
        3 => '691',
      ),
    ),
    'daggerdbl' => 
    array (
      'C' => '179',
      'WX' => '500',
      'N' => 'daggerdbl',
      'B' => 
      array (
        0 => '45',
        1 => '-132',
        2 => '456',
        3 => '691',
      ),
    ),
    180 => 
    array (
      'C' => '180',
      'WX' => '250',
      'N' => 'periodcentered',
      'B' => 
      array (
        0 => '41',
        1 => '248',
        2 => '210',
        3 => '417',
      ),
    ),
    'periodcentered' => 
    array (
      'C' => '180',
      'WX' => '250',
      'N' => 'periodcentered',
      'B' => 
      array (
        0 => '41',
        1 => '248',
        2 => '210',
        3 => '417',
      ),
    ),
    182 => 
    array (
      'C' => '182',
      'WX' => '540',
      'N' => 'paragraph',
      'B' => 
      array (
        0 => '0',
        1 => '-186',
        2 => '519',
        3 => '676',
      ),
    ),
    'paragraph' => 
    array (
      'C' => '182',
      'WX' => '540',
      'N' => 'paragraph',
      'B' => 
      array (
        0 => '0',
        1 => '-186',
        2 => '519',
        3 => '676',
      ),
    ),
    183 => 
    array (
      'C' => '183',
      'WX' => '350',
      'N' => 'bullet',
      'B' => 
      array (
        0 => '35',
        1 => '198',
        2 => '315',
        3 => '478',
      ),
    ),
    'bullet' => 
    array (
      'C' => '183',
      'WX' => '350',
      'N' => 'bullet',
      'B' => 
      array (
        0 => '35',
        1 => '198',
        2 => '315',
        3 => '478',
      ),
    ),
    184 => 
    array (
      'C' => '184',
      'WX' => '333',
      'N' => 'quotesinglbase',
      'B' => 
      array (
        0 => '79',
        1 => '-180',
        2 => '263',
        3 => '155',
      ),
    ),
    'quotesinglbase' => 
    array (
      'C' => '184',
      'WX' => '333',
      'N' => 'quotesinglbase',
      'B' => 
      array (
        0 => '79',
        1 => '-180',
        2 => '263',
        3 => '155',
      ),
    ),
    185 => 
    array (
      'C' => '185',
      'WX' => '500',
      'N' => 'quotedblbase',
      'B' => 
      array (
        0 => '14',
        1 => '-180',
        2 => '468',
        3 => '155',
      ),
    ),
    'quotedblbase' => 
    array (
      'C' => '185',
      'WX' => '500',
      'N' => 'quotedblbase',
      'B' => 
      array (
        0 => '14',
        1 => '-180',
        2 => '468',
        3 => '155',
      ),
    ),
    186 => 
    array (
      'C' => '186',
      'WX' => '500',
      'N' => 'quotedblright',
      'B' => 
      array (
        0 => '14',
        1 => '356',
        2 => '468',
        3 => '691',
      ),
    ),
    'quotedblright' => 
    array (
      'C' => '186',
      'WX' => '500',
      'N' => 'quotedblright',
      'B' => 
      array (
        0 => '14',
        1 => '356',
        2 => '468',
        3 => '691',
      ),
    ),
    187 => 
    array (
      'C' => '187',
      'WX' => '500',
      'N' => 'guillemotright',
      'B' => 
      array (
        0 => '27',
        1 => '36',
        2 => '477',
        3 => '415',
      ),
    ),
    'guillemotright' => 
    array (
      'C' => '187',
      'WX' => '500',
      'N' => 'guillemotright',
      'B' => 
      array (
        0 => '27',
        1 => '36',
        2 => '477',
        3 => '415',
      ),
    ),
    188 => 
    array (
      'C' => '188',
      'WX' => '1000',
      'N' => 'ellipsis',
      'B' => 
      array (
        0 => '82',
        1 => '-13',
        2 => '917',
        3 => '156',
      ),
    ),
    'ellipsis' => 
    array (
      'C' => '188',
      'WX' => '1000',
      'N' => 'ellipsis',
      'B' => 
      array (
        0 => '82',
        1 => '-13',
        2 => '917',
        3 => '156',
      ),
    ),
    189 => 
    array (
      'C' => '189',
      'WX' => '1000',
      'N' => 'perthousand',
      'B' => 
      array (
        0 => '7',
        1 => '-29',
        2 => '995',
        3 => '706',
      ),
    ),
    'perthousand' => 
    array (
      'C' => '189',
      'WX' => '1000',
      'N' => 'perthousand',
      'B' => 
      array (
        0 => '7',
        1 => '-29',
        2 => '995',
        3 => '706',
      ),
    ),
    191 => 
    array (
      'C' => '191',
      'WX' => '500',
      'N' => 'questiondown',
      'B' => 
      array (
        0 => '55',
        1 => '-201',
        2 => '443',
        3 => '501',
      ),
    ),
    'questiondown' => 
    array (
      'C' => '191',
      'WX' => '500',
      'N' => 'questiondown',
      'B' => 
      array (
        0 => '55',
        1 => '-201',
        2 => '443',
        3 => '501',
      ),
    ),
    193 => 
    array (
      'C' => '193',
      'WX' => '333',
      'N' => 'grave',
      'B' => 
      array (
        0 => '8',
        1 => '528',
        2 => '246',
        3 => '713',
      ),
    ),
    'grave' => 
    array (
      'C' => '193',
      'WX' => '333',
      'N' => 'grave',
      'B' => 
      array (
        0 => '8',
        1 => '528',
        2 => '246',
        3 => '713',
      ),
    ),
    194 => 
    array (
      'C' => '194',
      'WX' => '333',
      'N' => 'acute',
      'B' => 
      array (
        0 => '86',
        1 => '528',
        2 => '324',
        3 => '713',
      ),
    ),
    'acute' => 
    array (
      'C' => '194',
      'WX' => '333',
      'N' => 'acute',
      'B' => 
      array (
        0 => '86',
        1 => '528',
        2 => '324',
        3 => '713',
      ),
    ),
    195 => 
    array (
      'C' => '195',
      'WX' => '333',
      'N' => 'circumflex',
      'B' => 
      array (
        0 => '-2',
        1 => '528',
        2 => '335',
        3 => '704',
      ),
    ),
    'circumflex' => 
    array (
      'C' => '195',
      'WX' => '333',
      'N' => 'circumflex',
      'B' => 
      array (
        0 => '-2',
        1 => '528',
        2 => '335',
        3 => '704',
      ),
    ),
    196 => 
    array (
      'C' => '196',
      'WX' => '333',
      'N' => 'tilde',
      'B' => 
      array (
        0 => '-16',
        1 => '547',
        2 => '349',
        3 => '674',
      ),
    ),
    'tilde' => 
    array (
      'C' => '196',
      'WX' => '333',
      'N' => 'tilde',
      'B' => 
      array (
        0 => '-16',
        1 => '547',
        2 => '349',
        3 => '674',
      ),
    ),
    197 => 
    array (
      'C' => '197',
      'WX' => '333',
      'N' => 'macron',
      'B' => 
      array (
        0 => '1',
        1 => '565',
        2 => '331',
        3 => '637',
      ),
    ),
    'macron' => 
    array (
      'C' => '197',
      'WX' => '333',
      'N' => 'macron',
      'B' => 
      array (
        0 => '1',
        1 => '565',
        2 => '331',
        3 => '637',
      ),
    ),
    198 => 
    array (
      'C' => '198',
      'WX' => '333',
      'N' => 'breve',
      'B' => 
      array (
        0 => '15',
        1 => '528',
        2 => '318',
        3 => '691',
      ),
    ),
    'breve' => 
    array (
      'C' => '198',
      'WX' => '333',
      'N' => 'breve',
      'B' => 
      array (
        0 => '15',
        1 => '528',
        2 => '318',
        3 => '691',
      ),
    ),
    199 => 
    array (
      'C' => '199',
      'WX' => '333',
      'N' => 'dotaccent',
      'B' => 
      array (
        0 => '103',
        1 => '536',
        2 => '258',
        3 => '691',
      ),
    ),
    'dotaccent' => 
    array (
      'C' => '199',
      'WX' => '333',
      'N' => 'dotaccent',
      'B' => 
      array (
        0 => '103',
        1 => '536',
        2 => '258',
        3 => '691',
      ),
    ),
    200 => 
    array (
      'C' => '200',
      'WX' => '333',
      'N' => 'dieresis',
      'B' => 
      array (
        0 => '-2',
        1 => '537',
        2 => '335',
        3 => '667',
      ),
    ),
    'dieresis' => 
    array (
      'C' => '200',
      'WX' => '333',
      'N' => 'dieresis',
      'B' => 
      array (
        0 => '-2',
        1 => '537',
        2 => '335',
        3 => '667',
      ),
    ),
    202 => 
    array (
      'C' => '202',
      'WX' => '333',
      'N' => 'ring',
      'B' => 
      array (
        0 => '60',
        1 => '527',
        2 => '273',
        3 => '740',
      ),
    ),
    'ring' => 
    array (
      'C' => '202',
      'WX' => '333',
      'N' => 'ring',
      'B' => 
      array (
        0 => '60',
        1 => '527',
        2 => '273',
        3 => '740',
      ),
    ),
    203 => 
    array (
      'C' => '203',
      'WX' => '333',
      'N' => 'cedilla',
      'B' => 
      array (
        0 => '68',
        1 => '-218',
        2 => '294',
        3 => '0',
      ),
    ),
    'cedilla' => 
    array (
      'C' => '203',
      'WX' => '333',
      'N' => 'cedilla',
      'B' => 
      array (
        0 => '68',
        1 => '-218',
        2 => '294',
        3 => '0',
      ),
    ),
    205 => 
    array (
      'C' => '205',
      'WX' => '333',
      'N' => 'hungarumlaut',
      'B' => 
      array (
        0 => '-13',
        1 => '528',
        2 => '425',
        3 => '713',
      ),
    ),
    'hungarumlaut' => 
    array (
      'C' => '205',
      'WX' => '333',
      'N' => 'hungarumlaut',
      'B' => 
      array (
        0 => '-13',
        1 => '528',
        2 => '425',
        3 => '713',
      ),
    ),
    206 => 
    array (
      'C' => '206',
      'WX' => '333',
      'N' => 'ogonek',
      'B' => 
      array (
        0 => '90',
        1 => '-193',
        2 => '319',
        3 => '24',
      ),
    ),
    'ogonek' => 
    array (
      'C' => '206',
      'WX' => '333',
      'N' => 'ogonek',
      'B' => 
      array (
        0 => '90',
        1 => '-193',
        2 => '319',
        3 => '24',
      ),
    ),
    207 => 
    array (
      'C' => '207',
      'WX' => '333',
      'N' => 'caron',
      'B' => 
      array (
        0 => '-2',
        1 => '528',
        2 => '335',
        3 => '704',
      ),
    ),
    'caron' => 
    array (
      'C' => '207',
      'WX' => '333',
      'N' => 'caron',
      'B' => 
      array (
        0 => '-2',
        1 => '528',
        2 => '335',
        3 => '704',
      ),
    ),
    208 => 
    array (
      'C' => '208',
      'WX' => '1000',
      'N' => 'emdash',
      'B' => 
      array (
        0 => '0',
        1 => '181',
        2 => '1000',
        3 => '271',
      ),
    ),
    'emdash' => 
    array (
      'C' => '208',
      'WX' => '1000',
      'N' => 'emdash',
      'B' => 
      array (
        0 => '0',
        1 => '181',
        2 => '1000',
        3 => '271',
      ),
    ),
    225 => 
    array (
      'C' => '225',
      'WX' => '1000',
      'N' => 'AE',
      'B' => 
      array (
        0 => '4',
        1 => '0',
        2 => '951',
        3 => '676',
      ),
    ),
    'AE' => 
    array (
      'C' => '225',
      'WX' => '1000',
      'N' => 'AE',
      'B' => 
      array (
        0 => '4',
        1 => '0',
        2 => '951',
        3 => '676',
      ),
    ),
    227 => 
    array (
      'C' => '227',
      'WX' => '300',
      'N' => 'ordfeminine',
      'B' => 
      array (
        0 => '-1',
        1 => '397',
        2 => '301',
        3 => '688',
      ),
    ),
    'ordfeminine' => 
    array (
      'C' => '227',
      'WX' => '300',
      'N' => 'ordfeminine',
      'B' => 
      array (
        0 => '-1',
        1 => '397',
        2 => '301',
        3 => '688',
      ),
    ),
    232 => 
    array (
      'C' => '232',
      'WX' => '667',
      'N' => 'Lslash',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '638',
        3 => '676',
      ),
    ),
    'Lslash' => 
    array (
      'C' => '232',
      'WX' => '667',
      'N' => 'Lslash',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '638',
        3 => '676',
      ),
    ),
    233 => 
    array (
      'C' => '233',
      'WX' => '778',
      'N' => 'Oslash',
      'B' => 
      array (
        0 => '35',
        1 => '-74',
        2 => '743',
        3 => '737',
      ),
    ),
    'Oslash' => 
    array (
      'C' => '233',
      'WX' => '778',
      'N' => 'Oslash',
      'B' => 
      array (
        0 => '35',
        1 => '-74',
        2 => '743',
        3 => '737',
      ),
    ),
    234 => 
    array (
      'C' => '234',
      'WX' => '1000',
      'N' => 'OE',
      'B' => 
      array (
        0 => '22',
        1 => '-5',
        2 => '981',
        3 => '684',
      ),
    ),
    'OE' => 
    array (
      'C' => '234',
      'WX' => '1000',
      'N' => 'OE',
      'B' => 
      array (
        0 => '22',
        1 => '-5',
        2 => '981',
        3 => '684',
      ),
    ),
    235 => 
    array (
      'C' => '235',
      'WX' => '330',
      'N' => 'ordmasculine',
      'B' => 
      array (
        0 => '18',
        1 => '397',
        2 => '312',
        3 => '688',
      ),
    ),
    'ordmasculine' => 
    array (
      'C' => '235',
      'WX' => '330',
      'N' => 'ordmasculine',
      'B' => 
      array (
        0 => '18',
        1 => '397',
        2 => '312',
        3 => '688',
      ),
    ),
    241 => 
    array (
      'C' => '241',
      'WX' => '722',
      'N' => 'ae',
      'B' => 
      array (
        0 => '33',
        1 => '-14',
        2 => '693',
        3 => '473',
      ),
    ),
    'ae' => 
    array (
      'C' => '241',
      'WX' => '722',
      'N' => 'ae',
      'B' => 
      array (
        0 => '33',
        1 => '-14',
        2 => '693',
        3 => '473',
      ),
    ),
    245 => 
    array (
      'C' => '245',
      'WX' => '278',
      'N' => 'dotlessi',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '255',
        3 => '461',
      ),
    ),
    'dotlessi' => 
    array (
      'C' => '245',
      'WX' => '278',
      'N' => 'dotlessi',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '255',
        3 => '461',
      ),
    ),
    248 => 
    array (
      'C' => '248',
      'WX' => '278',
      'N' => 'lslash',
      'B' => 
      array (
        0 => '-22',
        1 => '0',
        2 => '303',
        3 => '676',
      ),
    ),
    'lslash' => 
    array (
      'C' => '248',
      'WX' => '278',
      'N' => 'lslash',
      'B' => 
      array (
        0 => '-22',
        1 => '0',
        2 => '303',
        3 => '676',
      ),
    ),
    249 => 
    array (
      'C' => '249',
      'WX' => '500',
      'N' => 'oslash',
      'B' => 
      array (
        0 => '25',
        1 => '-92',
        2 => '476',
        3 => '549',
      ),
    ),
    'oslash' => 
    array (
      'C' => '249',
      'WX' => '500',
      'N' => 'oslash',
      'B' => 
      array (
        0 => '25',
        1 => '-92',
        2 => '476',
        3 => '549',
      ),
    ),
    250 => 
    array (
      'C' => '250',
      'WX' => '722',
      'N' => 'oe',
      'B' => 
      array (
        0 => '22',
        1 => '-14',
        2 => '696',
        3 => '473',
      ),
    ),
    'oe' => 
    array (
      'C' => '250',
      'WX' => '722',
      'N' => 'oe',
      'B' => 
      array (
        0 => '22',
        1 => '-14',
        2 => '696',
        3 => '473',
      ),
    ),
    251 => 
    array (
      'C' => '251',
      'WX' => '556',
      'N' => 'germandbls',
      'B' => 
      array (
        0 => '19',
        1 => '-12',
        2 => '517',
        3 => '691',
      ),
    ),
    'germandbls' => 
    array (
      'C' => '251',
      'WX' => '556',
      'N' => 'germandbls',
      'B' => 
      array (
        0 => '19',
        1 => '-12',
        2 => '517',
        3 => '691',
      ),
    ),
    'Idieresis' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Idieresis',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '370',
        3 => '877',
      ),
    ),
    'eacute' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'eacute',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '426',
        3 => '713',
      ),
    ),
    'abreve' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'abreve',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '488',
        3 => '691',
      ),
    ),
    'uhungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uhungarumlaut',
      'B' => 
      array (
        0 => '16',
        1 => '-14',
        2 => '557',
        3 => '713',
      ),
    ),
    'ecaron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ecaron',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '426',
        3 => '704',
      ),
    ),
    'Ydieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ydieresis',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '699',
        3 => '877',
      ),
    ),
    'divide' => 
    array (
      'C' => '-1',
      'WX' => '570',
      'N' => 'divide',
      'B' => 
      array (
        0 => '33',
        1 => '-31',
        2 => '537',
        3 => '537',
      ),
    ),
    'Yacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Yacute',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '699',
        3 => '923',
      ),
    ),
    'Acircumflex' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Acircumflex',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '689',
        3 => '914',
      ),
    ),
    'aacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'aacute',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '488',
        3 => '713',
      ),
    ),
    'Ucircumflex' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ucircumflex',
      'B' => 
      array (
        0 => '16',
        1 => '-19',
        2 => '701',
        3 => '914',
      ),
    ),
    'yacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'yacute',
      'B' => 
      array (
        0 => '16',
        1 => '-205',
        2 => '480',
        3 => '713',
      ),
    ),
    'scommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'scommaaccent',
      'B' => 
      array (
        0 => '25',
        1 => '-218',
        2 => '361',
        3 => '473',
      ),
    ),
    'ecircumflex' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ecircumflex',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '426',
        3 => '704',
      ),
    ),
    'Uring' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uring',
      'B' => 
      array (
        0 => '16',
        1 => '-19',
        2 => '701',
        3 => '935',
      ),
    ),
    'Udieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Udieresis',
      'B' => 
      array (
        0 => '16',
        1 => '-19',
        2 => '701',
        3 => '877',
      ),
    ),
    'aogonek' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'aogonek',
      'B' => 
      array (
        0 => '25',
        1 => '-193',
        2 => '504',
        3 => '473',
      ),
    ),
    'Uacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uacute',
      'B' => 
      array (
        0 => '16',
        1 => '-19',
        2 => '701',
        3 => '923',
      ),
    ),
    'uogonek' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uogonek',
      'B' => 
      array (
        0 => '16',
        1 => '-193',
        2 => '539',
        3 => '461',
      ),
    ),
    'Edieresis' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Edieresis',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '641',
        3 => '877',
      ),
    ),
    'Dcroat' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Dcroat',
      'B' => 
      array (
        0 => '6',
        1 => '0',
        2 => '690',
        3 => '676',
      ),
    ),
    'commaaccent' => 
    array (
      'C' => '-1',
      'WX' => '250',
      'N' => 'commaaccent',
      'B' => 
      array (
        0 => '47',
        1 => '-218',
        2 => '203',
        3 => '-50',
      ),
    ),
    'copyright' => 
    array (
      'C' => '-1',
      'WX' => '747',
      'N' => 'copyright',
      'B' => 
      array (
        0 => '26',
        1 => '-19',
        2 => '721',
        3 => '691',
      ),
    ),
    'Emacron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Emacron',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '641',
        3 => '847',
      ),
    ),
    'ccaron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ccaron',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '430',
        3 => '704',
      ),
    ),
    'aring' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'aring',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '488',
        3 => '740',
      ),
    ),
    'Ncommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ncommaaccent',
      'B' => 
      array (
        0 => '16',
        1 => '-188',
        2 => '701',
        3 => '676',
      ),
    ),
    'lacute' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'lacute',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '297',
        3 => '923',
      ),
    ),
    'agrave' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'agrave',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '488',
        3 => '713',
      ),
    ),
    'Tcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Tcommaaccent',
      'B' => 
      array (
        0 => '31',
        1 => '-218',
        2 => '636',
        3 => '676',
      ),
    ),
    'Cacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Cacute',
      'B' => 
      array (
        0 => '49',
        1 => '-19',
        2 => '687',
        3 => '923',
      ),
    ),
    'atilde' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'atilde',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '488',
        3 => '674',
      ),
    ),
    'Edotaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Edotaccent',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '641',
        3 => '901',
      ),
    ),
    'scaron' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'scaron',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '363',
        3 => '704',
      ),
    ),
    'scedilla' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'scedilla',
      'B' => 
      array (
        0 => '25',
        1 => '-218',
        2 => '361',
        3 => '473',
      ),
    ),
    'iacute' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'iacute',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '289',
        3 => '713',
      ),
    ),
    'lozenge' => 
    array (
      'C' => '-1',
      'WX' => '494',
      'N' => 'lozenge',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '484',
        3 => '745',
      ),
    ),
    'Rcaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Rcaron',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '715',
        3 => '914',
      ),
    ),
    'Gcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Gcommaaccent',
      'B' => 
      array (
        0 => '37',
        1 => '-218',
        2 => '755',
        3 => '691',
      ),
    ),
    'ucircumflex' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ucircumflex',
      'B' => 
      array (
        0 => '16',
        1 => '-14',
        2 => '537',
        3 => '704',
      ),
    ),
    'acircumflex' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'acircumflex',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '488',
        3 => '704',
      ),
    ),
    'Amacron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Amacron',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '689',
        3 => '847',
      ),
    ),
    'rcaron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'rcaron',
      'B' => 
      array (
        0 => '29',
        1 => '0',
        2 => '434',
        3 => '704',
      ),
    ),
    'ccedilla' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ccedilla',
      'B' => 
      array (
        0 => '25',
        1 => '-218',
        2 => '430',
        3 => '473',
      ),
    ),
    'Zdotaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Zdotaccent',
      'B' => 
      array (
        0 => '28',
        1 => '0',
        2 => '634',
        3 => '901',
      ),
    ),
    'Thorn' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Thorn',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '600',
        3 => '676',
      ),
    ),
    'Omacron' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Omacron',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '743',
        3 => '847',
      ),
    ),
    'Racute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Racute',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '715',
        3 => '923',
      ),
    ),
    'Sacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Sacute',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '513',
        3 => '923',
      ),
    ),
    'dcaron' => 
    array (
      'C' => '-1',
      'WX' => '672',
      'N' => 'dcaron',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '681',
        3 => '682',
      ),
    ),
    'Umacron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Umacron',
      'B' => 
      array (
        0 => '16',
        1 => '-19',
        2 => '701',
        3 => '847',
      ),
    ),
    'uring' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uring',
      'B' => 
      array (
        0 => '16',
        1 => '-14',
        2 => '537',
        3 => '740',
      ),
    ),
    'threesuperior' => 
    array (
      'C' => '-1',
      'WX' => '300',
      'N' => 'threesuperior',
      'B' => 
      array (
        0 => '3',
        1 => '268',
        2 => '297',
        3 => '688',
      ),
    ),
    'Ograve' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Ograve',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '743',
        3 => '923',
      ),
    ),
    'Agrave' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Agrave',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '689',
        3 => '923',
      ),
    ),
    'Abreve' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Abreve',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '689',
        3 => '901',
      ),
    ),
    'multiply' => 
    array (
      'C' => '-1',
      'WX' => '570',
      'N' => 'multiply',
      'B' => 
      array (
        0 => '48',
        1 => '16',
        2 => '522',
        3 => '490',
      ),
    ),
    'uacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uacute',
      'B' => 
      array (
        0 => '16',
        1 => '-14',
        2 => '537',
        3 => '713',
      ),
    ),
    'Tcaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Tcaron',
      'B' => 
      array (
        0 => '31',
        1 => '0',
        2 => '636',
        3 => '914',
      ),
    ),
    'partialdiff' => 
    array (
      'C' => '-1',
      'WX' => '494',
      'N' => 'partialdiff',
      'B' => 
      array (
        0 => '11',
        1 => '-21',
        2 => '494',
        3 => '750',
      ),
    ),
    'ydieresis' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ydieresis',
      'B' => 
      array (
        0 => '16',
        1 => '-205',
        2 => '480',
        3 => '667',
      ),
    ),
    'Nacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Nacute',
      'B' => 
      array (
        0 => '16',
        1 => '-18',
        2 => '701',
        3 => '923',
      ),
    ),
    'icircumflex' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'icircumflex',
      'B' => 
      array (
        0 => '-37',
        1 => '0',
        2 => '300',
        3 => '704',
      ),
    ),
    'Ecircumflex' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ecircumflex',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '641',
        3 => '914',
      ),
    ),
    'adieresis' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'adieresis',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '488',
        3 => '667',
      ),
    ),
    'edieresis' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'edieresis',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '426',
        3 => '667',
      ),
    ),
    'cacute' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'cacute',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '430',
        3 => '713',
      ),
    ),
    'nacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'nacute',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '539',
        3 => '713',
      ),
    ),
    'umacron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'umacron',
      'B' => 
      array (
        0 => '16',
        1 => '-14',
        2 => '537',
        3 => '637',
      ),
    ),
    'Ncaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ncaron',
      'B' => 
      array (
        0 => '16',
        1 => '-18',
        2 => '701',
        3 => '914',
      ),
    ),
    'Iacute' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Iacute',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '370',
        3 => '923',
      ),
    ),
    'plusminus' => 
    array (
      'C' => '-1',
      'WX' => '570',
      'N' => 'plusminus',
      'B' => 
      array (
        0 => '33',
        1 => '0',
        2 => '537',
        3 => '506',
      ),
    ),
    'brokenbar' => 
    array (
      'C' => '-1',
      'WX' => '220',
      'N' => 'brokenbar',
      'B' => 
      array (
        0 => '66',
        1 => '-143',
        2 => '154',
        3 => '707',
      ),
    ),
    'registered' => 
    array (
      'C' => '-1',
      'WX' => '747',
      'N' => 'registered',
      'B' => 
      array (
        0 => '26',
        1 => '-19',
        2 => '721',
        3 => '691',
      ),
    ),
    'Gbreve' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Gbreve',
      'B' => 
      array (
        0 => '37',
        1 => '-19',
        2 => '755',
        3 => '901',
      ),
    ),
    'Idotaccent' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Idotaccent',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '370',
        3 => '901',
      ),
    ),
    'summation' => 
    array (
      'C' => '-1',
      'WX' => '600',
      'N' => 'summation',
      'B' => 
      array (
        0 => '14',
        1 => '-10',
        2 => '585',
        3 => '706',
      ),
    ),
    'Egrave' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Egrave',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '641',
        3 => '923',
      ),
    ),
    'racute' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'racute',
      'B' => 
      array (
        0 => '29',
        1 => '0',
        2 => '434',
        3 => '713',
      ),
    ),
    'omacron' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'omacron',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '476',
        3 => '637',
      ),
    ),
    'Zacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Zacute',
      'B' => 
      array (
        0 => '28',
        1 => '0',
        2 => '634',
        3 => '923',
      ),
    ),
    'Zcaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Zcaron',
      'B' => 
      array (
        0 => '28',
        1 => '0',
        2 => '634',
        3 => '914',
      ),
    ),
    'greaterequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'greaterequal',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '523',
        3 => '704',
      ),
    ),
    'Eth' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Eth',
      'B' => 
      array (
        0 => '6',
        1 => '0',
        2 => '690',
        3 => '676',
      ),
    ),
    'Ccedilla' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ccedilla',
      'B' => 
      array (
        0 => '49',
        1 => '-218',
        2 => '687',
        3 => '691',
      ),
    ),
    'lcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'lcommaaccent',
      'B' => 
      array (
        0 => '16',
        1 => '-218',
        2 => '255',
        3 => '676',
      ),
    ),
    'tcaron' => 
    array (
      'C' => '-1',
      'WX' => '416',
      'N' => 'tcaron',
      'B' => 
      array (
        0 => '20',
        1 => '-12',
        2 => '425',
        3 => '815',
      ),
    ),
    'eogonek' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'eogonek',
      'B' => 
      array (
        0 => '25',
        1 => '-193',
        2 => '426',
        3 => '473',
      ),
    ),
    'Uogonek' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uogonek',
      'B' => 
      array (
        0 => '16',
        1 => '-193',
        2 => '701',
        3 => '676',
      ),
    ),
    'Aacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Aacute',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '689',
        3 => '923',
      ),
    ),
    'Adieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Adieresis',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '689',
        3 => '877',
      ),
    ),
    'egrave' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'egrave',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '426',
        3 => '713',
      ),
    ),
    'zacute' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'zacute',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '420',
        3 => '713',
      ),
    ),
    'iogonek' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'iogonek',
      'B' => 
      array (
        0 => '16',
        1 => '-193',
        2 => '274',
        3 => '691',
      ),
    ),
    'Oacute' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Oacute',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '743',
        3 => '923',
      ),
    ),
    'oacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'oacute',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '476',
        3 => '713',
      ),
    ),
    'amacron' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'amacron',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '488',
        3 => '637',
      ),
    ),
    'sacute' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'sacute',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '361',
        3 => '713',
      ),
    ),
    'idieresis' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'idieresis',
      'B' => 
      array (
        0 => '-37',
        1 => '0',
        2 => '300',
        3 => '667',
      ),
    ),
    'Ocircumflex' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Ocircumflex',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '743',
        3 => '914',
      ),
    ),
    'Ugrave' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ugrave',
      'B' => 
      array (
        0 => '16',
        1 => '-19',
        2 => '701',
        3 => '923',
      ),
    ),
    'Delta' => 
    array (
      'C' => '-1',
      'WX' => '612',
      'N' => 'Delta',
      'B' => 
      array (
        0 => '6',
        1 => '0',
        2 => '608',
        3 => '688',
      ),
    ),
    'thorn' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'thorn',
      'B' => 
      array (
        0 => '19',
        1 => '-205',
        2 => '524',
        3 => '676',
      ),
    ),
    'twosuperior' => 
    array (
      'C' => '-1',
      'WX' => '300',
      'N' => 'twosuperior',
      'B' => 
      array (
        0 => '0',
        1 => '275',
        2 => '300',
        3 => '688',
      ),
    ),
    'Odieresis' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Odieresis',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '743',
        3 => '877',
      ),
    ),
    'mu' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'mu',
      'B' => 
      array (
        0 => '33',
        1 => '-206',
        2 => '536',
        3 => '461',
      ),
    ),
    'igrave' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'igrave',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '255',
        3 => '713',
      ),
    ),
    'ohungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ohungarumlaut',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '529',
        3 => '713',
      ),
    ),
    'Eogonek' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Eogonek',
      'B' => 
      array (
        0 => '16',
        1 => '-193',
        2 => '644',
        3 => '676',
      ),
    ),
    'dcroat' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'dcroat',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '534',
        3 => '676',
      ),
    ),
    'threequarters' => 
    array (
      'C' => '-1',
      'WX' => '750',
      'N' => 'threequarters',
      'B' => 
      array (
        0 => '23',
        1 => '-12',
        2 => '733',
        3 => '688',
      ),
    ),
    'Scedilla' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Scedilla',
      'B' => 
      array (
        0 => '35',
        1 => '-218',
        2 => '513',
        3 => '692',
      ),
    ),
    'lcaron' => 
    array (
      'C' => '-1',
      'WX' => '394',
      'N' => 'lcaron',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '412',
        3 => '682',
      ),
    ),
    'Kcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Kcommaaccent',
      'B' => 
      array (
        0 => '30',
        1 => '-218',
        2 => '769',
        3 => '676',
      ),
    ),
    'Lacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Lacute',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '638',
        3 => '923',
      ),
    ),
    'trademark' => 
    array (
      'C' => '-1',
      'WX' => '1000',
      'N' => 'trademark',
      'B' => 
      array (
        0 => '24',
        1 => '271',
        2 => '977',
        3 => '676',
      ),
    ),
    'edotaccent' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'edotaccent',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '426',
        3 => '691',
      ),
    ),
    'Igrave' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Igrave',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '370',
        3 => '923',
      ),
    ),
    'Imacron' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Imacron',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '370',
        3 => '847',
      ),
    ),
    'Lcaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Lcaron',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '652',
        3 => '682',
      ),
    ),
    'onehalf' => 
    array (
      'C' => '-1',
      'WX' => '750',
      'N' => 'onehalf',
      'B' => 
      array (
        0 => '-7',
        1 => '-12',
        2 => '775',
        3 => '688',
      ),
    ),
    'lessequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'lessequal',
      'B' => 
      array (
        0 => '29',
        1 => '0',
        2 => '526',
        3 => '704',
      ),
    ),
    'ocircumflex' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ocircumflex',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '476',
        3 => '704',
      ),
    ),
    'ntilde' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ntilde',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '539',
        3 => '674',
      ),
    ),
    'Uhungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uhungarumlaut',
      'B' => 
      array (
        0 => '16',
        1 => '-19',
        2 => '701',
        3 => '923',
      ),
    ),
    'Eacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Eacute',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '641',
        3 => '923',
      ),
    ),
    'emacron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'emacron',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '426',
        3 => '637',
      ),
    ),
    'gbreve' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'gbreve',
      'B' => 
      array (
        0 => '28',
        1 => '-206',
        2 => '483',
        3 => '691',
      ),
    ),
    'onequarter' => 
    array (
      'C' => '-1',
      'WX' => '750',
      'N' => 'onequarter',
      'B' => 
      array (
        0 => '28',
        1 => '-12',
        2 => '743',
        3 => '688',
      ),
    ),
    'Scaron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Scaron',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '513',
        3 => '914',
      ),
    ),
    'Scommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Scommaaccent',
      'B' => 
      array (
        0 => '35',
        1 => '-218',
        2 => '513',
        3 => '692',
      ),
    ),
    'Ohungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Ohungarumlaut',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '743',
        3 => '923',
      ),
    ),
    'degree' => 
    array (
      'C' => '-1',
      'WX' => '400',
      'N' => 'degree',
      'B' => 
      array (
        0 => '57',
        1 => '402',
        2 => '343',
        3 => '688',
      ),
    ),
    'ograve' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ograve',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '476',
        3 => '713',
      ),
    ),
    'Ccaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ccaron',
      'B' => 
      array (
        0 => '49',
        1 => '-19',
        2 => '687',
        3 => '914',
      ),
    ),
    'ugrave' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ugrave',
      'B' => 
      array (
        0 => '16',
        1 => '-14',
        2 => '537',
        3 => '713',
      ),
    ),
    'radical' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'radical',
      'B' => 
      array (
        0 => '10',
        1 => '-46',
        2 => '512',
        3 => '850',
      ),
    ),
    'Dcaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Dcaron',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '690',
        3 => '914',
      ),
    ),
    'rcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'rcommaaccent',
      'B' => 
      array (
        0 => '29',
        1 => '-218',
        2 => '434',
        3 => '473',
      ),
    ),
    'Ntilde' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ntilde',
      'B' => 
      array (
        0 => '16',
        1 => '-18',
        2 => '701',
        3 => '884',
      ),
    ),
    'otilde' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'otilde',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '476',
        3 => '674',
      ),
    ),
    'Rcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Rcommaaccent',
      'B' => 
      array (
        0 => '26',
        1 => '-218',
        2 => '715',
        3 => '676',
      ),
    ),
    'Lcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Lcommaaccent',
      'B' => 
      array (
        0 => '19',
        1 => '-218',
        2 => '638',
        3 => '676',
      ),
    ),
    'Atilde' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Atilde',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '689',
        3 => '884',
      ),
    ),
    'Aogonek' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Aogonek',
      'B' => 
      array (
        0 => '9',
        1 => '-193',
        2 => '699',
        3 => '690',
      ),
    ),
    'Aring' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Aring',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '689',
        3 => '935',
      ),
    ),
    'Otilde' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Otilde',
      'B' => 
      array (
        0 => '35',
        1 => '-19',
        2 => '743',
        3 => '884',
      ),
    ),
    'zdotaccent' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'zdotaccent',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '420',
        3 => '691',
      ),
    ),
    'Ecaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ecaron',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '641',
        3 => '914',
      ),
    ),
    'Iogonek' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Iogonek',
      'B' => 
      array (
        0 => '20',
        1 => '-193',
        2 => '370',
        3 => '676',
      ),
    ),
    'kcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'kcommaaccent',
      'B' => 
      array (
        0 => '22',
        1 => '-218',
        2 => '543',
        3 => '676',
      ),
    ),
    'minus' => 
    array (
      'C' => '-1',
      'WX' => '570',
      'N' => 'minus',
      'B' => 
      array (
        0 => '33',
        1 => '209',
        2 => '537',
        3 => '297',
      ),
    ),
    'Icircumflex' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Icircumflex',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '370',
        3 => '914',
      ),
    ),
    'ncaron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ncaron',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '539',
        3 => '704',
      ),
    ),
    'tcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'tcommaaccent',
      'B' => 
      array (
        0 => '20',
        1 => '-218',
        2 => '332',
        3 => '630',
      ),
    ),
    'logicalnot' => 
    array (
      'C' => '-1',
      'WX' => '570',
      'N' => 'logicalnot',
      'B' => 
      array (
        0 => '33',
        1 => '108',
        2 => '537',
        3 => '399',
      ),
    ),
    'odieresis' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'odieresis',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '476',
        3 => '667',
      ),
    ),
    'udieresis' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'udieresis',
      'B' => 
      array (
        0 => '16',
        1 => '-14',
        2 => '537',
        3 => '667',
      ),
    ),
    'notequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'notequal',
      'B' => 
      array (
        0 => '15',
        1 => '-49',
        2 => '540',
        3 => '570',
      ),
    ),
    'gcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'gcommaaccent',
      'B' => 
      array (
        0 => '28',
        1 => '-206',
        2 => '483',
        3 => '829',
      ),
    ),
    'eth' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'eth',
      'B' => 
      array (
        0 => '25',
        1 => '-14',
        2 => '476',
        3 => '691',
      ),
    ),
    'zcaron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'zcaron',
      'B' => 
      array (
        0 => '21',
        1 => '0',
        2 => '420',
        3 => '704',
      ),
    ),
    'ncommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ncommaaccent',
      'B' => 
      array (
        0 => '21',
        1 => '-218',
        2 => '539',
        3 => '473',
      ),
    ),
    'onesuperior' => 
    array (
      'C' => '-1',
      'WX' => '300',
      'N' => 'onesuperior',
      'B' => 
      array (
        0 => '28',
        1 => '275',
        2 => '273',
        3 => '688',
      ),
    ),
    'imacron' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'imacron',
      'B' => 
      array (
        0 => '-8',
        1 => '0',
        2 => '272',
        3 => '637',
      ),
    ),
    'Euro' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'Euro',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
  ),
  'KPX' => 
  array (
    'A' => 
    array (
      'C' => '-55',
      'Cacute' => '-55',
      'Ccaron' => '-55',
      'Ccedilla' => '-55',
      'G' => '-55',
      'Gbreve' => '-55',
      'Gcommaaccent' => '-55',
      'O' => '-45',
      'Oacute' => '-45',
      'Ocircumflex' => '-45',
      'Odieresis' => '-45',
      'Ograve' => '-45',
      'Ohungarumlaut' => '-45',
      'Omacron' => '-45',
      'Oslash' => '-45',
      'Otilde' => '-45',
      'Q' => '-45',
      'T' => '-95',
      'Tcaron' => '-95',
      'Tcommaaccent' => '-95',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-145',
      'W' => '-130',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'p' => '-25',
      'quoteright' => '-74',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'v' => '-100',
      'w' => '-90',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Aacute' => 
    array (
      'C' => '-55',
      'Cacute' => '-55',
      'Ccaron' => '-55',
      'Ccedilla' => '-55',
      'G' => '-55',
      'Gbreve' => '-55',
      'Gcommaaccent' => '-55',
      'O' => '-45',
      'Oacute' => '-45',
      'Ocircumflex' => '-45',
      'Odieresis' => '-45',
      'Ograve' => '-45',
      'Ohungarumlaut' => '-45',
      'Omacron' => '-45',
      'Oslash' => '-45',
      'Otilde' => '-45',
      'Q' => '-45',
      'T' => '-95',
      'Tcaron' => '-95',
      'Tcommaaccent' => '-95',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-145',
      'W' => '-130',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'p' => '-25',
      'quoteright' => '-74',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'v' => '-100',
      'w' => '-90',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Abreve' => 
    array (
      'C' => '-55',
      'Cacute' => '-55',
      'Ccaron' => '-55',
      'Ccedilla' => '-55',
      'G' => '-55',
      'Gbreve' => '-55',
      'Gcommaaccent' => '-55',
      'O' => '-45',
      'Oacute' => '-45',
      'Ocircumflex' => '-45',
      'Odieresis' => '-45',
      'Ograve' => '-45',
      'Ohungarumlaut' => '-45',
      'Omacron' => '-45',
      'Oslash' => '-45',
      'Otilde' => '-45',
      'Q' => '-45',
      'T' => '-95',
      'Tcaron' => '-95',
      'Tcommaaccent' => '-95',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-145',
      'W' => '-130',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'p' => '-25',
      'quoteright' => '-74',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'v' => '-100',
      'w' => '-90',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Acircumflex' => 
    array (
      'C' => '-55',
      'Cacute' => '-55',
      'Ccaron' => '-55',
      'Ccedilla' => '-55',
      'G' => '-55',
      'Gbreve' => '-55',
      'Gcommaaccent' => '-55',
      'O' => '-45',
      'Oacute' => '-45',
      'Ocircumflex' => '-45',
      'Odieresis' => '-45',
      'Ograve' => '-45',
      'Ohungarumlaut' => '-45',
      'Omacron' => '-45',
      'Oslash' => '-45',
      'Otilde' => '-45',
      'Q' => '-45',
      'T' => '-95',
      'Tcaron' => '-95',
      'Tcommaaccent' => '-95',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-145',
      'W' => '-130',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'p' => '-25',
      'quoteright' => '-74',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'v' => '-100',
      'w' => '-90',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Adieresis' => 
    array (
      'C' => '-55',
      'Cacute' => '-55',
      'Ccaron' => '-55',
      'Ccedilla' => '-55',
      'G' => '-55',
      'Gbreve' => '-55',
      'Gcommaaccent' => '-55',
      'O' => '-45',
      'Oacute' => '-45',
      'Ocircumflex' => '-45',
      'Odieresis' => '-45',
      'Ograve' => '-45',
      'Ohungarumlaut' => '-45',
      'Omacron' => '-45',
      'Oslash' => '-45',
      'Otilde' => '-45',
      'Q' => '-45',
      'T' => '-95',
      'Tcaron' => '-95',
      'Tcommaaccent' => '-95',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-145',
      'W' => '-130',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'p' => '-25',
      'quoteright' => '-74',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'v' => '-100',
      'w' => '-90',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Agrave' => 
    array (
      'C' => '-55',
      'Cacute' => '-55',
      'Ccaron' => '-55',
      'Ccedilla' => '-55',
      'G' => '-55',
      'Gbreve' => '-55',
      'Gcommaaccent' => '-55',
      'O' => '-45',
      'Oacute' => '-45',
      'Ocircumflex' => '-45',
      'Odieresis' => '-45',
      'Ograve' => '-45',
      'Ohungarumlaut' => '-45',
      'Omacron' => '-45',
      'Oslash' => '-45',
      'Otilde' => '-45',
      'Q' => '-45',
      'T' => '-95',
      'Tcaron' => '-95',
      'Tcommaaccent' => '-95',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-145',
      'W' => '-130',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'p' => '-25',
      'quoteright' => '-74',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'v' => '-100',
      'w' => '-90',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Amacron' => 
    array (
      'C' => '-55',
      'Cacute' => '-55',
      'Ccaron' => '-55',
      'Ccedilla' => '-55',
      'G' => '-55',
      'Gbreve' => '-55',
      'Gcommaaccent' => '-55',
      'O' => '-45',
      'Oacute' => '-45',
      'Ocircumflex' => '-45',
      'Odieresis' => '-45',
      'Ograve' => '-45',
      'Ohungarumlaut' => '-45',
      'Omacron' => '-45',
      'Oslash' => '-45',
      'Otilde' => '-45',
      'Q' => '-45',
      'T' => '-95',
      'Tcaron' => '-95',
      'Tcommaaccent' => '-95',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-145',
      'W' => '-130',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'p' => '-25',
      'quoteright' => '-74',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'v' => '-100',
      'w' => '-90',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Aogonek' => 
    array (
      'C' => '-55',
      'Cacute' => '-55',
      'Ccaron' => '-55',
      'Ccedilla' => '-55',
      'G' => '-55',
      'Gbreve' => '-55',
      'Gcommaaccent' => '-55',
      'O' => '-45',
      'Oacute' => '-45',
      'Ocircumflex' => '-45',
      'Odieresis' => '-45',
      'Ograve' => '-45',
      'Ohungarumlaut' => '-45',
      'Omacron' => '-45',
      'Oslash' => '-45',
      'Otilde' => '-45',
      'Q' => '-45',
      'T' => '-95',
      'Tcaron' => '-95',
      'Tcommaaccent' => '-95',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-145',
      'W' => '-130',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'p' => '-25',
      'quoteright' => '-74',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'v' => '-100',
      'w' => '-90',
      'y' => '-34',
      'yacute' => '-34',
      'ydieresis' => '-34',
    ),
    'Aring' => 
    array (
      'C' => '-55',
      'Cacute' => '-55',
      'Ccaron' => '-55',
      'Ccedilla' => '-55',
      'G' => '-55',
      'Gbreve' => '-55',
      'Gcommaaccent' => '-55',
      'O' => '-45',
      'Oacute' => '-45',
      'Ocircumflex' => '-45',
      'Odieresis' => '-45',
      'Ograve' => '-45',
      'Ohungarumlaut' => '-45',
      'Omacron' => '-45',
      'Oslash' => '-45',
      'Otilde' => '-45',
      'Q' => '-45',
      'T' => '-95',
      'Tcaron' => '-95',
      'Tcommaaccent' => '-95',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-145',
      'W' => '-130',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'p' => '-25',
      'quoteright' => '-74',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'v' => '-100',
      'w' => '-90',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Atilde' => 
    array (
      'C' => '-55',
      'Cacute' => '-55',
      'Ccaron' => '-55',
      'Ccedilla' => '-55',
      'G' => '-55',
      'Gbreve' => '-55',
      'Gcommaaccent' => '-55',
      'O' => '-45',
      'Oacute' => '-45',
      'Ocircumflex' => '-45',
      'Odieresis' => '-45',
      'Ograve' => '-45',
      'Ohungarumlaut' => '-45',
      'Omacron' => '-45',
      'Oslash' => '-45',
      'Otilde' => '-45',
      'Q' => '-45',
      'T' => '-95',
      'Tcaron' => '-95',
      'Tcommaaccent' => '-95',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-145',
      'W' => '-130',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'p' => '-25',
      'quoteright' => '-74',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'v' => '-100',
      'w' => '-90',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'B' => 
    array (
      'A' => '-30',
      'Aacute' => '-30',
      'Abreve' => '-30',
      'Acircumflex' => '-30',
      'Adieresis' => '-30',
      'Agrave' => '-30',
      'Amacron' => '-30',
      'Aogonek' => '-30',
      'Aring' => '-30',
      'Atilde' => '-30',
      'U' => '-10',
      'Uacute' => '-10',
      'Ucircumflex' => '-10',
      'Udieresis' => '-10',
      'Ugrave' => '-10',
      'Uhungarumlaut' => '-10',
      'Umacron' => '-10',
      'Uogonek' => '-10',
      'Uring' => '-10',
    ),
    'D' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'V' => '-40',
      'W' => '-40',
      'Y' => '-40',
      'Yacute' => '-40',
      'Ydieresis' => '-40',
      'period' => '-20',
    ),
    'Dcaron' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'V' => '-40',
      'W' => '-40',
      'Y' => '-40',
      'Yacute' => '-40',
      'Ydieresis' => '-40',
      'period' => '-20',
    ),
    'Dcroat' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'V' => '-40',
      'W' => '-40',
      'Y' => '-40',
      'Yacute' => '-40',
      'Ydieresis' => '-40',
      'period' => '-20',
    ),
    'F' => 
    array (
      'A' => '-90',
      'Aacute' => '-90',
      'Abreve' => '-90',
      'Acircumflex' => '-90',
      'Adieresis' => '-90',
      'Agrave' => '-90',
      'Amacron' => '-90',
      'Aogonek' => '-90',
      'Aring' => '-90',
      'Atilde' => '-90',
      'a' => '-25',
      'aacute' => '-25',
      'abreve' => '-25',
      'acircumflex' => '-25',
      'adieresis' => '-25',
      'agrave' => '-25',
      'amacron' => '-25',
      'aogonek' => '-25',
      'aring' => '-25',
      'atilde' => '-25',
      'comma' => '-92',
      'e' => '-25',
      'eacute' => '-25',
      'ecaron' => '-25',
      'ecircumflex' => '-25',
      'edieresis' => '-25',
      'edotaccent' => '-25',
      'egrave' => '-25',
      'emacron' => '-25',
      'eogonek' => '-25',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'period' => '-110',
    ),
    'J' => 
    array (
      'A' => '-30',
      'Aacute' => '-30',
      'Abreve' => '-30',
      'Acircumflex' => '-30',
      'Adieresis' => '-30',
      'Agrave' => '-30',
      'Amacron' => '-30',
      'Aogonek' => '-30',
      'Aring' => '-30',
      'Atilde' => '-30',
      'a' => '-15',
      'aacute' => '-15',
      'abreve' => '-15',
      'acircumflex' => '-15',
      'adieresis' => '-15',
      'agrave' => '-15',
      'amacron' => '-15',
      'aogonek' => '-15',
      'aring' => '-15',
      'atilde' => '-15',
      'e' => '-15',
      'eacute' => '-15',
      'ecaron' => '-15',
      'ecircumflex' => '-15',
      'edieresis' => '-15',
      'edotaccent' => '-15',
      'egrave' => '-15',
      'emacron' => '-15',
      'eogonek' => '-15',
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
      'period' => '-20',
      'u' => '-15',
      'uacute' => '-15',
      'ucircumflex' => '-15',
      'udieresis' => '-15',
      'ugrave' => '-15',
      'uhungarumlaut' => '-15',
      'umacron' => '-15',
      'uogonek' => '-15',
      'uring' => '-15',
    ),
    'K' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'e' => '-25',
      'eacute' => '-25',
      'ecaron' => '-25',
      'ecircumflex' => '-25',
      'edieresis' => '-25',
      'edotaccent' => '-25',
      'egrave' => '-25',
      'emacron' => '-25',
      'eogonek' => '-25',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'u' => '-15',
      'uacute' => '-15',
      'ucircumflex' => '-15',
      'udieresis' => '-15',
      'ugrave' => '-15',
      'uhungarumlaut' => '-15',
      'umacron' => '-15',
      'uogonek' => '-15',
      'uring' => '-15',
      'y' => '-45',
      'yacute' => '-45',
      'ydieresis' => '-45',
    ),
    'Kcommaaccent' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'e' => '-25',
      'eacute' => '-25',
      'ecaron' => '-25',
      'ecircumflex' => '-25',
      'edieresis' => '-25',
      'edotaccent' => '-25',
      'egrave' => '-25',
      'emacron' => '-25',
      'eogonek' => '-25',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'u' => '-15',
      'uacute' => '-15',
      'ucircumflex' => '-15',
      'udieresis' => '-15',
      'ugrave' => '-15',
      'uhungarumlaut' => '-15',
      'umacron' => '-15',
      'uogonek' => '-15',
      'uring' => '-15',
      'y' => '-45',
      'yacute' => '-45',
      'ydieresis' => '-45',
    ),
    'L' => 
    array (
      'T' => '-92',
      'Tcaron' => '-92',
      'Tcommaaccent' => '-92',
      'V' => '-92',
      'W' => '-92',
      'Y' => '-92',
      'Yacute' => '-92',
      'Ydieresis' => '-92',
      'quotedblright' => '-20',
      'quoteright' => '-110',
      'y' => '-55',
      'yacute' => '-55',
      'ydieresis' => '-55',
    ),
    'Lacute' => 
    array (
      'T' => '-92',
      'Tcaron' => '-92',
      'Tcommaaccent' => '-92',
      'V' => '-92',
      'W' => '-92',
      'Y' => '-92',
      'Yacute' => '-92',
      'Ydieresis' => '-92',
      'quotedblright' => '-20',
      'quoteright' => '-110',
      'y' => '-55',
      'yacute' => '-55',
      'ydieresis' => '-55',
    ),
    'Lcommaaccent' => 
    array (
      'T' => '-92',
      'Tcaron' => '-92',
      'Tcommaaccent' => '-92',
      'V' => '-92',
      'W' => '-92',
      'Y' => '-92',
      'Yacute' => '-92',
      'Ydieresis' => '-92',
      'quotedblright' => '-20',
      'quoteright' => '-110',
      'y' => '-55',
      'yacute' => '-55',
      'ydieresis' => '-55',
    ),
    'Lslash' => 
    array (
      'T' => '-92',
      'Tcaron' => '-92',
      'Tcommaaccent' => '-92',
      'V' => '-92',
      'W' => '-92',
      'Y' => '-92',
      'Yacute' => '-92',
      'Ydieresis' => '-92',
      'quotedblright' => '-20',
      'quoteright' => '-110',
      'y' => '-55',
      'yacute' => '-55',
      'ydieresis' => '-55',
    ),
    'N' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
    ),
    'Nacute' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
    ),
    'Ncaron' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
    ),
    'Ncommaaccent' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
    ),
    'Ntilde' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
    ),
    'O' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Oacute' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Ocircumflex' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Odieresis' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Ograve' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Ohungarumlaut' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Omacron' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Oslash' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Otilde' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'P' => 
    array (
      'A' => '-74',
      'Aacute' => '-74',
      'Abreve' => '-74',
      'Acircumflex' => '-74',
      'Adieresis' => '-74',
      'Agrave' => '-74',
      'Amacron' => '-74',
      'Aogonek' => '-74',
      'Aring' => '-74',
      'Atilde' => '-74',
      'a' => '-10',
      'aacute' => '-10',
      'abreve' => '-10',
      'acircumflex' => '-10',
      'adieresis' => '-10',
      'agrave' => '-10',
      'amacron' => '-10',
      'aogonek' => '-10',
      'aring' => '-10',
      'atilde' => '-10',
      'comma' => '-92',
      'e' => '-20',
      'eacute' => '-20',
      'ecaron' => '-20',
      'ecircumflex' => '-20',
      'edieresis' => '-20',
      'edotaccent' => '-20',
      'egrave' => '-20',
      'emacron' => '-20',
      'eogonek' => '-20',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
      'period' => '-110',
    ),
    'Q' => 
    array (
      'U' => '-10',
      'Uacute' => '-10',
      'Ucircumflex' => '-10',
      'Udieresis' => '-10',
      'Ugrave' => '-10',
      'Uhungarumlaut' => '-10',
      'Umacron' => '-10',
      'Uogonek' => '-10',
      'Uring' => '-10',
      'period' => '-20',
    ),
    'R' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'U' => '-30',
      'Uacute' => '-30',
      'Ucircumflex' => '-30',
      'Udieresis' => '-30',
      'Ugrave' => '-30',
      'Uhungarumlaut' => '-30',
      'Umacron' => '-30',
      'Uogonek' => '-30',
      'Uring' => '-30',
      'V' => '-55',
      'W' => '-35',
      'Y' => '-35',
      'Yacute' => '-35',
      'Ydieresis' => '-35',
    ),
    'Racute' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'U' => '-30',
      'Uacute' => '-30',
      'Ucircumflex' => '-30',
      'Udieresis' => '-30',
      'Ugrave' => '-30',
      'Uhungarumlaut' => '-30',
      'Umacron' => '-30',
      'Uogonek' => '-30',
      'Uring' => '-30',
      'V' => '-55',
      'W' => '-35',
      'Y' => '-35',
      'Yacute' => '-35',
      'Ydieresis' => '-35',
    ),
    'Rcaron' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'U' => '-30',
      'Uacute' => '-30',
      'Ucircumflex' => '-30',
      'Udieresis' => '-30',
      'Ugrave' => '-30',
      'Uhungarumlaut' => '-30',
      'Umacron' => '-30',
      'Uogonek' => '-30',
      'Uring' => '-30',
      'V' => '-55',
      'W' => '-35',
      'Y' => '-35',
      'Yacute' => '-35',
      'Ydieresis' => '-35',
    ),
    'Rcommaaccent' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'U' => '-30',
      'Uacute' => '-30',
      'Ucircumflex' => '-30',
      'Udieresis' => '-30',
      'Ugrave' => '-30',
      'Uhungarumlaut' => '-30',
      'Umacron' => '-30',
      'Uogonek' => '-30',
      'Uring' => '-30',
      'V' => '-55',
      'W' => '-35',
      'Y' => '-35',
      'Yacute' => '-35',
      'Ydieresis' => '-35',
    ),
    'T' => 
    array (
      'A' => '-90',
      'Aacute' => '-90',
      'Abreve' => '-90',
      'Acircumflex' => '-90',
      'Adieresis' => '-90',
      'Agrave' => '-90',
      'Amacron' => '-90',
      'Aogonek' => '-90',
      'Aring' => '-90',
      'Atilde' => '-90',
      'O' => '-18',
      'Oacute' => '-18',
      'Ocircumflex' => '-18',
      'Odieresis' => '-18',
      'Ograve' => '-18',
      'Ohungarumlaut' => '-18',
      'Omacron' => '-18',
      'Oslash' => '-18',
      'Otilde' => '-18',
      'a' => '-92',
      'aacute' => '-92',
      'abreve' => '-52',
      'acircumflex' => '-52',
      'adieresis' => '-52',
      'agrave' => '-52',
      'amacron' => '-52',
      'aogonek' => '-92',
      'aring' => '-92',
      'atilde' => '-52',
      'colon' => '-74',
      'comma' => '-74',
      'e' => '-92',
      'eacute' => '-92',
      'ecaron' => '-92',
      'ecircumflex' => '-92',
      'edieresis' => '-52',
      'edotaccent' => '-92',
      'egrave' => '-52',
      'emacron' => '-52',
      'eogonek' => '-92',
      'hyphen' => '-92',
      'i' => '-18',
      'iacute' => '-18',
      'iogonek' => '-18',
      'o' => '-92',
      'oacute' => '-92',
      'ocircumflex' => '-92',
      'odieresis' => '-92',
      'ograve' => '-92',
      'ohungarumlaut' => '-92',
      'omacron' => '-92',
      'oslash' => '-92',
      'otilde' => '-92',
      'period' => '-90',
      'r' => '-74',
      'racute' => '-74',
      'rcaron' => '-74',
      'rcommaaccent' => '-74',
      'semicolon' => '-74',
      'u' => '-92',
      'uacute' => '-92',
      'ucircumflex' => '-92',
      'udieresis' => '-92',
      'ugrave' => '-92',
      'uhungarumlaut' => '-92',
      'umacron' => '-92',
      'uogonek' => '-92',
      'uring' => '-92',
      'w' => '-74',
      'y' => '-34',
      'yacute' => '-34',
      'ydieresis' => '-34',
    ),
    'Tcaron' => 
    array (
      'A' => '-90',
      'Aacute' => '-90',
      'Abreve' => '-90',
      'Acircumflex' => '-90',
      'Adieresis' => '-90',
      'Agrave' => '-90',
      'Amacron' => '-90',
      'Aogonek' => '-90',
      'Aring' => '-90',
      'Atilde' => '-90',
      'O' => '-18',
      'Oacute' => '-18',
      'Ocircumflex' => '-18',
      'Odieresis' => '-18',
      'Ograve' => '-18',
      'Ohungarumlaut' => '-18',
      'Omacron' => '-18',
      'Oslash' => '-18',
      'Otilde' => '-18',
      'a' => '-92',
      'aacute' => '-92',
      'abreve' => '-52',
      'acircumflex' => '-52',
      'adieresis' => '-52',
      'agrave' => '-52',
      'amacron' => '-52',
      'aogonek' => '-92',
      'aring' => '-92',
      'atilde' => '-52',
      'colon' => '-74',
      'comma' => '-74',
      'e' => '-92',
      'eacute' => '-92',
      'ecaron' => '-92',
      'ecircumflex' => '-92',
      'edieresis' => '-52',
      'edotaccent' => '-92',
      'egrave' => '-52',
      'emacron' => '-52',
      'eogonek' => '-92',
      'hyphen' => '-92',
      'i' => '-18',
      'iacute' => '-18',
      'iogonek' => '-18',
      'o' => '-92',
      'oacute' => '-92',
      'ocircumflex' => '-92',
      'odieresis' => '-92',
      'ograve' => '-92',
      'ohungarumlaut' => '-92',
      'omacron' => '-92',
      'oslash' => '-92',
      'otilde' => '-92',
      'period' => '-90',
      'r' => '-74',
      'racute' => '-74',
      'rcaron' => '-74',
      'rcommaaccent' => '-74',
      'semicolon' => '-74',
      'u' => '-92',
      'uacute' => '-92',
      'ucircumflex' => '-92',
      'udieresis' => '-92',
      'ugrave' => '-92',
      'uhungarumlaut' => '-92',
      'umacron' => '-92',
      'uogonek' => '-92',
      'uring' => '-92',
      'w' => '-74',
      'y' => '-34',
      'yacute' => '-34',
      'ydieresis' => '-34',
    ),
    'Tcommaaccent' => 
    array (
      'A' => '-90',
      'Aacute' => '-90',
      'Abreve' => '-90',
      'Acircumflex' => '-90',
      'Adieresis' => '-90',
      'Agrave' => '-90',
      'Amacron' => '-90',
      'Aogonek' => '-90',
      'Aring' => '-90',
      'Atilde' => '-90',
      'O' => '-18',
      'Oacute' => '-18',
      'Ocircumflex' => '-18',
      'Odieresis' => '-18',
      'Ograve' => '-18',
      'Ohungarumlaut' => '-18',
      'Omacron' => '-18',
      'Oslash' => '-18',
      'Otilde' => '-18',
      'a' => '-92',
      'aacute' => '-92',
      'abreve' => '-52',
      'acircumflex' => '-52',
      'adieresis' => '-52',
      'agrave' => '-52',
      'amacron' => '-52',
      'aogonek' => '-92',
      'aring' => '-92',
      'atilde' => '-52',
      'colon' => '-74',
      'comma' => '-74',
      'e' => '-92',
      'eacute' => '-92',
      'ecaron' => '-92',
      'ecircumflex' => '-92',
      'edieresis' => '-52',
      'edotaccent' => '-92',
      'egrave' => '-52',
      'emacron' => '-52',
      'eogonek' => '-92',
      'hyphen' => '-92',
      'i' => '-18',
      'iacute' => '-18',
      'iogonek' => '-18',
      'o' => '-92',
      'oacute' => '-92',
      'ocircumflex' => '-92',
      'odieresis' => '-92',
      'ograve' => '-92',
      'ohungarumlaut' => '-92',
      'omacron' => '-92',
      'oslash' => '-92',
      'otilde' => '-92',
      'period' => '-90',
      'r' => '-74',
      'racute' => '-74',
      'rcaron' => '-74',
      'rcommaaccent' => '-74',
      'semicolon' => '-74',
      'u' => '-92',
      'uacute' => '-92',
      'ucircumflex' => '-92',
      'udieresis' => '-92',
      'ugrave' => '-92',
      'uhungarumlaut' => '-92',
      'umacron' => '-92',
      'uogonek' => '-92',
      'uring' => '-92',
      'w' => '-74',
      'y' => '-34',
      'yacute' => '-34',
      'ydieresis' => '-34',
    ),
    'U' => 
    array (
      'A' => '-60',
      'Aacute' => '-60',
      'Abreve' => '-60',
      'Acircumflex' => '-60',
      'Adieresis' => '-60',
      'Agrave' => '-60',
      'Amacron' => '-60',
      'Aogonek' => '-60',
      'Aring' => '-60',
      'Atilde' => '-60',
      'comma' => '-50',
      'period' => '-50',
    ),
    'Uacute' => 
    array (
      'A' => '-60',
      'Aacute' => '-60',
      'Abreve' => '-60',
      'Acircumflex' => '-60',
      'Adieresis' => '-60',
      'Agrave' => '-60',
      'Amacron' => '-60',
      'Aogonek' => '-60',
      'Aring' => '-60',
      'Atilde' => '-60',
      'comma' => '-50',
      'period' => '-50',
    ),
    'Ucircumflex' => 
    array (
      'A' => '-60',
      'Aacute' => '-60',
      'Abreve' => '-60',
      'Acircumflex' => '-60',
      'Adieresis' => '-60',
      'Agrave' => '-60',
      'Amacron' => '-60',
      'Aogonek' => '-60',
      'Aring' => '-60',
      'Atilde' => '-60',
      'comma' => '-50',
      'period' => '-50',
    ),
    'Udieresis' => 
    array (
      'A' => '-60',
      'Aacute' => '-60',
      'Abreve' => '-60',
      'Acircumflex' => '-60',
      'Adieresis' => '-60',
      'Agrave' => '-60',
      'Amacron' => '-60',
      'Aogonek' => '-60',
      'Aring' => '-60',
      'Atilde' => '-60',
      'comma' => '-50',
      'period' => '-50',
    ),
    'Ugrave' => 
    array (
      'A' => '-60',
      'Aacute' => '-60',
      'Abreve' => '-60',
      'Acircumflex' => '-60',
      'Adieresis' => '-60',
      'Agrave' => '-60',
      'Amacron' => '-60',
      'Aogonek' => '-60',
      'Aring' => '-60',
      'Atilde' => '-60',
      'comma' => '-50',
      'period' => '-50',
    ),
    'Uhungarumlaut' => 
    array (
      'A' => '-60',
      'Aacute' => '-60',
      'Abreve' => '-60',
      'Acircumflex' => '-60',
      'Adieresis' => '-60',
      'Agrave' => '-60',
      'Amacron' => '-60',
      'Aogonek' => '-60',
      'Aring' => '-60',
      'Atilde' => '-60',
      'comma' => '-50',
      'period' => '-50',
    ),
    'Umacron' => 
    array (
      'A' => '-60',
      'Aacute' => '-60',
      'Abreve' => '-60',
      'Acircumflex' => '-60',
      'Adieresis' => '-60',
      'Agrave' => '-60',
      'Amacron' => '-60',
      'Aogonek' => '-60',
      'Aring' => '-60',
      'Atilde' => '-60',
      'comma' => '-50',
      'period' => '-50',
    ),
    'Uogonek' => 
    array (
      'A' => '-60',
      'Aacute' => '-60',
      'Abreve' => '-60',
      'Acircumflex' => '-60',
      'Adieresis' => '-60',
      'Agrave' => '-60',
      'Amacron' => '-60',
      'Aogonek' => '-60',
      'Aring' => '-60',
      'Atilde' => '-60',
      'comma' => '-50',
      'period' => '-50',
    ),
    'Uring' => 
    array (
      'A' => '-60',
      'Aacute' => '-60',
      'Abreve' => '-60',
      'Acircumflex' => '-60',
      'Adieresis' => '-60',
      'Agrave' => '-60',
      'Amacron' => '-60',
      'Aogonek' => '-60',
      'Aring' => '-60',
      'Atilde' => '-60',
      'comma' => '-50',
      'period' => '-50',
    ),
    'V' => 
    array (
      'A' => '-135',
      'Aacute' => '-135',
      'Abreve' => '-135',
      'Acircumflex' => '-135',
      'Adieresis' => '-135',
      'Agrave' => '-135',
      'Amacron' => '-135',
      'Aogonek' => '-135',
      'Aring' => '-135',
      'Atilde' => '-135',
      'G' => '-30',
      'Gbreve' => '-30',
      'Gcommaaccent' => '-30',
      'O' => '-45',
      'Oacute' => '-45',
      'Ocircumflex' => '-45',
      'Odieresis' => '-45',
      'Ograve' => '-45',
      'Ohungarumlaut' => '-45',
      'Omacron' => '-45',
      'Oslash' => '-45',
      'Otilde' => '-45',
      'a' => '-92',
      'aacute' => '-92',
      'abreve' => '-92',
      'acircumflex' => '-92',
      'adieresis' => '-92',
      'agrave' => '-92',
      'amacron' => '-92',
      'aogonek' => '-92',
      'aring' => '-92',
      'atilde' => '-92',
      'colon' => '-92',
      'comma' => '-129',
      'e' => '-100',
      'eacute' => '-100',
      'ecaron' => '-100',
      'ecircumflex' => '-100',
      'edieresis' => '-100',
      'edotaccent' => '-100',
      'egrave' => '-100',
      'emacron' => '-100',
      'eogonek' => '-100',
      'hyphen' => '-74',
      'i' => '-37',
      'iacute' => '-37',
      'icircumflex' => '-37',
      'idieresis' => '-37',
      'igrave' => '-37',
      'imacron' => '-37',
      'iogonek' => '-37',
      'o' => '-100',
      'oacute' => '-100',
      'ocircumflex' => '-100',
      'odieresis' => '-100',
      'ograve' => '-100',
      'ohungarumlaut' => '-100',
      'omacron' => '-100',
      'oslash' => '-100',
      'otilde' => '-100',
      'period' => '-145',
      'semicolon' => '-92',
      'u' => '-92',
      'uacute' => '-92',
      'ucircumflex' => '-92',
      'udieresis' => '-92',
      'ugrave' => '-92',
      'uhungarumlaut' => '-92',
      'umacron' => '-92',
      'uogonek' => '-92',
      'uring' => '-92',
    ),
    'W' => 
    array (
      'A' => '-120',
      'Aacute' => '-120',
      'Abreve' => '-120',
      'Acircumflex' => '-120',
      'Adieresis' => '-120',
      'Agrave' => '-120',
      'Amacron' => '-120',
      'Aogonek' => '-120',
      'Aring' => '-120',
      'Atilde' => '-120',
      'O' => '-10',
      'Oacute' => '-10',
      'Ocircumflex' => '-10',
      'Odieresis' => '-10',
      'Ograve' => '-10',
      'Ohungarumlaut' => '-10',
      'Omacron' => '-10',
      'Oslash' => '-10',
      'Otilde' => '-10',
      'a' => '-65',
      'aacute' => '-65',
      'abreve' => '-65',
      'acircumflex' => '-65',
      'adieresis' => '-65',
      'agrave' => '-65',
      'amacron' => '-65',
      'aogonek' => '-65',
      'aring' => '-65',
      'atilde' => '-65',
      'colon' => '-55',
      'comma' => '-92',
      'e' => '-65',
      'eacute' => '-65',
      'ecaron' => '-65',
      'ecircumflex' => '-65',
      'edieresis' => '-65',
      'edotaccent' => '-65',
      'egrave' => '-65',
      'emacron' => '-65',
      'eogonek' => '-65',
      'hyphen' => '-37',
      'i' => '-18',
      'iacute' => '-18',
      'iogonek' => '-18',
      'o' => '-75',
      'oacute' => '-75',
      'ocircumflex' => '-75',
      'odieresis' => '-75',
      'ograve' => '-75',
      'ohungarumlaut' => '-75',
      'omacron' => '-75',
      'oslash' => '-75',
      'otilde' => '-75',
      'period' => '-92',
      'semicolon' => '-55',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'y' => '-60',
      'yacute' => '-60',
      'ydieresis' => '-60',
    ),
    'Y' => 
    array (
      'A' => '-110',
      'Aacute' => '-110',
      'Abreve' => '-110',
      'Acircumflex' => '-110',
      'Adieresis' => '-110',
      'Agrave' => '-110',
      'Amacron' => '-110',
      'Aogonek' => '-110',
      'Aring' => '-110',
      'Atilde' => '-110',
      'O' => '-35',
      'Oacute' => '-35',
      'Ocircumflex' => '-35',
      'Odieresis' => '-35',
      'Ograve' => '-35',
      'Ohungarumlaut' => '-35',
      'Omacron' => '-35',
      'Oslash' => '-35',
      'Otilde' => '-35',
      'a' => '-85',
      'aacute' => '-85',
      'abreve' => '-85',
      'acircumflex' => '-85',
      'adieresis' => '-85',
      'agrave' => '-85',
      'amacron' => '-85',
      'aogonek' => '-85',
      'aring' => '-85',
      'atilde' => '-85',
      'colon' => '-92',
      'comma' => '-92',
      'e' => '-111',
      'eacute' => '-111',
      'ecaron' => '-111',
      'ecircumflex' => '-111',
      'edieresis' => '-71',
      'edotaccent' => '-111',
      'egrave' => '-71',
      'emacron' => '-71',
      'eogonek' => '-111',
      'hyphen' => '-92',
      'i' => '-37',
      'iacute' => '-37',
      'iogonek' => '-37',
      'o' => '-111',
      'oacute' => '-111',
      'ocircumflex' => '-111',
      'odieresis' => '-111',
      'ograve' => '-111',
      'ohungarumlaut' => '-111',
      'omacron' => '-111',
      'oslash' => '-111',
      'otilde' => '-111',
      'period' => '-92',
      'semicolon' => '-92',
      'u' => '-92',
      'uacute' => '-92',
      'ucircumflex' => '-92',
      'udieresis' => '-92',
      'ugrave' => '-92',
      'uhungarumlaut' => '-92',
      'umacron' => '-92',
      'uogonek' => '-92',
      'uring' => '-92',
    ),
    'Yacute' => 
    array (
      'A' => '-110',
      'Aacute' => '-110',
      'Abreve' => '-110',
      'Acircumflex' => '-110',
      'Adieresis' => '-110',
      'Agrave' => '-110',
      'Amacron' => '-110',
      'Aogonek' => '-110',
      'Aring' => '-110',
      'Atilde' => '-110',
      'O' => '-35',
      'Oacute' => '-35',
      'Ocircumflex' => '-35',
      'Odieresis' => '-35',
      'Ograve' => '-35',
      'Ohungarumlaut' => '-35',
      'Omacron' => '-35',
      'Oslash' => '-35',
      'Otilde' => '-35',
      'a' => '-85',
      'aacute' => '-85',
      'abreve' => '-85',
      'acircumflex' => '-85',
      'adieresis' => '-85',
      'agrave' => '-85',
      'amacron' => '-85',
      'aogonek' => '-85',
      'aring' => '-85',
      'atilde' => '-85',
      'colon' => '-92',
      'comma' => '-92',
      'e' => '-111',
      'eacute' => '-111',
      'ecaron' => '-111',
      'ecircumflex' => '-111',
      'edieresis' => '-71',
      'edotaccent' => '-111',
      'egrave' => '-71',
      'emacron' => '-71',
      'eogonek' => '-111',
      'hyphen' => '-92',
      'i' => '-37',
      'iacute' => '-37',
      'iogonek' => '-37',
      'o' => '-111',
      'oacute' => '-111',
      'ocircumflex' => '-111',
      'odieresis' => '-111',
      'ograve' => '-111',
      'ohungarumlaut' => '-111',
      'omacron' => '-111',
      'oslash' => '-111',
      'otilde' => '-111',
      'period' => '-92',
      'semicolon' => '-92',
      'u' => '-92',
      'uacute' => '-92',
      'ucircumflex' => '-92',
      'udieresis' => '-92',
      'ugrave' => '-92',
      'uhungarumlaut' => '-92',
      'umacron' => '-92',
      'uogonek' => '-92',
      'uring' => '-92',
    ),
    'Ydieresis' => 
    array (
      'A' => '-110',
      'Aacute' => '-110',
      'Abreve' => '-110',
      'Acircumflex' => '-110',
      'Adieresis' => '-110',
      'Agrave' => '-110',
      'Amacron' => '-110',
      'Aogonek' => '-110',
      'Aring' => '-110',
      'Atilde' => '-110',
      'O' => '-35',
      'Oacute' => '-35',
      'Ocircumflex' => '-35',
      'Odieresis' => '-35',
      'Ograve' => '-35',
      'Ohungarumlaut' => '-35',
      'Omacron' => '-35',
      'Oslash' => '-35',
      'Otilde' => '-35',
      'a' => '-85',
      'aacute' => '-85',
      'abreve' => '-85',
      'acircumflex' => '-85',
      'adieresis' => '-85',
      'agrave' => '-85',
      'amacron' => '-85',
      'aogonek' => '-85',
      'aring' => '-85',
      'atilde' => '-85',
      'colon' => '-92',
      'comma' => '-92',
      'e' => '-111',
      'eacute' => '-111',
      'ecaron' => '-111',
      'ecircumflex' => '-111',
      'edieresis' => '-71',
      'edotaccent' => '-111',
      'egrave' => '-71',
      'emacron' => '-71',
      'eogonek' => '-111',
      'hyphen' => '-92',
      'i' => '-37',
      'iacute' => '-37',
      'iogonek' => '-37',
      'o' => '-111',
      'oacute' => '-111',
      'ocircumflex' => '-111',
      'odieresis' => '-111',
      'ograve' => '-111',
      'ohungarumlaut' => '-111',
      'omacron' => '-111',
      'oslash' => '-111',
      'otilde' => '-111',
      'period' => '-92',
      'semicolon' => '-92',
      'u' => '-92',
      'uacute' => '-92',
      'ucircumflex' => '-92',
      'udieresis' => '-92',
      'ugrave' => '-92',
      'uhungarumlaut' => '-92',
      'umacron' => '-92',
      'uogonek' => '-92',
      'uring' => '-92',
    ),
    'a' => 
    array (
      'v' => '-25',
    ),
    'aacute' => 
    array (
      'v' => '-25',
    ),
    'abreve' => 
    array (
      'v' => '-25',
    ),
    'acircumflex' => 
    array (
      'v' => '-25',
    ),
    'adieresis' => 
    array (
      'v' => '-25',
    ),
    'agrave' => 
    array (
      'v' => '-25',
    ),
    'amacron' => 
    array (
      'v' => '-25',
    ),
    'aogonek' => 
    array (
      'v' => '-25',
    ),
    'aring' => 
    array (
      'v' => '-25',
    ),
    'atilde' => 
    array (
      'v' => '-25',
    ),
    'b' => 
    array (
      'b' => '-10',
      'period' => '-40',
      'u' => '-20',
      'uacute' => '-20',
      'ucircumflex' => '-20',
      'udieresis' => '-20',
      'ugrave' => '-20',
      'uhungarumlaut' => '-20',
      'umacron' => '-20',
      'uogonek' => '-20',
      'uring' => '-20',
      'v' => '-15',
    ),
    'comma' => 
    array (
      'quotedblright' => '-45',
      'quoteright' => '-55',
    ),
    'd' => 
    array (
      'w' => '-15',
    ),
    'dcroat' => 
    array (
      'w' => '-15',
    ),
    'e' => 
    array (
      'v' => '-15',
    ),
    'eacute' => 
    array (
      'v' => '-15',
    ),
    'ecaron' => 
    array (
      'v' => '-15',
    ),
    'ecircumflex' => 
    array (
      'v' => '-15',
    ),
    'edieresis' => 
    array (
      'v' => '-15',
    ),
    'edotaccent' => 
    array (
      'v' => '-15',
    ),
    'egrave' => 
    array (
      'v' => '-15',
    ),
    'emacron' => 
    array (
      'v' => '-15',
    ),
    'eogonek' => 
    array (
      'v' => '-15',
    ),
    'f' => 
    array (
      'comma' => '-15',
      'dotlessi' => '-35',
      'i' => '-25',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'period' => '-15',
      'quotedblright' => '50',
      'quoteright' => '55',
    ),
    'g' => 
    array (
      'period' => '-15',
    ),
    'gbreve' => 
    array (
      'period' => '-15',
    ),
    'gcommaaccent' => 
    array (
      'period' => '-15',
    ),
    'h' => 
    array (
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'i' => 
    array (
      'v' => '-10',
    ),
    'iacute' => 
    array (
      'v' => '-10',
    ),
    'icircumflex' => 
    array (
      'v' => '-10',
    ),
    'idieresis' => 
    array (
      'v' => '-10',
    ),
    'igrave' => 
    array (
      'v' => '-10',
    ),
    'imacron' => 
    array (
      'v' => '-10',
    ),
    'iogonek' => 
    array (
      'v' => '-10',
    ),
    'k' => 
    array (
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'kcommaaccent' => 
    array (
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'n' => 
    array (
      'v' => '-40',
    ),
    'nacute' => 
    array (
      'v' => '-40',
    ),
    'ncaron' => 
    array (
      'v' => '-40',
    ),
    'ncommaaccent' => 
    array (
      'v' => '-40',
    ),
    'ntilde' => 
    array (
      'v' => '-40',
    ),
    'o' => 
    array (
      'v' => '-10',
      'w' => '-10',
    ),
    'oacute' => 
    array (
      'v' => '-10',
      'w' => '-10',
    ),
    'ocircumflex' => 
    array (
      'v' => '-10',
      'w' => '-10',
    ),
    'odieresis' => 
    array (
      'v' => '-10',
      'w' => '-10',
    ),
    'ograve' => 
    array (
      'v' => '-10',
      'w' => '-10',
    ),
    'ohungarumlaut' => 
    array (
      'v' => '-10',
      'w' => '-10',
    ),
    'omacron' => 
    array (
      'v' => '-10',
      'w' => '-10',
    ),
    'oslash' => 
    array (
      'v' => '-10',
      'w' => '-10',
    ),
    'otilde' => 
    array (
      'v' => '-10',
      'w' => '-10',
    ),
    'period' => 
    array (
      'quotedblright' => '-55',
      'quoteright' => '-55',
    ),
    'quotedblleft' => 
    array (
      'A' => '-10',
      'Aacute' => '-10',
      'Abreve' => '-10',
      'Acircumflex' => '-10',
      'Adieresis' => '-10',
      'Agrave' => '-10',
      'Amacron' => '-10',
      'Aogonek' => '-10',
      'Aring' => '-10',
      'Atilde' => '-10',
    ),
    'quoteleft' => 
    array (
      'A' => '-10',
      'Aacute' => '-10',
      'Abreve' => '-10',
      'Acircumflex' => '-10',
      'Adieresis' => '-10',
      'Agrave' => '-10',
      'Amacron' => '-10',
      'Aogonek' => '-10',
      'Aring' => '-10',
      'Atilde' => '-10',
      'quoteleft' => '-63',
    ),
    'quoteright' => 
    array (
      'd' => '-20',
      'dcroat' => '-20',
      'quoteright' => '-63',
      'r' => '-20',
      'racute' => '-20',
      'rcaron' => '-20',
      'rcommaaccent' => '-20',
      's' => '-37',
      'sacute' => '-37',
      'scaron' => '-37',
      'scedilla' => '-37',
      'scommaaccent' => '-37',
      'space' => '-74',
      'v' => '-20',
    ),
    'r' => 
    array (
      'c' => '-18',
      'cacute' => '-18',
      'ccaron' => '-18',
      'ccedilla' => '-18',
      'comma' => '-92',
      'e' => '-18',
      'eacute' => '-18',
      'ecaron' => '-18',
      'ecircumflex' => '-18',
      'edieresis' => '-18',
      'edotaccent' => '-18',
      'egrave' => '-18',
      'emacron' => '-18',
      'eogonek' => '-18',
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'hyphen' => '-37',
      'n' => '-15',
      'nacute' => '-15',
      'ncaron' => '-15',
      'ncommaaccent' => '-15',
      'ntilde' => '-15',
      'o' => '-18',
      'oacute' => '-18',
      'ocircumflex' => '-18',
      'odieresis' => '-18',
      'ograve' => '-18',
      'ohungarumlaut' => '-18',
      'omacron' => '-18',
      'oslash' => '-18',
      'otilde' => '-18',
      'p' => '-10',
      'period' => '-100',
      'q' => '-18',
      'v' => '-10',
    ),
    'racute' => 
    array (
      'c' => '-18',
      'cacute' => '-18',
      'ccaron' => '-18',
      'ccedilla' => '-18',
      'comma' => '-92',
      'e' => '-18',
      'eacute' => '-18',
      'ecaron' => '-18',
      'ecircumflex' => '-18',
      'edieresis' => '-18',
      'edotaccent' => '-18',
      'egrave' => '-18',
      'emacron' => '-18',
      'eogonek' => '-18',
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'hyphen' => '-37',
      'n' => '-15',
      'nacute' => '-15',
      'ncaron' => '-15',
      'ncommaaccent' => '-15',
      'ntilde' => '-15',
      'o' => '-18',
      'oacute' => '-18',
      'ocircumflex' => '-18',
      'odieresis' => '-18',
      'ograve' => '-18',
      'ohungarumlaut' => '-18',
      'omacron' => '-18',
      'oslash' => '-18',
      'otilde' => '-18',
      'p' => '-10',
      'period' => '-100',
      'q' => '-18',
      'v' => '-10',
    ),
    'rcaron' => 
    array (
      'c' => '-18',
      'cacute' => '-18',
      'ccaron' => '-18',
      'ccedilla' => '-18',
      'comma' => '-92',
      'e' => '-18',
      'eacute' => '-18',
      'ecaron' => '-18',
      'ecircumflex' => '-18',
      'edieresis' => '-18',
      'edotaccent' => '-18',
      'egrave' => '-18',
      'emacron' => '-18',
      'eogonek' => '-18',
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'hyphen' => '-37',
      'n' => '-15',
      'nacute' => '-15',
      'ncaron' => '-15',
      'ncommaaccent' => '-15',
      'ntilde' => '-15',
      'o' => '-18',
      'oacute' => '-18',
      'ocircumflex' => '-18',
      'odieresis' => '-18',
      'ograve' => '-18',
      'ohungarumlaut' => '-18',
      'omacron' => '-18',
      'oslash' => '-18',
      'otilde' => '-18',
      'p' => '-10',
      'period' => '-100',
      'q' => '-18',
      'v' => '-10',
    ),
    'rcommaaccent' => 
    array (
      'c' => '-18',
      'cacute' => '-18',
      'ccaron' => '-18',
      'ccedilla' => '-18',
      'comma' => '-92',
      'e' => '-18',
      'eacute' => '-18',
      'ecaron' => '-18',
      'ecircumflex' => '-18',
      'edieresis' => '-18',
      'edotaccent' => '-18',
      'egrave' => '-18',
      'emacron' => '-18',
      'eogonek' => '-18',
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'hyphen' => '-37',
      'n' => '-15',
      'nacute' => '-15',
      'ncaron' => '-15',
      'ncommaaccent' => '-15',
      'ntilde' => '-15',
      'o' => '-18',
      'oacute' => '-18',
      'ocircumflex' => '-18',
      'odieresis' => '-18',
      'ograve' => '-18',
      'ohungarumlaut' => '-18',
      'omacron' => '-18',
      'oslash' => '-18',
      'otilde' => '-18',
      'p' => '-10',
      'period' => '-100',
      'q' => '-18',
      'v' => '-10',
    ),
    'space' => 
    array (
      'A' => '-55',
      'Aacute' => '-55',
      'Abreve' => '-55',
      'Acircumflex' => '-55',
      'Adieresis' => '-55',
      'Agrave' => '-55',
      'Amacron' => '-55',
      'Aogonek' => '-55',
      'Aring' => '-55',
      'Atilde' => '-55',
      'T' => '-30',
      'Tcaron' => '-30',
      'Tcommaaccent' => '-30',
      'V' => '-45',
      'W' => '-30',
      'Y' => '-55',
      'Yacute' => '-55',
      'Ydieresis' => '-55',
    ),
    'v' => 
    array (
      'a' => '-10',
      'aacute' => '-10',
      'abreve' => '-10',
      'acircumflex' => '-10',
      'adieresis' => '-10',
      'agrave' => '-10',
      'amacron' => '-10',
      'aogonek' => '-10',
      'aring' => '-10',
      'atilde' => '-10',
      'comma' => '-55',
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-10',
      'oacute' => '-10',
      'ocircumflex' => '-10',
      'odieresis' => '-10',
      'ograve' => '-10',
      'ohungarumlaut' => '-10',
      'omacron' => '-10',
      'oslash' => '-10',
      'otilde' => '-10',
      'period' => '-70',
    ),
    'w' => 
    array (
      'comma' => '-55',
      'o' => '-10',
      'oacute' => '-10',
      'ocircumflex' => '-10',
      'odieresis' => '-10',
      'ograve' => '-10',
      'ohungarumlaut' => '-10',
      'omacron' => '-10',
      'oslash' => '-10',
      'otilde' => '-10',
      'period' => '-70',
    ),
    'y' => 
    array (
      'comma' => '-55',
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'period' => '-70',
    ),
    'yacute' => 
    array (
      'comma' => '-55',
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'period' => '-70',
    ),
    'ydieresis' => 
    array (
      'comma' => '-55',
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'period' => '-70',
    ),
  ),
  '_version_' => 1,
);