/* Mobile Responsive Fixes for Matrimony Site */

/* ========================================
   ICON FIXES
   ======================================== */

/* Fix for missing Glyphicons - Replace with Bootstrap Icons */
.gi-loader:before { content: "\f3f7"; font-family: "bootstrap-icons"; }
.gi-spin { animation: spin 1s linear infinite; }
.gi-refresh:before { content: "\f3f7"; font-family: "bootstrap-icons"; }
.gi-user:before { content: "\f4da"; font-family: "bootstrap-icons"; }
.gi-envelope:before { content: "\f32f"; font-family: "bootstrap-icons"; }
.gi-lock:before { content: "\f3f1"; font-family: "bootstrap-icons"; }
.gi-phone:before { content: "\f4b2"; font-family: "bootstrap-icons"; }
.gi-calendar:before { content: "\f1ec"; font-family: "bootstrap-icons"; }
.gi-heart:before { content: "\f33e"; font-family: "bootstrap-icons"; }
.gi-search:before { content: "\f52a"; font-family: "bootstrap-icons"; }

/* Font Awesome icon fixes */
.fa-mobile-alt { font-family: "Font Awesome 6 Free"; }
.fa-user-circle { font-family: "Font Awesome 6 Free"; }
.fa-walking { font-family: "Font Awesome 6 Free"; }

/* Spin animation for loading icons */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ========================================
   MOBILE RESPONSIVE FIXES
   ======================================== */

/* Ensure proper viewport scaling */
html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

/* Mobile-first responsive design */
@media (max-width: 767px) {
    
    /* Container fixes */
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    /* Registration form fixes */
    .gtRegister {
        padding: 15px 10px;
    }
    
    .gtRegister .form-group {
        margin-bottom: 20px;
    }
    
    .gtRegister .form-group .row {
        margin-left: 0;
        margin-right: 0;
    }
    
    .gtRegister .form-group .row > div {
        padding-left: 5px;
        padding-right: 5px;
    }
    
    /* Form labels on mobile */
    .gtRegister label {
        font-size: 14px !important;
        margin-bottom: 8px;
        display: block;
    }
    
    /* Form controls on mobile */
    .gt-form-control {
        font-size: 16px !important; /* Prevents zoom on iOS */
        padding: 12px 15px;
        height: auto;
        min-height: 44px; /* Touch-friendly size */
    }
    
    /* Select dropdowns on mobile */
    select.gt-form-control {
        font-size: 16px !important;
        padding: 12px 15px;
        background-size: 20px;
        background-position: right 10px center;
    }
    
    /* Button fixes for mobile */
    .btn, .gt-btn-green, .gt-btn-orange {
        min-height: 44px;
        padding: 12px 20px;
        font-size: 16px !important;
        width: 100%;
        margin-bottom: 10px;
    }
    
    /* Registration titles on mobile */
    .gtRegTitle {
        font-size: 18px !important;
        margin-bottom: 15px;
        text-align: center;
    }
    
    /* Icon adjustments for mobile */
    .gtRegTitle i {
        font-size: 20px;
        margin-right: 8px;
    }
    
    /* Radio button groups on mobile */
    .form-group input[type="radio"] {
        margin-right: 8px;
        transform: scale(1.2);
    }
    
    .form-group label[for] {
        font-size: 14px;
        margin-right: 15px;
        margin-bottom: 10px;
        display: inline-block;
    }
    
    /* Checkbox styling for mobile */
    input[type="checkbox"] {
        transform: scale(1.3);
        margin-right: 10px;
    }
    
    /* Navigation fixes */
    .navbar-toggle {
        padding: 8px 12px;
        margin-top: 10px;
        margin-bottom: 10px;
    }
    
    .navbar-toggle .icon-bar {
        width: 24px;
        height: 3px;
        margin-bottom: 4px;
    }
    
    /* Header form fixes */
    .gtHeaderForm .gt-form-control {
        font-size: 14px;
        padding: 8px 12px;
    }
    
    /* Panel fixes for mobile */
    .gt-panel {
        margin-bottom: 15px;
    }
    
    .gt-panel .gt-panel-head {
        padding: 10px 15px;
    }
    
    .gt-panel .gt-panel-body {
        padding: 15px;
    }
    
    /* Image responsive fixes */
    .img-responsive {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 0 auto;
    }
    
    /* Registration step images */
    .gtRegister img {
        max-width: 100%;
        height: auto;
        margin-bottom: 20px;
    }
    
    /* Footer fixes for mobile */
    .gt-footer-bottom {
        text-align: center;
        padding: 15px 10px;
    }
    
    .footer-before-login {
        padding: 20px 10px;
    }
    
    /* Social icons mobile fix */
    ul.gt-footer-social li a {
        font-size: 28px !important;
        padding: 10px;
    }
}

/* Tablet responsive fixes */
@media (min-width: 768px) and (max-width: 991px) {
    
    .gtRegister {
        padding: 20px 15px;
    }
    
    .gt-form-control {
        font-size: 14px;
        padding: 10px 12px;
    }
    
    .btn, .gt-btn-green, .gt-btn-orange {
        font-size: 14px;
        padding: 10px 20px;
    }
    
    .gtRegTitle {
        font-size: 20px;
    }
}

/* ========================================
   FORM VALIDATION FIXES
   ======================================== */

/* Validation error styling */
.validetta-error {
    color: #d9534f;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.validetta-error-field {
    border-color: #d9534f !important;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px rgba(217,83,79,.6);
}

/* ========================================
   LOADING AND PRELOADER FIXES
   ======================================== */

.preloader-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #499202;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ========================================
   ACCESSIBILITY IMPROVEMENTS
   ======================================== */

/* Focus states for better accessibility */
.gt-form-control:focus,
.btn:focus,
select:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
    outline: 2px solid #499202;
    outline-offset: 2px;
}

/* Better contrast for text */
.gt-text-light-Grey {
    color: #333 !important;
}

/* ========================================
   PRINT STYLES
   ======================================== */

@media print {
    .navbar,
    .gt-footer-bottom,
    .footer-before-login,
    .btn,
    .preloader-wrapper {
        display: none !important;
    }
    
    .container {
        width: 100% !important;
        max-width: none !important;
    }
}

/* ========================================
   DARK MODE SUPPORT (Optional)
   ======================================== */

@media (prefers-color-scheme: dark) {
    /* Add dark mode styles if needed */
    .gt-form-control {
        background-color: #2d2d2d;
        color: #fff;
        border-color: #555;
    }
    
    .gt-panel {
        background-color: #1e1e1e;
        color: #fff;
    }
}
