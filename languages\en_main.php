<?php
	$lang = array(
        "View More Photos" => "View More Photos",
         "Save Changes" => "Save Changes",
        
        
		"Enter your login id" => "Enter your login id",
        "Enter your password" => "Enter your password",
        "LOGIN" => "LOGIN",
        "Forgot Password ?" => "Forgot Password ?",
        "Login with OTP" => "Login with OTP",
        "Login" => "Login",
        "Last Login" => "Last Login",
        "Membership" => "Membership",
        "Upgrade Membership" => "Upgrade Membership",
        "Signup" => "Signup",
        "Contact Us" => "Contact Us",
        "Success Story" => "Success Story",
        "Occupation Search" => "Occupation Search",
        "Location Search" => "Location Search",
        "Keyword Search" => "Keyword Search",
        "Advanced Search" => "Advanced Search",
        "Basic Search" => "Basic Search",
        "Quick Search" => "Quick Search",
        "Search" => "Search",
        "Home" => "Home",
        "MENU" => "MENU",
        "No New Notification" => "No New Notification",
        "Notification" => "Notification",
        "Logout" => "Logout",
        "My Home" => "My Home",
        "My Profile" => "My Profile",
        "View Profile" => "View Profile",
        "Edit Profile" => "Edit Profile",
        "My Saved Searches" => "My Saved Searches",
        "My Messages" => "My Messages",
        "My Express Interest" => "My Express Interest",
        "Manage Photo" => "Manage Photo",
        "Manage Horoscope" => "Manage Horoscope",
        "Manage Document" => "Manage Document",
        "Search" => "Search",
        "My Matches" => "My Matches",
        "One Way Matches" => "One Way Matches",
        "Two Way Matches" => "Two Way Matches",
        "Broader Matches" => "Broader Matches",
        "Preferred Matches" => "Preferred Matches",
        "Custom Matches" => "Custom Matches",
        "Membership Plans" => "Membership Plans",
        "Current Plan" => "Current Plan",
        "Profile Details" => "Profile Details",
        "Shortlisted Profile" => "Shortlisted Profile",
        "Blocked Profile" => "Blocked Profile",
        "My Profile Viewed By" => "My Profile Viewed By",
        "I Visited Profile" => "I Visited Profile",
        "My Mobile No Viewed By" => "My Mobile No Viewed By",
        "Photo Password Request" => "Photo Password Request",
        "Settings" => "Settings",
        "Photo Privacy Setting" => "Photo Privacy Setting",
        "Contact View Setting" => "Contact View Setting",
        "Change Password" => "Change Password",
        "No New Notification" => "No New Notification",
        "100% Verified Profiles" => "100% Verified Profiles",
        "Best Matching Profiles" => "Best Matching Profiles",
        "REGISTER NOW" => "REGISTER NOW",
        "Profile Created By" => "Profile Created By",
        "Select Gender" => "Select Gender",
        "Enter First Name" => "Enter First Name",
        "Enter Last Name" => "Enter Last Name",
        "Day" => "Day",
        "Month" => "Month",
        "Year" => "Year",
        "Select Your Religion" => "Select Your Religion",
        "Select Religion First" => "Select Religion First",
        "Mother Tongue" => "Mother Tongue",
        "Country" => "Country",
        "Enter Your 10 Digit No" => "Enter Your 10 Digit No",
        "Enter Your Email Id" => "Enter Your Email Id",
        "I accept" => "I accept",
        "terms & conditions" => "terms & conditions",
        "privacy policy" => "privacy policy",
        "and" => "and",
        "Best matrimony service provider in India.We find the best perfect life partner for you.join us now and" => "Best matrimony service provider in India.We find the best perfect life partner for you.join us now and",
        "find your life partner from our thousand’s of verified profiles." => "find your life partner from our thousand’s of verified profiles.",
        "Hundred’s of successful member found their soulmates with us." => "Hundred’s of successful member found their soulmates with us.",
        "View Success Stories" => "View Success Stories",
        "Verified Members" => "Verified Members",
        "Thousands of verified member profile so our members find perfect partner without any concern." => "Thousands of verified member profile so our members find perfect partner without any concern.",
        "View Profiles Now" => "View Profiles Now",
        "Search Options" => "Search Options",
        "Multiple search options to find partner who know you better." => "Multiple search options to find partner who know you better.",
        "Search Now" => "Search Now",
        "Matching Profiles" => "Matching Profiles",
        "With our auto match profile you can see members which was suits you best and get married." => "With our auto match profile you can see members which was suits you best and get married.",
        "View Matches Now" => "View Matches Now",
        "Featured Brides" => "Featured Brides",
        "This is our featured brides section where you can check our elite profiles." => "This is our featured brides section where you can check our elite profiles.",
        "Featured Groom" => "Featured Groom",
        "This is our featured grooms section where you can check our elite profiles." => "This is our featured grooms section where you can check our elite profiles.",
        "Login With OTP" => "Login With OTP",
        "Email/Mobile No/Matri id" => "Email/Mobile No/Matri id",
        "Enter Email id / Mobile No / Matri Id" => "Enter Email id / Mobile No / Matri Id",
        "GET OTP" => "GET OTP",
        "Register Now" => "Register Now",
        "We are always happy to help" => "We are always happy to help",
        "Enter Email id / Matri id" => "Enter Email id / Matri id",
        "Email id / Matri id" => "Email id / Matri id",
        "SUBMIT" => "SUBMIT",
        "Search perfect partner with our multiple search options which help you to find out perfect partner for life" => "Search perfect partner with our multiple search options which help you to find out perfect partner for life",
        "Search profiles and provide you suitable profiles quickly" => "Search profiles and provide you suitable profiles quickly",
        "Gender" => "Gender",
        "Age" => "Age",
        "To" => "To",
        "Religion" => "Religion",
        "Caste" => "Caste",
        "Save Search" => "Save Search",
        "Searches to provide suitable profiles" => "Searches to provide suitable profiles",
        "Height" => "Height",
        "Marital status" => "Marital status",
        "Country living in" => "Country living in",
        "State living in" => "State living in",
        "City living in" => "City living in",
        "Education" => "Education",
        "Photo settings" => "Photo settings",
        "Advance search contain criteria that helps you to find a suitable profile" => "Advance search contain criteria that helps you to find a suitable profile",
        "Physical status" => "Physical status",
        "Religion" => "Religion",
        "Caste" => "Caste",
        "Location Details" => "Location Details",
        "Education / Occupation / income Details" => "Education / Occupation / income Details",
        "Occupation" => "Occupation",
        "Annual Income" => "Annual Income",
        "Horoscope Details" => "Horoscope Details",
        "Have a dosh" => "Have a dosh",
        "With keyword search, you can get suitable profiles with specific keywords" => "With keyword search, you can get suitable profiles with specific keywords",
        "Example - First Name, Last Name, Email id" => "Example - First Name, Last Name, Email id",
        "With Location search, you can get suitable profiles from specific location or place" => "With Location search, you can get suitable profiles from specific location or place",
        "With Occupation Search, you can get suitable profiles with specific type of occupation" => "With Occupation Search, you can get suitable profiles with specific type of occupation",
        "Search By Id" => "Search By Id",
        "Example - IN1234" => "Example - IN1234",
        "Enter Matri Id" => "Enter Matri Id",
        "Close" => "Close",
        "Submit" => "Submit",
        "Saved Search Name" => "Saved Search Name",
        "What do you think about our website and experience" => "What do you think about our website and experience",
        "How you think that your perfect and process further" => "How you think that your perfect and process further",
        "How you you contact your partner" => "How you you contact your partner",
        "How you create your id and how you became our user" => "How you create your id and how you became our user",
        "Enter Your Success Story Here" => "Enter Your Success Story Here",
        "Upload Photo" => "Upload Photo",
        "Marriage Date" => "Marriage Date",
        "Engagement Date" => "Engagement Date",
        "Enter Bride Name" => "Enter Bride Name",
        "Groom Name" => "Groom Name",
        "Groom Id" => "Groom Id",
        "Enter Groom Id" => "Enter Groom Id",
        "Enter Groom Name" => "Enter Groom Name",
        "Bride Name" => "Bride Name",
        "Happy Marriages" => "Happy Marriages",
        "Check it out our success stories who found their life partner here" => "Check it out our success stories who found their life partner here",
        "Success Stories" => "Success Stories",
        "Post your success story" => "Post your success story",
        "Some of our happily married couples story" => "Some of our happily married couples story",
        "Post Success Story" => "Post Success Story",
        "Post your success story here" => "Post your success story here",
        "Bride Id" => "Bride Id",
        "Enter Bride Id" => "Enter Bride Id",
        "Select from our multiple membership plan and find your best life partner with membership benefits" => "Select from our multiple membership plan and find your best life partner with membership benefits",
        "View Plan Detail" => "View Plan Detail",
        "Duration" => "Duration",
        "Messages" => "Messages",
        "Contact Views" => "Contact Views",
        "Live Chat" => "Live Chat",
        "Profile Views" => "Profile Views",
        "You Have Selected" => "You Have Selected",
        "Plan Name" => "Plan Name",
        "Total  Amount" => "Total  Amount",
        "Checkout" => "Checkout",
        "Including all taxes" => "Including all taxes",
        "Feel free to contact us you can ask your questions and query here" => "Feel free to contact us you can ask your questions and query here",
        "Main Branch Address" => "Main Branch Address",
        "Sub Branch Address" => "Sub Branch Address",
        "Ask query or give us feedback" => "Ask query or give us feedback",
        "Full Name" => "Full Name",
        "Enter Your Full Name" => "Enter Your Full Name",
        "Email Id" => "Email Id",
        "Enter Your Email Id Here" => "Enter Your Email Id Here",
        "Contact No" => "Contact No",
        "Enter Your Mobile No" => "Enter Your Mobile No",
        "Description" => "Description",
        "Enter Your Subject Here" => "Enter Your Subject Here",
        "Enter Your Query Here" => "Enter Your Query Here",
        "Enter Captcha" => "Enter Captcha",
        "click here" => "click here",
        "to refresh" => "to refresh",
        "And search your life partner" => "And search your life partner",
        "User Id / Email Id" => "User Id / Email Id",
        "Enter your user id" => "Enter your user id",
        "Keep me logged in" => "Keep me logged in",
        "Login Now" => "Login Now",
        "Resend Email Verification" => "Resend Email Verification",
        "Password" => "Password",
        "Subject" => "Subject",
        "Completing this page will take you closer to your perfect match" => "Completing this page will take you closer to your perfect match",
        "Personal Information" => "Personal Information",
        "You have many matching profiles based on your details. Completing this page will take you closer to your perfect match." => "You have many matching profiles based on your details. Completing this page will take you closer to your perfect match.",
        "Mandatory fields" => "Mandatory fields",
        "Account Information" => "Account Information",
        "First Name" => "First Name",
        "Last Name" => "Last Name",
        "Enter Your Proper Email Id" => "Enter Your Proper Email Id",
        "Enter Your Password" => "Enter Your Password",
        "Confirm Password" => "Confirm Password",
        "Enter Your Confirm Password" => "Enter Your Confirm Password",
        "Mobile No" => "Mobile No",
        "Some Personal Information" => "Some Personal Information",
        "Date Of Birth" => "Date Of Birth",
        "No. of children" => "No. of children",
        "Children Living Status" => "Children Living Status",
        "Willing to marry in other caste?" => "Willing to marry in other caste?",
        "Sub Caste" => "Sub Caste",
        "Country Living In" => "Country Living In",
        "Residing State" => "Residing State",
        "Residing City" => "Residing City",
        "Physical Attributes" => "Physical Attributes",
        "Weight" => "Weight",
        "Body type" => "Body type",
        "Complexion" => "Complexion",
        "Education & Occupation" => "Education & Occupation",
        "Highest Education" => "Highest Education",
        "Additional Degree" => "Additional Degree",
        "Employed in" => "Employed in",
        "Habits" => "Habits",
        "Diet" => "Diet",
        "Smoking" => "Smoking",
        "Drinking" => "Drinking",
        "We suggest our members to please insert your horoscope details even of you dont believe in it because our lots of members interested in this detail" => "We suggest our members to please insert your horoscope details even of you dont believe in it because our lots of members interested in this detail",
        "Star" => "Star",
        "Raasi/Moonsign" => "Raasi/Moonsign",
        "Birth Time" => "Birth Time",
        "Enter Your Birth Place" => "Enter Your Birth Place",
        "Birth Place" => "Birth Place",
        "Family Profile" => "Family Profile",
        "Family status" => "Family status",
        "Family type" => "Family type",
        "Family value" => "Family value",
        "Father Occupation" => "Father Occupation",
        "Enter Father Occupation" => "Enter Father Occupation",
        "Mother Occupation" => "Mother Occupation",
        "Enter Mother Occupation" => "Enter Mother Occupation",
        "No. of Brothers" => "No. of Brothers",
        "Married Brothers" => "Married Brothers",
        "No. of Sisters" => "No. of Sisters",
        "Married Sisters" => "Married Sisters",
        "Something About You" => "Something About You",
        "Write some of about you.for example which kind of person you are ,about your" => "Write some of about you.for example which kind of person you are ,about your",
        "Personality" => "Personality",
        "Hobbies" => "Hobbies",
        "About your family" => "About your family",
        "ect" => "ect",
        "Continue" => "Continue",
        "Mobile Verified Profiles" => "Mobile Verified Profiles",
        "Many Happy Couples" => "Many Happy Couples",
        "Most Trusted Matrimony" => "Most Trusted Matrimony",
        "Tell some details about your life partner" => "Tell some details about your life partner",
        "Tell us which kind of life partner you want to marry and we will find for you.Just fill below details and step closer to your life partner" => "Tell us which kind of life partner you want to marry and we will find for you.Just fill below details and step closer to your life partner",
        "Basic Preference" => "Basic Preference",
        "Eating Habits" => "Eating Habits",
        "Smoking Habits" => "Smoking Habits",
        "Drinking Habits" => "Drinking Habits",
        "Religion Preference" => "Religion Preference",
        "Have Dosh?" => "Have Dosh?",
        "Location Preference" => "Location Preference",
        "Residing city" => "Residing city",
        "Professional Preference" => "Professional Preference",
        "Partner Exceptation" => "Partner Exceptation",
        "Upload Profile Picture" => "Upload Profile Picture",
        "Uploading your profile picture give you 10 time more response" => "Uploading your profile picture give you 10 time more response",
        "Skip" => "Skip",
        "Click on" => "Click on",
        "Select Image" => "Select Image",
        "and then" => "and then",
        "to upload image" => "to upload image",
        "UPLOAD" => "UPLOAD",
        "After select image click on" => "After select image click on",
        "Upload & Continue" => "Upload & Continue",
        "button" => "button",
        "Upload & Continue" => "Upload & Continue",
        "Upload Your Document" => "Upload Your Document",
        "Upload your Id Proof" => "Upload your Id Proof",
        "Upload Your Id Proof" => "Upload Your Id Proof",
        "Note" => "Note",
        "Only .jpg & .png allowded" => "Only .jpg & .png allowded",
        "Select image click on" => "Select image click on",
        "Upload" => "Upload",
        "button and then click on" => "button and then click on",
        "button to upload ID Proof" => "button to upload ID Proof",
        "IMPORTANT" => "IMPORTANT",
        "Verify your email id" => "Verify your email id",
        "NOTE" => "NOTE",
        "Verify your email id by checking email and click on activation link for activating email account.If you dont get verification link please contact us" => "Verify your email id by checking email and click on activation link for activating email account.If you dont get verification link please contact us",
        "Congratulations" => "Congratulations",
        "You are registered user now. Check your registered email id and click on verification link and start serching for your life partner" => "You are registered user now. Check your registered email id and click on verification link and start serching for your life partner",
        "You are registered user now. You can start journey to find your life partner once your profile get approved" => "You are registered user now. You can start journey to find your life partner once your profile get approved",
        "Back to home" => "Back to home",
        "Residing state" => "Residing state",
        "Partner Expectation" => "Partner Expectation",
        "Pending Approval" => "Pending Approval",
        "Change Profile Picture" => "Change Profile Picture",
        "Hello" => "Hello",
        "Your profile is" => "Your profile is",
        "complete" => "complete",
        "Tip : insert all details which can help you to find perfect life partner" => "Tip : insert all details which can help you to find perfect life partner",
        "Complete Your Profile" => "Complete Your Profile",
        "RECENT LOGIN" => "RECENT LOGIN",
        "Send Reminder" => "Send Reminder",
        "Interest Accepted" => "Interest Accepted",
        "Interest Rejected" => "Interest Rejected",
        "Send Interest" => "Send Interest",
        "Enter matri id to search" => "Enter matri id to search",
        "MESSAGES" => "MESSAGES",
        "Inbox" => "Inbox",
        "Outbox" => "Outbox",
        "MY PROFILE " => "MY PROFILE",
        "Manage Photos" => "Manage Photos",
        "PROFILE DETAILS" => "PROFILE DETAILS",
        "Express Interest Received" => "Express Interest Received",
        "My Shortlist Profile" => "My Shortlist Profile",
        "My Blocklist Profile" => "My Blocklist Profile",
        "Mobile Numbers Viewed By Me" => "Mobile Numbers Viewed By Me",
        "Photo Password Request Received" => "Photo Password Request Received",
        "SAVED SEARCHES" => "SAVED SEARCHES",
        "Help And Support" => "Help And Support",
        "Refund Policy" => "Refund Policy",
        "Terms & Policy" => "Terms & Policy",
        "Terms & Conditions" => "Terms & Conditions",
        "Privacy Policy" => "Privacy Policy",
        "Report Misuse" => "Report Misuse",
        "Need Help?" => "Need Help?",
        "Register" => "Register",
        "Information" => "Information",
        "About Us" => "About Us",
        "Join us on social" => "Join us on social",
        "All Rights Reserved By" => "All Rights Reserved By",
        "RECENTLY VISITED" => "RECENTLY VISITED",
        "RECENTLY JOINED " => "RECENTLY JOINED ",
        "FEATURED PROFILES" => "FEATURED PROFILES",
        "MY MATCHES" => "MY MATCHES",
        "This is your all profile detail which you added.You can view your all details and also can edit all your detail from here" => "This is your all profile detail which you added.You can view your all details and also can edit all your detail from here",
        "Edit your profile details is very easy just click on the left pencil button" => "Edit your profile details is very easy just click on the left pencil button",
        "and here we go. You can edit your profile detail" => "and here we go. You can edit your profile detail",
        "Edit Profile Picture" => "Edit Profile Picture",
        "Send Message" => "Send Message",
        "View Photos" => "View Photos",
        "Sent" => "Sent",
        "Interest" => "Interest",
        "Received" => "Received",
        "Photo Request" => "Photo Request",
        "INTEREST" => "INTEREST",
        "PHOTO REQUEST" => "PHOTO REQUEST",
        "Partner Preference" => "Partner Preference",
        "Enter Mobile No" => "Enter Mobile No",
        "Edit Mobile No" => "Edit Mobile No",
        "Basic Details" => "Basic Details",
        "EDIT MOBILE NO" => "EDIT MOBILE NO",
        "Marital Status" => "Marital Status",
        "No Of Children" => "No Of Children",
        "About Me" => "About Me",
        "Religion Information" => "Religion Information",
        "Will to marry in other caste?" => "Will to marry in other caste?",
        "Body Type" => "Body Type",
        "Habits And Hobbies" => "Habits And Hobbies",
        "Education / Profession Information" => "Education / Profession Information",
        "Family Details" => "Family Details",
        "Family Type" => "Family Type",
        "Family Status" => "Family Status",
        "Family Value" => "Family Value",
        "Location Information" => "Location Information",
        "State Living In" => "State Living In",
        "City Living In" => "City Living In",
        "Horoscope Information" => "Horoscope Information",
        "Dosh Type" => "Dosh Type",
        "Moonsign" => "Moonsign",
        "Expectations" => "Expectations",
        "Patner Expectation" => "Patner Expectation",
        "Basic Preferences" => "Basic Preferences",
        "Education / Professional Preference" => "Education / Professional Preference",
        "Moonsign" => "Moonsign",
        "State" => "State",
        "City" => "City",
        "EDIT" => "EDIT",
        "User Name" => "User Name",
        "Email" => "Email",
        "Approval Status" => "Approval Status",
        "Willing To marry in other caste?" => "Willing To marry in other caste?",
        "Physical Status" => "Physical Status",
        "Options" => "Options",
        "Saved Searches" => "Saved Searches",
        "All saved searches are here, with single button click you can search profile on behalf of your search criteria" => "All saved searches are here, with single button click you can search profile on behalf of your search criteria",
        "Message" => "Message",
        "You can see all of your inbox messages here" => "You can see all of your inbox messages here",
        "Read" => "Read",
        "Unreaded" => "Unreaded",
        "All" => "All",
        "Reply" => "Reply",
        "Forward" => "Forward",
        "Mark As Important" => "Mark As Important",
        "Delete" => "Delete",
        "Search Message By Matri Id" => "Search Message By Matri Id",
        "Check all your sent messages from here" => "Check all your sent messages from here",
        "Important" => "Important",
        "Select" => "Select",
        "Actions" => "Actions",
        "Check your all important message here" => "Check your all important message here",
        "All Express Interest" => "All Express Interest",
        "Here you can see your all express interest which you send and received from members.and with left side panel you can access other particluar express interest" => "Here you can see your all express interest which you send and received from members.and with left side panel you can access other particluar express interest",
        "All Sent Interest" => "All Sent Interest",
        "All Received Interest" => "All Received Interest",
        "Accept" => "Accept",
        "Express Interest Sent" => "Express Interest Sent",
        "Reminder" => "Reminder",
        "Accepted" => "Accepted",
        "Rejected" => "Rejected",
        "Decline" => "Decline",
        "Express Interest Received Accepted" => "Express Interest Received Accepted",
        "Here you can see your all express interest which you received from members and accepted by you" => "Here you can see your all express interest which you received from members and accepted by you",
        "Delete" => "Delete",
        "Express Interest Received Pending" => "Express Interest Received Pending",
        "Here you can see your all express interest which you received from members and waiting for response from you" => "Here you can see your all express interest which you received from members and waiting for response from you",
        "Express Interest Received Rejected" => "Express Interest Received Rejected",
        "Here you can see your all express interest which you received from members and rejected by you" => "Here you can see your all express interest which you received from members and rejected by you",
        "Interest Received Pending" => "Interest Received Pending",
        "Interest Received Accepted" => "Interest Received Accepted",
        "Interest Received Rejected" => "Interest Received Rejected",
        "Interest Sent Accepted" => "Interest Sent Accepted",
        "Interest Sent Rejected" => "Interest Sent Rejected",
        "Interest Sent Pending" => "Interest Sent Pending",
        "Express Interest Sent Accepted" => "Express Interest Sent Accepted",
        "Here you can see your all express interest which you send to members.and with left side panel you can access other particluar express interest" => "Here you can see your all express interest which you send to members.and with left side panel you can access other particluar express interest",
        "Express Interest Sent Pending" => "Express Interest Sent Pending",
        "Here you can see your all express interest which you sent to members and not yet responded" => "Here you can see your all express interest which you sent to members and not yet responded",
        "Express Interest Sent Rejected" => "Express Interest Sent Rejected",
        "Here you can see your all express interest which you send and rejcted by members" => "Here you can see your all express interest which you send and rejcted by members",
        "MY PROFILE" => "MY PROFILE",
        "Upload & Profile Picture Settings" => "Upload & Profile Picture Settings",
        "Here is your option to set your profile pictures and other pictures.Remember upload profile picture gives you 10 times better respose.So do it now if you didnt" => "Here is your option to set your profile pictures and other pictures.Remember upload profile picture gives you 10 times better respose.So do it now if you didnt",
        "Change Or Upload Profile Picture" => "Change Or Upload Profile Picture",
        "Delete Profile Picture" => "Delete Profile Picture",
        "Upload More Photos" => "Upload More Photos",
        "Photo" => "Photo",
        "Edit Photo" => "Edit Photo",
        "Delete Photo" => "Delete Photo",
        "Select image and then click on submit button to upload image" => "Select image and then click on submit button to upload image",
        "FAQ" => "FAQ",
        "Upload Horoscope" => "Upload Horoscope",
        "Here is your option to set your Horoscope.Upload your horoscope image(kundli) may be you not believe but other user does" => "Here is your option to set your Horoscope.Upload your horoscope image(kundli) may be you not believe but other user does",
        "Change Or Upload Horoscope" => "Change Or Upload Horoscope",
        "Upload Your Horoscope Image" => "Upload Your Horoscope Image",
        "Change Horoscope" => "Change Horoscope",
        "To get verified Upload document below" => "To get verified Upload document below",
        "Status" => "Status",
        "Uploading document will get your profile approval of authentication" => "Uploading document will get your profile approval of authentication",
        "Document – Upload / Edit Details" => "Document – Upload / Edit Details",
        "One Way Match" => "One Way Match",
        "One way match is the profile show in perticular criteria at its best.its help you to find out your life partner easily" => "One way match is the profile show in perticular criteria at its best.its help you to find out your life partner easily",
        "Two way match is the profile show in perticular criteria at its best.its help you to find out your life partner easily" => "Two way match is the profile show in perticular criteria at its best.its help you to find out your life partner easily",
        "Two Way Match" => "Two Way Match",
        "Broader match is the profile show in perticular criteria at its best.its help you to find out your life partner easily" => "Broader match is the profile show in perticular criteria at its best.its help you to find out your life partner easily",
        "Broader Match" => "Broader Match",
        "Preferred Match" => "Preferred Match",
        "Preferred match is the profile show in perticular criteria at its best.its help you to find out your life partner easily" => "Preferred match is the profile show in perticular criteria at its best.its help you to find out your life partner easily",
        "Custom Match" => "Custom Match",
        "In custom match you can set criteria to get auto results" => "In custom match you can set criteria to get auto results",
        "Set Custom Match" => "Set Custom Match",
        "Create Your Custom Match" => "Create Your Custom Match",
        "Looking For" => "Looking For",
        "Save & Search" => "Save & Search",
        "Your Custom Match Result" => "Your Custom Match Result",
        "Profile View" => "Profile View",
        "Contact View" => "Contact View",
        "Current Plan Details" => "Current Plan Details",
        "You can check your current membership plan detail and also recommanded plan suggestion with that" => "You can check your current membership plan detail and also recommanded plan suggestion with that",
        "Shortlisted Member Profile" => "Shortlisted Member Profile",
        "You can check all of your shortlisted members list here" => "You can check all of your shortlisted members list here",
        "Blocklisted Member Profile" => "Blocklisted Member Profile",
        "You can check all of your blocklisted members list here" => "You can check all of your blocklisted members list here",
        "You can check all of your My Profile Viewed By members list here" => "You can check all of your My Profile Viewed By members list here",
        "You can check all of your Visited Profile list here" => "You can check all of your Visited Profile list here",
        "Who watched my mobile number" => "Who watched my mobile number",
        "You can check all of Who watched my mobile number list here" => "You can check all of Who watched my mobile number list here",
        "With this tabs(photo req sent and photo req received) you can check all your photo req send and received" => "With this tabs(photo req sent and photo req received) you can check all your photo req send and received",
        "Photo Privacy" => "Photo Privacy",
        "You can set you photo privacy with our settings tab" => "You can set you photo privacy with our settings tab",
        "Change Photo Settings" => "Change Photo Settings",
        "Photo Request Received" => "Photo Request Received",
        "Photo Request Sent" => "Photo Request Sent",
        "All Settings" => "All Settings",
        "Here is all of your settings you can set your privacy as you want" => "Here is all of your settings you can set your privacy as you want",
        "Blacklist" => "Blacklist",
        "Contact show setting" => "Contact show setting",
        "You can set you photo privacy from here,so can manage who can see your photos" => "You can set you photo privacy from here,so can manage who can see your photos",
        "Current Status" => "Current Status",
        "Show To All" => "Show To All",
        "Edit" => "Edit",
        "Set Password for protect photo" => "Set Password for protect photo",
        "Change Password for protect photo" => "Change Password for protect photo",
        "Remove Password from protect photo" => "Remove Password from protect photo",
        "Set Photo Password" => "Set Photo Password",
        "Blocked Members List" => "Blocked Members List",
        "You can see all blocked members list here and you also can block directly from here" => "You can see all blocked members list here and you also can block directly from here",
        "Enter User Id Or Email Id" => "Enter User Id Or Email Id",
        "Block" => "Block",
        "Contact show setting option gives you access to set privacy for your contact detail" => "Contact show setting option gives you access to set privacy for your contact detail",
        "Show To Express Interest Accepted Paid Member" => "Show To Express Interest Accepted Paid Member",
        "Have any privacy concern ? You can easily change your account password from here" => "Have any privacy concern ? You can easily change your account password from here",
        "Enter Old Password" => "Enter Old Password",
        "Enter New Password" => "Enter New Password",
        "Confirm New Password" => "Confirm New Password",
        "List Of Blocked Members" => "List Of Blocked Members",
        "You can see and unblock members from this list from here" => "You can see and unblock members from this list from here",
        "Location" => "Location",
        "Unblock" => "Unblock",
        "Visible To All Members" => "Visible To All Members",
        "Visible To Paid Members" => "Visible To Paid Members",
        "Hidden For All" => "Hidden For All",
        "Show To Express Interest" => "Show To Express Interest",
        "Accepted Paid Member" => "Accepted Paid Member",
        "Show To Paid Members" => "Show To Paid Members",
        "Don't have password" => "Don't have password",
        "Enter Password" => "Enter Password",
        "The Photo has been protected by the owner of this profile. Members are given the feature to protect their Photo from viewing by anyone. If the Photo is protected, then you need a Photo Password to view it. " => "The Photo has been protected by the owner of this profile. Members are given the feature to protect their Photo from viewing by anyone. If the Photo is protected, then you need a Photo Password to view it. ",
        "View Protected Photo" => "View Protected Photo",
        "Photo not yet uploaded or approved" => "Photo not yet uploaded or approved",
        "Photo of" => "Photo of",
        "FEEDBACK / SUGGESTIONS" => "FEEDBACK / SUGGESTIONS",
        "Give us your valuable feedback to make website more user friendly" => "Give us your valuable feedback to make website more user friendly",
        "Back To" => "Back To",
        "From" => "From",
        "Sent on" => "Sent on",
        "Received on" => "Received on",
        "Enter OTP for login" => "Enter OTP for login",
        "Not received OTP yet?" => "Not received OTP yet?",
        "Send OTP Again" => "Send OTP Again",
        "Back To Login" => "Back To Login",
        "Mobile No Verfication" => "Mobile No Verfication",
        "Verify your mobile number now to activate your profile" => "Verify your mobile number now to activate your profile",
        "It is mandatory to verify your mobile number otherwise your profile will not be displayed to other members" => "It is mandatory to verify your mobile number otherwise your profile will not be displayed to other members",
        "Verify mobile number through SMS" => "Verify mobile number through SMS",
        "An SMS with verification PIN has been sent to" => "An SMS with verification PIN has been sent to",
        "Verify" => "Verify",
        "Not received verification code yet?" => "Not received verification code yet?",
        "Skip mobile verification" => "Skip mobile verification",
        "Congratulations" => "Congratulations",
        "You are our paid member now.You can access our membership benifits now" => "You are our paid member now.You can access our membership benifits now",
        "Go To Home" => "Go To Home",
        "Your Selected Plan" => "Your Selected Plan",
        "Duration" => "Duration",
        "Payment Not Successful" => "Payment Not Successful",
        "Payment not successful.If payment is debited please contact us" => "Payment not successful.If payment is debited please contact us",
        "Payment Options" => "Payment Options",
        "Pay fast and securly with our multiple payment option" => "Pay fast and securly with our multiple payment option",
        "Select your payment options to pay" => "Select your payment options to pay",
        "Pay using razorpay" => "Pay using razorpay",
        "Amount" => "Amount",
        "Bank Name" => "Bank Name",
        "Bank Account Type" => "Bank Account Type",
        "Bank Account Name" => "Bank Account Name",
        "Bank Account No" => "Bank Account No",
        "Bank IFSC Code" => "Bank IFSC Code",
        "Plan" => "Plan",
        "Enter Email ID or User Id" => "Enter Email ID or User Id",
        "Enter your email id or user id" => "Enter your email id or user id",
        "Please verify your Email ID" => "Please verify your Email ID",
        "It is mandatory that you verify your mobile number else your profile will not be displayed to other members" => "It is mandatory that you verify your mobile number else your profile will not be displayed to other members",
        "Refine Search" => "Refine Search",
        "Your search results" => "Your search results",
        "We found your profile to be a good match. Please send me Photo password to proceed further." => "We found your profile to be a good match. Please send me Photo password to proceed further.",
        "I am interested in your profile. I would like to view photo now, send me password." => "I am interested in your profile. I would like to view photo now, send me password.",
        "Click here if you have password" => "Click here if you have password",
        "Upgrade Your Membership" => "Upgrade Your Membership",
        "Please get the send message balance by upgrading your membership" => "Please get the send message balance by upgrading your membership",
        "Upgrade Now" => "Upgrade Now",
        "Please get the send sms balance by upgrading your membership." => "Please get the send sms balance by upgrading your membership.",
        "Compose" => "Compose",
        "Sent message to members from here." => "Sent message to members from here.",
        "Spotlight Profile" => "Spotlight Profile",
        "Blue color profiles are spotlight profile. Which always show on the top of all result." => "Blue color profiles are spotlight profile. Which always show on the top of all result.",
        "Profiles found" => "Profiles found",
        "Blue Header profile is are spotlight profile which was showing top of the search result.Its gives 10 times faster results." => "Blue Header profile is are spotlight profile which was showing top of the search result.Its gives 10 times faster results.",
        "Add To Saved Search" => "Add To Saved Search",
        "Grid View" => "Grid View",
        "List View" => "List View",
        "Blue color profile is are spotlight profile which was showing top of the search result.Its gives 10 times faster results." => "Blue color profile is are spotlight profile which was showing top of the search result.Its gives 10 times faster results.",
        "Saved Search" => "Saved Search",
        "Clear" => "Clear",
        "Latest Register Profile" => "Latest Register Profile",
        "Profile Type" => "Profile Type",
        "Name" => "Name",
        "About me is under approval or Unapproved." => "About me is under approval or Unapproved.",
        "Send Express Interest" => "Send Express Interest",
        "View Contact Details" => "View Contact Details",
        "Send Personal Message" => "Send Personal Message",
        "Add to Blocklist" => "Add to Blocklist",
        "Remove Blocklist" => "Remove Blocklist",
        "Add to Shortlist" => "Add to Shortlist",
        "Remove From Shortlist" => "Remove From Shortlist",
        "View Horoscope" => "View Horoscope",
        "Your profile matches with" => "Your profile matches with",
        "preferences" => "preferences",
        "Education / Profession Preference" => "Education / Profession Preference",
        "Moonsign (Raasi)" => "Moonsign (Raasi)",
        "Partner Expectation is under approval or Unapproved." => "Partner Expectation is under approval or Unapproved.",
        "Photo Album Of" => "Photo Album Of",
        "QUICK SEARCH" => "QUICK SEARCH",
        "Register On" => "Register On",
        "You are Blocked" => "You are Blocked",
        "This member has blocked you.You can't see his contact details." => "This member has blocked you.You can't see his contact details.",
        "User Blocked by You." => "User Blocked by You.",
        "Member is blocked by you,if you wish to see contact details please unblock first." => "Member is blocked by you,if you wish to see contact details please unblock first.",
        "Unblock Now" => "Unblock Now",
        "Remaining Contacts" => "Remaining Contacts",
        "Matri ID" => "Matri ID",
        "Date of Birth" => "Date of Birth",
        "Time of Birth" => "Time of Birth",
        "Raasi" => "Raasi",
        "Star" => "Star",
        "Only for express interest accepted" => "Only for express interest accepted",
        "This member only shows his/her contact details if you have already sent him/her express interest, and he/she has accepted it." => "This member only shows his/her contact details if you have already sent him/her express interest, and he/she has accepted it.",
        "Please send him/her express interest if you are interested." => "Please send him/her express interest if you are interested.",
        "Please get the contact view balance by upgrading your membership." => "Please get the contact view balance by upgrading your membership.",
        "Please Login" => "Please Login",
        "Please Login to access this feature." => "Please Login to access this feature.",
        "Your Contact Balance" => "Your Contact Balance",
        "You want to see contact Details?" => "You want to see contact Details?",
        "Yes" => "Yes",
        "No" => "No",
        "completed" => "completed",
        "Your Profile Is" => "Your Profile Is",
        "Complete (success)" => "Complete (success)",
        "Photo password request of" => "Photo password request of",
        "Your Photo Password has been successfully sent to requester's email id" => "Your Photo Password has been successfully sent to requester's email id",
        "This member has blocked you.You can't express your interest." => "This member has blocked you.You can't express your interest.",
        "Express Interest" => "Express Interest",
        "I am interested in your profile. Please Accept if you are interested." => "I am interested in your profile. Please Accept if you are interested.",
        "You are the kind of person we have been looking for. Please respond to proceed further." => "You are the kind of person we have been looking for. Please respond to proceed further.",
        "We liked your profile and interested to take it forward. Please reply at the earliest." => "We liked your profile and interested to take it forward. Please reply at the earliest.",
        "You seem to be the kind of person who suits our family. We would like to contact your parents to proceed further." => "You seem to be the kind of person who suits our family. We would like to contact your parents to proceed further.",
        "You profile matches my sister's/brother's profile. Please 'Accept' if you are interested." => "You profile matches my sister's/brother's profile. Please 'Accept' if you are interested.",
        "Our child's profile seems to match. Please reply to proceed further." => "Our child's profile seems to match. Please reply to proceed further.",
        "We find a good life partner in you for our friend. Please reply to proceed further." => "We find a good life partner in you for our friend. Please reply to proceed further.",
        "Date" => "Date",
        "You are not a paid member, Please upgrade your membership to express the interest." => "You are not a paid member, Please upgrade your membership to express the interest.",
        "Select your language" => "Select your language",   
	);
?>