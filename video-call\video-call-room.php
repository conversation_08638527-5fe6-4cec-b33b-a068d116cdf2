<?php
session_start();
include_once '../databaseConn.php';
include_once '../class/Config.class.php';
include_once 'agora-config.php';

$DatabaseCo = new DatabaseConn();
$configObj = new Config();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$userId = $_SESSION['user_id'];
$callId = $_GET['call_id'] ?? 0;
$channelName = $_GET['channel'] ?? '';
$isIncoming = $_GET['incoming'] ?? false;

// Validate call
if (empty($callId) || empty($channelName)) {
    echo "<script>alert('Invalid call parameters'); window.close();</script>";
    exit();
}

// Get call details
$callQuery = $DatabaseCo->dbLink->query("
    SELECT vc.*, 
           r1.username as caller_name, r1.photo1 as caller_photo,
           r2.username as receiver_name, r2.photo1 as receiver_photo
    FROM video_calls vc
    LEFT JOIN register r1 ON vc.caller_id = r1.matri_id
    LEFT JOIN register r2 ON vc.receiver_id = r2.matri_id
    WHERE vc.call_id = $callId
");

if (!$callQuery || !($callData = mysqli_fetch_object($callQuery))) {
    echo "<script>alert('Call not found'); window.close();</script>";
    exit();
}

// Determine if current user is caller or receiver
$isCaller = ($callData->caller_id == $userId);
$otherUser = $isCaller ? 
    ['id' => $callData->receiver_id, 'name' => $callData->receiver_name, 'photo' => $callData->receiver_photo] :
    ['id' => $callData->caller_id, 'name' => $callData->caller_name, 'photo' => $callData->caller_photo];

// Check user permissions
$permissions = AgoraConfig::canMakeVideoCall($userId, $otherUser['id']);
if (!$permissions['allowed']) {
    echo "<script>alert('{$permissions['reason']}'); window.close();</script>";
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Call - <?php echo $configObj->getConfigTitle(); ?></title>
    
    <!-- Agora SDK -->
    <script src="https://download.agora.io/sdk/release/AgoraRTC_N-4.19.0.js"></script>
    
    <!-- Bootstrap CSS -->
    <link href="../css/bootstrap.css" rel="stylesheet">
    <link href="../css/video-call.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        .video-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: #000;
        }
        
        .remote-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .local-video {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 200px;
            height: 150px;
            border-radius: 10px;
            border: 2px solid #fff;
            object-fit: cover;
            z-index: 10;
        }
        
        .call-controls {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 20;
        }
        
        .control-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-mute {
            background: #4CAF50;
        }
        
        .btn-mute.muted {
            background: #f44336;
        }
        
        .btn-video {
            background: #2196F3;
        }
        
        .btn-video.disabled {
            background: #f44336;
        }
        
        .btn-end {
            background: #f44336;
        }
        
        .call-info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 15;
        }
        
        .call-timer {
            font-size: 18px;
            font-weight: bold;
        }
        
        .user-info {
            margin-top: 10px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .incoming-call {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 30;
        }
        
        .incoming-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin-bottom: 20px;
        }
        
        .incoming-name {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .incoming-text {
            font-size: 16px;
            margin-bottom: 40px;
            opacity: 0.8;
        }
        
        .incoming-controls {
            display: flex;
            gap: 40px;
        }
        
        .btn-accept {
            background: #4CAF50;
        }
        
        .btn-reject {
            background: #f44336;
        }
        
        .connection-status {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
            z-index: 25;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="video-container">
        <!-- Incoming Call Screen -->
        <?php if ($isIncoming && $callData->call_status === 'initiated'): ?>
        <div id="incoming-call" class="incoming-call">
            <img src="../my_photos/<?php echo $otherUser['photo'] ?: 'default-avatar.png'; ?>" 
                 alt="<?php echo $otherUser['name']; ?>" class="incoming-avatar">
            <div class="incoming-name"><?php echo $otherUser['name']; ?></div>
            <div class="incoming-text">Incoming video call...</div>
            <div class="incoming-controls">
                <button class="control-btn btn-accept" onclick="acceptCall()">
                    <i class="fas fa-phone"></i>
                </button>
                <button class="control-btn btn-reject" onclick="rejectCall()">
                    <i class="fas fa-phone-slash"></i>
                </button>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Connection Status -->
        <div id="connection-status" class="connection-status hidden">
            <div class="spinner-border text-light" role="status">
                <span class="sr-only">Connecting...</span>
            </div>
            <div style="margin-top: 10px;">Connecting to call...</div>
        </div>
        
        <!-- Video Elements -->
        <div id="remote-video-container">
            <!-- Remote video will be inserted here -->
        </div>
        
        <div id="local-video-container">
            <!-- Local video will be inserted here -->
        </div>
        
        <!-- Call Info -->
        <div class="call-info">
            <div class="call-timer" id="call-timer">00:00</div>
            <div class="user-info">
                <img src="../my_photos/<?php echo $otherUser['photo'] ?: 'default-avatar.png'; ?>" 
                     alt="<?php echo $otherUser['name']; ?>" class="user-avatar">
                <span><?php echo $otherUser['name']; ?></span>
            </div>
        </div>
        
        <!-- Call Controls -->
        <div class="call-controls">
            <button class="control-btn btn-mute" id="mute-btn" onclick="toggleMute()">
                <i class="fas fa-microphone"></i>
            </button>
            <button class="control-btn btn-video" id="video-btn" onclick="toggleVideo()">
                <i class="fas fa-video"></i>
            </button>
            <button class="control-btn btn-end" onclick="endCall()">
                <i class="fas fa-phone-slash"></i>
            </button>
        </div>
    </div>

    <script>
        // Call configuration
        const callConfig = {
            callId: <?php echo $callId; ?>,
            channelName: '<?php echo $channelName; ?>',
            userId: '<?php echo $userId; ?>',
            otherUserId: '<?php echo $otherUser['id']; ?>',
            isCaller: <?php echo $isCaller ? 'true' : 'false'; ?>,
            isIncoming: <?php echo $isIncoming ? 'true' : 'false'; ?>,
            maxDuration: <?php echo $permissions['duration']; ?>,
            appId: '<?php echo AgoraConfig::getAppId(); ?>'
        };
        
        // Agora client
        let agoraClient;
        let localAudioTrack;
        let localVideoTrack;
        let remoteUsers = {};
        
        // Call state
        let callStartTime;
        let callTimer;
        let isAudioMuted = false;
        let isVideoEnabled = true;
        let callToken = '';
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            if (callConfig.isIncoming && document.getElementById('incoming-call')) {
                // Wait for user to accept/reject
                return;
            }
            
            initializeCall();
        });
        
        /**
         * Initialize video call
         */
        async function initializeCall() {
            try {
                showConnectionStatus(true);
                
                // Get token from server
                const tokenResponse = await fetch('agora-token-server.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'generate_token',
                        channel: callConfig.channelName,
                        uid: callConfig.userId
                    })
                });
                
                const tokenData = await tokenResponse.json();
                if (!tokenData.success) {
                    throw new Error(tokenData.message);
                }
                
                callToken = tokenData.data.token;
                
                // Initialize Agora client
                agoraClient = AgoraRTC.createClient({mode: "rtc", codec: "vp8"});
                
                // Add event listeners
                agoraClient.on("user-published", handleUserPublished);
                agoraClient.on("user-unpublished", handleUserUnpublished);
                agoraClient.on("user-left", handleUserLeft);
                
                // Join channel
                await agoraClient.join(callConfig.appId, callConfig.channelName, callToken, callConfig.userId);
                
                // Create and publish local tracks
                await createLocalTracks();
                await agoraClient.publish([localAudioTrack, localVideoTrack]);
                
                showConnectionStatus(false);
                startCallTimer();
                
            } catch (error) {
                console.error('Failed to initialize call:', error);
                alert('Failed to connect to call: ' + error.message);
                window.close();
            }
        }
        
        /**
         * Create local audio and video tracks
         */
        async function createLocalTracks() {
            try {
                localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
                localVideoTrack = await AgoraRTC.createCameraVideoTrack();
                
                // Play local video
                const localVideoElement = document.createElement('video');
                localVideoElement.className = 'local-video';
                localVideoElement.autoplay = true;
                localVideoElement.muted = true;
                
                localVideoTrack.play(localVideoElement);
                document.getElementById('local-video-container').appendChild(localVideoElement);
                
            } catch (error) {
                console.error('Failed to create local tracks:', error);
                throw error;
            }
        }
        
        /**
         * Handle remote user published
         */
        async function handleUserPublished(user, mediaType) {
            remoteUsers[user.uid] = user;
            await agoraClient.subscribe(user, mediaType);
            
            if (mediaType === 'video') {
                const remoteVideoElement = document.createElement('video');
                remoteVideoElement.className = 'remote-video';
                remoteVideoElement.autoplay = true;
                
                user.videoTrack.play(remoteVideoElement);
                document.getElementById('remote-video-container').appendChild(remoteVideoElement);
            }
            
            if (mediaType === 'audio') {
                user.audioTrack.play();
            }
        }
        
        /**
         * Handle remote user unpublished
         */
        function handleUserUnpublished(user, mediaType) {
            if (mediaType === 'video') {
                const remoteVideo = document.querySelector('.remote-video');
                if (remoteVideo) {
                    remoteVideo.remove();
                }
            }
        }
        
        /**
         * Handle remote user left
         */
        function handleUserLeft(user) {
            delete remoteUsers[user.uid];
            const remoteVideo = document.querySelector('.remote-video');
            if (remoteVideo) {
                remoteVideo.remove();
            }
        }
        
        /**
         * Accept incoming call
         */
        async function acceptCall() {
            try {
                // Update call status on server
                await fetch('agora-token-server.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'accept_call',
                        call_id: callConfig.callId,
                        receiver_id: callConfig.userId
                    })
                });
                
                // Hide incoming call screen
                document.getElementById('incoming-call').classList.add('hidden');
                
                // Initialize call
                await initializeCall();
                
            } catch (error) {
                console.error('Failed to accept call:', error);
                alert('Failed to accept call');
            }
        }
        
        /**
         * Reject incoming call
         */
        async function rejectCall() {
            try {
                await fetch('agora-token-server.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'reject_call',
                        call_id: callConfig.callId,
                        reason: 'rejected'
                    })
                });
                
                window.close();
                
            } catch (error) {
                console.error('Failed to reject call:', error);
                window.close();
            }
        }
        
        /**
         * End call
         */
        async function endCall() {
            try {
                const duration = callStartTime ? Math.floor((Date.now() - callStartTime) / 1000) : 0;
                
                // Update call status on server
                await fetch('agora-token-server.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'end_call',
                        call_id: callConfig.callId,
                        duration: duration
                    })
                });
                
                // Clean up
                if (localAudioTrack) {
                    localAudioTrack.close();
                }
                if (localVideoTrack) {
                    localVideoTrack.close();
                }
                if (agoraClient) {
                    await agoraClient.leave();
                }
                
                window.close();
                
            } catch (error) {
                console.error('Failed to end call:', error);
                window.close();
            }
        }
        
        /**
         * Toggle audio mute
         */
        async function toggleMute() {
            if (localAudioTrack) {
                await localAudioTrack.setEnabled(!isAudioMuted);
                isAudioMuted = !isAudioMuted;
                
                const muteBtn = document.getElementById('mute-btn');
                const icon = muteBtn.querySelector('i');
                
                if (isAudioMuted) {
                    muteBtn.classList.add('muted');
                    icon.className = 'fas fa-microphone-slash';
                } else {
                    muteBtn.classList.remove('muted');
                    icon.className = 'fas fa-microphone';
                }
            }
        }
        
        /**
         * Toggle video
         */
        async function toggleVideo() {
            if (localVideoTrack) {
                await localVideoTrack.setEnabled(!isVideoEnabled);
                isVideoEnabled = !isVideoEnabled;
                
                const videoBtn = document.getElementById('video-btn');
                const icon = videoBtn.querySelector('i');
                
                if (!isVideoEnabled) {
                    videoBtn.classList.add('disabled');
                    icon.className = 'fas fa-video-slash';
                } else {
                    videoBtn.classList.remove('disabled');
                    icon.className = 'fas fa-video';
                }
            }
        }
        
        /**
         * Start call timer
         */
        function startCallTimer() {
            callStartTime = Date.now();
            callTimer = setInterval(updateCallTimer, 1000);
            
            // Auto-end call when max duration is reached
            setTimeout(() => {
                alert('Call time limit reached');
                endCall();
            }, callConfig.maxDuration * 1000);
        }
        
        /**
         * Update call timer display
         */
        function updateCallTimer() {
            if (!callStartTime) return;
            
            const elapsed = Math.floor((Date.now() - callStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            
            document.getElementById('call-timer').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        /**
         * Show/hide connection status
         */
        function showConnectionStatus(show) {
            const statusElement = document.getElementById('connection-status');
            if (show) {
                statusElement.classList.remove('hidden');
            } else {
                statusElement.classList.add('hidden');
            }
        }
        
        // Handle page unload
        window.addEventListener('beforeunload', function() {
            if (callTimer) {
                clearInterval(callTimer);
            }
            endCall();
        });
    </script>
</body>
</html>
