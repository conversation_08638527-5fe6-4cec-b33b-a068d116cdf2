<?php
include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();
$getAgeQry = $DatabaseCo->dbLink->query("SELECT * FROM `age`");
if(mysqli_num_rows($getAgeQry) > 0){
	$count=0;
	while($contact_res = mysqli_fetch_object($getAgeQry)){
		$count++;
		$response = array('id' => $contact_res->id,'age' => $contact_res->age,'status'=>"1");
		$data["response"][] = $response; 
	}
	echo json_encode($data);
	exit;
}else{
	$data['status'] = "0";
	echo json_encode($data);
	exit;
}
?>
