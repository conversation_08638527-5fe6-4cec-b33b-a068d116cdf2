# 📹 **VIDEO CALLING INTEGRATION GUIDE**

## 🚀 **Quick Setup Instructions**

### **1. Database Setup**
Run the SQL schema to create video calling tables:
```bash
mysql -u username -p database_name < database/video_calls_schema.sql
```

### **2. Agora.io Account Setup**
1. Sign up at [Agora.io](https://www.agora.io/)
2. Create a new project
3. Get your App ID and App Certificate
4. Update `video-call/agora-config.php` with your credentials:
```php
private static $APP_ID = 'YOUR_AGORA_APP_ID';
private static $APP_CERTIFICATE = 'YOUR_AGORA_APP_CERTIFICATE';
```

### **3. Include Video Call Assets**
Add to your main layout files:

**In `<head>` section:**
```html
<!-- Agora SDK -->
<script src="https://download.agora.io/sdk/release/AgoraRTC_N-4.19.0.js"></script>

<!-- Video Call CSS -->
<link href="css/video-call.css" rel="stylesheet">
```

**Before closing `</body>` tag:**
```html
<!-- Video Call JavaScript -->
<script src="js/video-call.js"></script>
```

### **4. Add Video Call Buttons to Profile Pages**

**Example integration in profile view:**
```php
<?php if($user_can_make_calls): ?>
<button class="btn btn-primary video-call-btn" data-user-id="<?php echo $profile_user_id; ?>">
    <i class="fas fa-video"></i> Video Call
</button>
<?php endif; ?>
```

### **5. Update User Session**
Make sure user ID is available in JavaScript:
```html
<script>
window.currentUserId = '<?php echo $_SESSION['user_id']; ?>';
</script>
```

## 🔧 **Configuration Options**

### **Call Duration Limits**
Edit in `video-call/agora-config.php`:
```php
private static $MAX_CALL_DURATION_FREE = 300;  // 5 minutes for free users
private static $MAX_CALL_DURATION_PAID = 3600; // 60 minutes for paid users
```

### **Daily Call Limits**
Free users: 3 calls per day
Paid users: Unlimited

### **Call Permissions**
- Free users: Basic video calling (5 min limit)
- Paid users: HD video calling (60 min limit)
- Blocked users: Cannot call each other
- DND mode: Automatically rejects calls

## 📱 **Features Included**

### **✅ Core Features**
- One-to-one video calling
- Audio mute/unmute
- Video enable/disable
- Call duration tracking
- Call history
- Incoming call notifications
- Call permissions based on subscription

### **✅ Advanced Features**
- Call quality indicators
- Do Not Disturb mode
- Call statistics
- Daily call limits
- Auto-reject for blocked users
- Call feedback system
- Missed call notifications

### **✅ Security Features**
- Token-based authentication
- Call permission validation
- User blocking support
- Secure channel generation

## 🎯 **Usage Examples**

### **1. Basic Video Call Button**
```html
<button class="video-call-btn" data-user-id="USER123">
    <i class="fas fa-video"></i> Video Call
</button>
```

### **2. Check Call Permissions**
```javascript
const canCall = await videoCallManager.canMakeVideoCalls();
if (canCall.allowed) {
    // Show video call button
} else {
    // Show upgrade message
}
```

### **3. Get Call History**
```javascript
const history = await videoCallManager.getCallHistory(20);
console.log(history.data.calls);
```

### **4. Custom Call Settings**
```sql
INSERT INTO call_settings (user_id, auto_accept_calls, do_not_disturb) 
VALUES ('USER123', FALSE, TRUE);
```

## 🔄 **API Endpoints**

### **Available APIs:**
- `video-call/agora-token-server.php` - Token generation and call management
- `api/check_call_permissions.php` - Validate call permissions
- `api/check_incoming_calls.php` - Check for incoming calls
- `api/get_call_history.php` - Retrieve call history

### **API Usage Examples:**

**Initiate Call:**
```javascript
fetch('video-call/agora-token-server.php', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        action: 'initiate_call',
        caller_id: 'USER123',
        receiver_id: 'USER456'
    })
});
```

**Check Permissions:**
```javascript
fetch('api/check_call_permissions.php', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        caller_id: 'USER123',
        receiver_id: 'USER456'
    })
});
```

## 🎨 **Customization**

### **Styling Video Call UI**
Edit `css/video-call.css` to match your theme:
```css
.video-call-btn {
    background: linear-gradient(135deg, #your-primary-color, #your-secondary-color);
    /* Your custom styles */
}
```

### **Custom Call Notifications**
Modify `js/video-call.js` to use your notification system:
```javascript
showError(message) {
    // Replace with your notification system
    // Example: toastr.error(message);
    alert(message);
}
```

## 📊 **Analytics & Monitoring**

### **Call Statistics**
The system automatically tracks:
- Total calls made/received
- Call duration
- Success/failure rates
- Daily usage statistics

### **Admin Dashboard Integration**
Add to your admin panel:
```php
// Get call statistics
$stats = $DatabaseCo->dbLink->query("
    SELECT COUNT(*) as total_calls, 
           SUM(call_duration) as total_duration
    FROM video_calls 
    WHERE DATE(created_at) = CURDATE()
");
```

## 🔒 **Security Considerations**

### **Token Security**
- Tokens expire after 24 hours
- Unique tokens for each call
- Server-side validation

### **User Privacy**
- Call recordings (optional)
- Privacy settings respected
- Blocked user protection

### **Rate Limiting**
- Daily call limits for free users
- Call duration limits
- Spam protection

## 🐛 **Troubleshooting**

### **Common Issues:**

**1. Video Call Button Not Working**
- Check if `video-call.js` is loaded
- Verify user ID is set in JavaScript
- Check browser console for errors

**2. Agora Connection Failed**
- Verify App ID and Certificate
- Check network connectivity
- Ensure HTTPS for production

**3. Call Permissions Denied**
- Check user subscription status
- Verify database permissions
- Check for blocked users

**4. No Incoming Call Notifications**
- Enable browser notifications
- Check notification permissions
- Verify polling is working

### **Debug Mode**
Enable debug logging in `agora-config.php`:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 🚀 **Production Deployment**

### **Before Going Live:**
1. Update Agora credentials for production
2. Enable HTTPS (required for video calling)
3. Test call quality and performance
4. Set up monitoring and analytics
5. Configure proper error handling
6. Test on different devices and browsers

### **Performance Optimization:**
- Use CDN for Agora SDK
- Optimize video quality settings
- Implement call quality monitoring
- Set up proper caching

### **Monitoring:**
- Track call success rates
- Monitor call quality metrics
- Set up alerts for failures
- Regular performance reviews

## 📞 **Support**

For technical support or questions:
1. Check the troubleshooting section
2. Review Agora.io documentation
3. Check browser compatibility
4. Test network connectivity

---

**Note:** This video calling system is production-ready but should be thoroughly tested in your specific environment before deployment.
