{"version": 3, "sources": ["bootstrap-responsive-tabs.less", "bootstrap-responsive-tabs.css"], "names": [], "mappings": "AAGE;EAEI,qBAAA;ECHL;ADCC;EAMI,eAAA;EACA,qBAAA;EACA,oBAAA;EACA,2BAAA;EACA,oBAAA;EACA,wBAAA;EACA,aAAA;ECJL;ADyBC;EALE;IATE,eAAA;ICPH;EDgBC;IALE,gBAAA;ICRH;EACF;ADuBC;EALE;IAfE,eAAA;ICCH;EDcC;IAXE,gBAAA;ICAH;EACF;ADqBC;EALE;IArBE,eAAA;ICSH;EDYC;IAjBE,gBAAA;ICQH;EACF;ADkBD;EAJI;IA3BE,eAAA;ICiBH;EDUC;IAvBE,gBAAA;ICgBH;EACF", "file": "bootstrap-responsive-tabs.css", "sourcesContent": [".responsive-tabs-container {\n\n  // Accordion Styles\n  &[class*=\"accordion-\"] {\n    .tab-pane {\n      margin-bottom: 15px;\n    }\n\n    .accordion-link {\n      display: none;\n      margin-bottom: 10px;\n      padding: 10px 15px;\n      background-color: #f5f5f5;\n      border-radius: 3px;\n      border: 1px solid #ddd;\n      color: #333;\n    }\n  }\n\n  // Accordion media queries\n  .accordion-toggle() {\n    .nav-tabs {\n      display: none;\n    }\n\n    .accordion-link {\n      display: block;\n    }\n  }\n\n  @media (max-width: 767px) {\n    &.accordion-xs {\n      .accordion-toggle();\n    }\n  }\n\n  @media (min-width: 768px) and (max-width: 991px) {\n    &.accordion-sm {\n      .accordion-toggle();\n    }\n  }\n\n  @media (min-width: 992px) and (max-width: 1199px) {\n    &.accordion-md {\n      .accordion-toggle();\n    }\n  }\n\n  @media (min-width: 1200px) {\n    &.accordion-lg {\n      .accordion-toggle();\n    }\n  }\n}\n\n", ".responsive-tabs-container[class*=\"accordion-\"] .tab-pane {\n  margin-bottom: 15px;\n}\n.responsive-tabs-container[class*=\"accordion-\"] .accordion-link {\n  display: none;\n  margin-bottom: 10px;\n  padding: 10px 15px;\n  background-color: #f5f5f5;\n  border-radius: 3px;\n  border: 1px solid #ddd;\n  color: #333;\n}\n@media (max-width: 767px) {\n  .responsive-tabs-container.accordion-xs .nav-tabs {\n    display: none;\n  }\n  .responsive-tabs-container.accordion-xs .accordion-link {\n    display: block;\n  }\n}\n@media (min-width: 768px) and (max-width: 991px) {\n  .responsive-tabs-container.accordion-sm .nav-tabs {\n    display: none;\n  }\n  .responsive-tabs-container.accordion-sm .accordion-link {\n    display: block;\n  }\n}\n@media (min-width: 992px) and (max-width: 1199px) {\n  .responsive-tabs-container.accordion-md .nav-tabs {\n    display: none;\n  }\n  .responsive-tabs-container.accordion-md .accordion-link {\n    display: block;\n  }\n}\n@media (min-width: 1200px) {\n  .responsive-tabs-container.accordion-lg .nav-tabs {\n    display: none;\n  }\n  .responsive-tabs-container.accordion-lg .accordion-link {\n    display: block;\n  }\n}\n/*# sourceMappingURL=bootstrap-responsive-tabs.css.map */"]}