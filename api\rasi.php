<?php
include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();
$getRasiQry = $DatabaseCo->dbLink->query("SELECT * FROM `rasi` WHERE status='APPROVED'");
if(mysqli_num_rows($getRasiQry) > 0){
	$count=0;
	while($contact_res = mysqli_fetch_object($getRasiQry)){
		$count++;
		$response = array('id' => $contact_res->rasi_id,'rasi' => $contact_res->rasi,'status'=>"1");
		$data["response"][] = $response; 
	}
	echo json_encode($data);
	exit;
}else{
	$data['status'] = "0";
	echo json_encode($data);
	exit;
}
?>
