<?php
include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();
$count="";
if($_POST['user_id']==""){
	 $user_id = $_POST['user_id'];
	 $count++;
}
if($count==0){
	$user_id=$_POST['user_id'];
	$send_otp=$_POST['otp'];

	$s="select * from register where index_id='$user_id'";
	$q=$DatabaseCo->dbLink->query($s);
	$r=mysqli_fetch_object($q);
	$no=mysqli_num_rows($q);

    $order_id = rand(1000,9999);
    $order_id = substr($order_id, rand(0, strlen($order_id) - 4), 4);
    $_SESSION['order_id'] = $order_id;
	
    $text = "Hello $user, Welcome, Your OTP is $order_id. Do not share your OTP with anyone.";
    $message = str_replace(" ", "%20", $text);
    $mno = $r->mobile;
	$code = $r->mobile_code;
	$url="https://www.fast2sms.com/dev/bulkV2?authorization=r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr&route=otp&variables_values=$order_id&flash=0&numbers=$mno";		
	
    $ret = file($url);
    $old_otp1= $order_id;

if($no>0){
	
$insert="update register set otp='$old_otp1' where index_id='$user_id'";
$query=$DatabaseCo->dbLink->query($insert);

	$response['otp'] = "$old_otp1";
	$response['user_id'] = "$user_id";
	$response['message'] = "OTP Successfully Sent";
	$response['status'] = "1";
    echo json_encode($response);
	exit;
}else{
	$response['status'] = "0";
	$response['message'] = "User Not Exit";
	echo json_encode($response);
}

}else{
	$response['status'] = "0";
	$response['message'] = "Enter User Id";
	echo json_encode($response);
	exit;
}
?>