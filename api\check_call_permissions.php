<?php
/**
 * Check Video Call Permissions API
 * Validates if a user can make video calls to another user
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Only POST method allowed');
}

$input = json_decode(file_get_contents('php://input'), true);
$callerId = $input['caller_id'] ?? '';
$receiverId = $input['receiver_id'] ?? '';

if (empty($callerId) || empty($receiverId)) {
    sendResponse(false, 'Caller ID and Receiver ID are required');
}

try {
    // Check if caller exists and is active
    $callerQuery = $DatabaseCo->dbLink->query("
        SELECT r.status, r.gender, p.chat, p.video_calls, p.plan_duration, p.exp_date
        FROM register r 
        LEFT JOIN payments p ON r.matri_id = p.pmatri_id 
        WHERE r.matri_id = '$callerId'
    ");
    
    if (!$callerQuery || !($callerData = mysqli_fetch_object($callerQuery))) {
        sendResponse(false, 'Caller not found');
    }
    
    // Check if receiver exists and is active
    $receiverQuery = $DatabaseCo->dbLink->query("
        SELECT r.status, r.gender, cs.allow_calls_from, cs.do_not_disturb, cs.dnd_start_time, cs.dnd_end_time
        FROM register r 
        LEFT JOIN call_settings cs ON r.matri_id = cs.user_id
        WHERE r.matri_id = '$receiverId'
    ");
    
    if (!$receiverQuery || !($receiverData = mysqli_fetch_object($receiverQuery))) {
        sendResponse(false, 'Receiver not found');
    }
    
    // Check if users are blocked
    $blockQuery = $DatabaseCo->dbLink->query("
        SELECT COUNT(*) as blocked_count 
        FROM block_profile 
        WHERE (block_by = '$callerId' AND block_to = '$receiverId') 
        OR (block_by = '$receiverId' AND block_to = '$callerId')
    ");
    
    if ($blockQuery && ($blockData = mysqli_fetch_object($blockQuery)) && $blockData->blocked_count > 0) {
        sendResponse(false, 'Cannot call this user');
    }
    
    // Check caller's video call permissions
    if ($callerData->status === 'Active') {
        // Free user - limited video calling
        $permissions = [
            'allowed' => true,
            'type' => 'free',
            'max_duration' => 300, // 5 minutes
            'daily_limit' => 3,
            'features' => ['basic_video', 'basic_audio']
        ];
    } elseif ($callerData->status === 'Paid' && $callerData->chat === 'Yes') {
        // Check if subscription is still valid
        $expDate = strtotime($callerData->exp_date);
        if ($expDate < time()) {
            sendResponse(false, 'Your subscription has expired. Please renew to make video calls.');
        }
        
        // Paid user - full access
        $permissions = [
            'allowed' => true,
            'type' => 'paid',
            'max_duration' => 3600, // 60 minutes
            'daily_limit' => -1, // Unlimited
            'features' => ['hd_video', 'hd_audio', 'recording', 'screen_share']
        ];
    } else {
        sendResponse(false, 'Upgrade to premium to make video calls');
    }
    
    // Check daily call limit for free users
    if ($permissions['type'] === 'free') {
        $today = date('Y-m-d');
        $callCountQuery = $DatabaseCo->dbLink->query("
            SELECT COUNT(*) as call_count 
            FROM video_calls 
            WHERE caller_id = '$callerId' 
            AND DATE(created_at) = '$today'
            AND call_status IN ('accepted', 'ended')
        ");
        
        if ($callCountQuery && ($countData = mysqli_fetch_object($callCountQuery))) {
            $remaining = $permissions['daily_limit'] - $countData->call_count;
            if ($remaining <= 0) {
                sendResponse(false, 'Daily call limit reached. Upgrade to premium for unlimited calls.');
            }
            $permissions['calls_remaining'] = $remaining;
        }
    }
    
    // Check receiver's call settings
    if ($receiverData->do_not_disturb) {
        $currentTime = date('H:i:s');
        $dndStart = $receiverData->dnd_start_time;
        $dndEnd = $receiverData->dnd_end_time;
        
        if ($dndStart && $dndEnd) {
            if ($dndStart <= $dndEnd) {
                // Same day DND
                if ($currentTime >= $dndStart && $currentTime <= $dndEnd) {
                    sendResponse(false, 'User is in Do Not Disturb mode');
                }
            } else {
                // Overnight DND
                if ($currentTime >= $dndStart || $currentTime <= $dndEnd) {
                    sendResponse(false, 'User is in Do Not Disturb mode');
                }
            }
        }
    }
    
    // Check receiver's call preferences
    if ($receiverData->allow_calls_from === 'premium_only' && $callerData->status !== 'Paid') {
        sendResponse(false, 'This user only accepts calls from premium members');
    }
    
    if ($receiverData->allow_calls_from === 'contacts_only') {
        // Check if users have exchanged messages or interests
        $contactQuery = $DatabaseCo->dbLink->query("
            SELECT COUNT(*) as contact_count 
            FROM (
                SELECT 1 FROM messages WHERE (from_id = '$callerId' AND to_id = '$receiverId') OR (from_id = '$receiverId' AND to_id = '$callerId')
                UNION
                SELECT 1 FROM express_interest WHERE (from_id = '$callerId' AND to_id = '$receiverId' AND status = 'Accept') OR (from_id = '$receiverId' AND to_id = '$callerId' AND status = 'Accept')
            ) as contacts
        ");
        
        if ($contactQuery && ($contactData = mysqli_fetch_object($contactQuery)) && $contactData->contact_count == 0) {
            sendResponse(false, 'You need to connect with this user first before making a call');
        }
    }
    
    // Check if receiver is currently in a call
    $activeCallQuery = $DatabaseCo->dbLink->query("
        SELECT COUNT(*) as active_calls 
        FROM video_calls 
        WHERE (caller_id = '$receiverId' OR receiver_id = '$receiverId') 
        AND call_status IN ('initiated', 'ringing', 'accepted')
        AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    ");
    
    if ($activeCallQuery && ($activeCallData = mysqli_fetch_object($activeCallQuery)) && $activeCallData->active_calls > 0) {
        sendResponse(false, 'User is currently busy in another call');
    }
    
    // Get receiver info for the response
    $receiverInfoQuery = $DatabaseCo->dbLink->query("
        SELECT username, photo1, mobile 
        FROM register 
        WHERE matri_id = '$receiverId'
    ");
    
    $receiverInfo = null;
    if ($receiverInfoQuery && ($receiverInfoData = mysqli_fetch_object($receiverInfoQuery))) {
        $receiverInfo = [
            'name' => $receiverInfoData->username,
            'photo' => $receiverInfoData->photo1 ?: 'default-avatar.png',
            'mobile' => $receiverInfoData->mobile
        ];
    }
    
    sendResponse(true, 'Video call permission granted', [
        'permissions' => $permissions,
        'receiver' => $receiverInfo,
        'estimated_cost' => calculateCallCost($permissions['type'], $permissions['max_duration'])
    ]);
    
} catch (Exception $e) {
    error_log("Video call permission check error: " . $e->getMessage());
    sendResponse(false, 'Unable to verify call permissions');
}

/**
 * Calculate estimated call cost (if applicable)
 */
function calculateCallCost($userType, $duration) {
    if ($userType === 'paid') {
        return ['amount' => 0, 'currency' => 'INR', 'note' => 'Included in subscription'];
    }
    
    // For free users, you might charge per minute after the free limit
    $freeMinutes = 5;
    $ratePerMinute = 2; // INR per minute
    
    if ($duration > $freeMinutes * 60) {
        $chargeableMinutes = ceil(($duration - ($freeMinutes * 60)) / 60);
        return [
            'amount' => $chargeableMinutes * $ratePerMinute,
            'currency' => 'INR',
            'note' => "First {$freeMinutes} minutes free, then ₹{$ratePerMinute}/minute"
        ];
    }
    
    return ['amount' => 0, 'currency' => 'INR', 'note' => 'Free call'];
}

/**
 * Send JSON response
 */
function sendResponse($success, $message, $data = null) {
    $response = [
        'allowed' => $success,
        'success' => $success,
        'message' => $message,
        'timestamp' => time()
    ];
    
    if ($data !== null) {
        $response = array_merge($response, $data);
    }
    
    echo json_encode($response);
    exit();
}
?>
