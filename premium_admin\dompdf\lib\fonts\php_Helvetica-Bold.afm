$this->fonts[$font]=array (
  'FontName' => 'Helvetica-Bold',
  'FullName' => 'Helvetica Bold',
  'FamilyName' => 'Helvetica',
  'Weight' => 'Bold',
  'ItalicAngle' => '0',
  'IsFixedPitch' => 'false',
  'CharacterSet' => 'ExtendedRoman',
  'FontBBox' => 
  array (
    0 => '-170',
    1 => '-228',
    2 => '1003',
    3 => '962',
  ),
  'UnderlinePosition' => '-100',
  'UnderlineThickness' => '50',
  'Version' => '002.000',
  'EncodingScheme' => 'AdobeStandardEncoding',
  'CapHeight' => '718',
  'XHeight' => '532',
  'Ascender' => '718',
  'Descender' => '-207',
  'StdHW' => '118',
  'StdVW' => '140',
  'StartCharMetrics' => '315',
  'C' => 
  array (
    32 => 
    array (
      'C' => '32',
      'WX' => '278',
      'N' => 'space',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
    'space' => 
    array (
      'C' => '32',
      'WX' => '278',
      'N' => 'space',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
    33 => 
    array (
      'C' => '33',
      'WX' => '333',
      'N' => 'exclam',
      'B' => 
      array (
        0 => '90',
        1 => '0',
        2 => '244',
        3 => '718',
      ),
    ),
    'exclam' => 
    array (
      'C' => '33',
      'WX' => '333',
      'N' => 'exclam',
      'B' => 
      array (
        0 => '90',
        1 => '0',
        2 => '244',
        3 => '718',
      ),
    ),
    34 => 
    array (
      'C' => '34',
      'WX' => '474',
      'N' => 'quotedbl',
      'B' => 
      array (
        0 => '98',
        1 => '447',
        2 => '376',
        3 => '718',
      ),
    ),
    'quotedbl' => 
    array (
      'C' => '34',
      'WX' => '474',
      'N' => 'quotedbl',
      'B' => 
      array (
        0 => '98',
        1 => '447',
        2 => '376',
        3 => '718',
      ),
    ),
    35 => 
    array (
      'C' => '35',
      'WX' => '556',
      'N' => 'numbersign',
      'B' => 
      array (
        0 => '18',
        1 => '0',
        2 => '538',
        3 => '698',
      ),
    ),
    'numbersign' => 
    array (
      'C' => '35',
      'WX' => '556',
      'N' => 'numbersign',
      'B' => 
      array (
        0 => '18',
        1 => '0',
        2 => '538',
        3 => '698',
      ),
    ),
    36 => 
    array (
      'C' => '36',
      'WX' => '556',
      'N' => 'dollar',
      'B' => 
      array (
        0 => '30',
        1 => '-115',
        2 => '523',
        3 => '775',
      ),
    ),
    'dollar' => 
    array (
      'C' => '36',
      'WX' => '556',
      'N' => 'dollar',
      'B' => 
      array (
        0 => '30',
        1 => '-115',
        2 => '523',
        3 => '775',
      ),
    ),
    37 => 
    array (
      'C' => '37',
      'WX' => '889',
      'N' => 'percent',
      'B' => 
      array (
        0 => '28',
        1 => '-19',
        2 => '861',
        3 => '710',
      ),
    ),
    'percent' => 
    array (
      'C' => '37',
      'WX' => '889',
      'N' => 'percent',
      'B' => 
      array (
        0 => '28',
        1 => '-19',
        2 => '861',
        3 => '710',
      ),
    ),
    38 => 
    array (
      'C' => '38',
      'WX' => '722',
      'N' => 'ampersand',
      'B' => 
      array (
        0 => '54',
        1 => '-19',
        2 => '701',
        3 => '718',
      ),
    ),
    'ampersand' => 
    array (
      'C' => '38',
      'WX' => '722',
      'N' => 'ampersand',
      'B' => 
      array (
        0 => '54',
        1 => '-19',
        2 => '701',
        3 => '718',
      ),
    ),
    39 => 
    array (
      'C' => '39',
      'WX' => '278',
      'N' => 'quoteright',
      'B' => 
      array (
        0 => '69',
        1 => '445',
        2 => '209',
        3 => '718',
      ),
    ),
    'quoteright' => 
    array (
      'C' => '39',
      'WX' => '278',
      'N' => 'quoteright',
      'B' => 
      array (
        0 => '69',
        1 => '445',
        2 => '209',
        3 => '718',
      ),
    ),
    40 => 
    array (
      'C' => '40',
      'WX' => '333',
      'N' => 'parenleft',
      'B' => 
      array (
        0 => '35',
        1 => '-208',
        2 => '314',
        3 => '734',
      ),
    ),
    'parenleft' => 
    array (
      'C' => '40',
      'WX' => '333',
      'N' => 'parenleft',
      'B' => 
      array (
        0 => '35',
        1 => '-208',
        2 => '314',
        3 => '734',
      ),
    ),
    41 => 
    array (
      'C' => '41',
      'WX' => '333',
      'N' => 'parenright',
      'B' => 
      array (
        0 => '19',
        1 => '-208',
        2 => '298',
        3 => '734',
      ),
    ),
    'parenright' => 
    array (
      'C' => '41',
      'WX' => '333',
      'N' => 'parenright',
      'B' => 
      array (
        0 => '19',
        1 => '-208',
        2 => '298',
        3 => '734',
      ),
    ),
    42 => 
    array (
      'C' => '42',
      'WX' => '389',
      'N' => 'asterisk',
      'B' => 
      array (
        0 => '27',
        1 => '387',
        2 => '362',
        3 => '718',
      ),
    ),
    'asterisk' => 
    array (
      'C' => '42',
      'WX' => '389',
      'N' => 'asterisk',
      'B' => 
      array (
        0 => '27',
        1 => '387',
        2 => '362',
        3 => '718',
      ),
    ),
    43 => 
    array (
      'C' => '43',
      'WX' => '584',
      'N' => 'plus',
      'B' => 
      array (
        0 => '40',
        1 => '0',
        2 => '544',
        3 => '506',
      ),
    ),
    'plus' => 
    array (
      'C' => '43',
      'WX' => '584',
      'N' => 'plus',
      'B' => 
      array (
        0 => '40',
        1 => '0',
        2 => '544',
        3 => '506',
      ),
    ),
    44 => 
    array (
      'C' => '44',
      'WX' => '278',
      'N' => 'comma',
      'B' => 
      array (
        0 => '64',
        1 => '-168',
        2 => '214',
        3 => '146',
      ),
    ),
    'comma' => 
    array (
      'C' => '44',
      'WX' => '278',
      'N' => 'comma',
      'B' => 
      array (
        0 => '64',
        1 => '-168',
        2 => '214',
        3 => '146',
      ),
    ),
    45 => 
    array (
      'C' => '45',
      'WX' => '333',
      'N' => 'hyphen',
      'B' => 
      array (
        0 => '27',
        1 => '215',
        2 => '306',
        3 => '345',
      ),
    ),
    'hyphen' => 
    array (
      'C' => '45',
      'WX' => '333',
      'N' => 'hyphen',
      'B' => 
      array (
        0 => '27',
        1 => '215',
        2 => '306',
        3 => '345',
      ),
    ),
    46 => 
    array (
      'C' => '46',
      'WX' => '278',
      'N' => 'period',
      'B' => 
      array (
        0 => '64',
        1 => '0',
        2 => '214',
        3 => '146',
      ),
    ),
    'period' => 
    array (
      'C' => '46',
      'WX' => '278',
      'N' => 'period',
      'B' => 
      array (
        0 => '64',
        1 => '0',
        2 => '214',
        3 => '146',
      ),
    ),
    47 => 
    array (
      'C' => '47',
      'WX' => '278',
      'N' => 'slash',
      'B' => 
      array (
        0 => '-33',
        1 => '-19',
        2 => '311',
        3 => '737',
      ),
    ),
    'slash' => 
    array (
      'C' => '47',
      'WX' => '278',
      'N' => 'slash',
      'B' => 
      array (
        0 => '-33',
        1 => '-19',
        2 => '311',
        3 => '737',
      ),
    ),
    48 => 
    array (
      'C' => '48',
      'WX' => '556',
      'N' => 'zero',
      'B' => 
      array (
        0 => '32',
        1 => '-19',
        2 => '524',
        3 => '710',
      ),
    ),
    'zero' => 
    array (
      'C' => '48',
      'WX' => '556',
      'N' => 'zero',
      'B' => 
      array (
        0 => '32',
        1 => '-19',
        2 => '524',
        3 => '710',
      ),
    ),
    49 => 
    array (
      'C' => '49',
      'WX' => '556',
      'N' => 'one',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '378',
        3 => '710',
      ),
    ),
    'one' => 
    array (
      'C' => '49',
      'WX' => '556',
      'N' => 'one',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '378',
        3 => '710',
      ),
    ),
    50 => 
    array (
      'C' => '50',
      'WX' => '556',
      'N' => 'two',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '511',
        3 => '710',
      ),
    ),
    'two' => 
    array (
      'C' => '50',
      'WX' => '556',
      'N' => 'two',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '511',
        3 => '710',
      ),
    ),
    51 => 
    array (
      'C' => '51',
      'WX' => '556',
      'N' => 'three',
      'B' => 
      array (
        0 => '27',
        1 => '-19',
        2 => '516',
        3 => '710',
      ),
    ),
    'three' => 
    array (
      'C' => '51',
      'WX' => '556',
      'N' => 'three',
      'B' => 
      array (
        0 => '27',
        1 => '-19',
        2 => '516',
        3 => '710',
      ),
    ),
    52 => 
    array (
      'C' => '52',
      'WX' => '556',
      'N' => 'four',
      'B' => 
      array (
        0 => '27',
        1 => '0',
        2 => '526',
        3 => '710',
      ),
    ),
    'four' => 
    array (
      'C' => '52',
      'WX' => '556',
      'N' => 'four',
      'B' => 
      array (
        0 => '27',
        1 => '0',
        2 => '526',
        3 => '710',
      ),
    ),
    53 => 
    array (
      'C' => '53',
      'WX' => '556',
      'N' => 'five',
      'B' => 
      array (
        0 => '27',
        1 => '-19',
        2 => '516',
        3 => '698',
      ),
    ),
    'five' => 
    array (
      'C' => '53',
      'WX' => '556',
      'N' => 'five',
      'B' => 
      array (
        0 => '27',
        1 => '-19',
        2 => '516',
        3 => '698',
      ),
    ),
    54 => 
    array (
      'C' => '54',
      'WX' => '556',
      'N' => 'six',
      'B' => 
      array (
        0 => '31',
        1 => '-19',
        2 => '520',
        3 => '710',
      ),
    ),
    'six' => 
    array (
      'C' => '54',
      'WX' => '556',
      'N' => 'six',
      'B' => 
      array (
        0 => '31',
        1 => '-19',
        2 => '520',
        3 => '710',
      ),
    ),
    55 => 
    array (
      'C' => '55',
      'WX' => '556',
      'N' => 'seven',
      'B' => 
      array (
        0 => '25',
        1 => '0',
        2 => '528',
        3 => '698',
      ),
    ),
    'seven' => 
    array (
      'C' => '55',
      'WX' => '556',
      'N' => 'seven',
      'B' => 
      array (
        0 => '25',
        1 => '0',
        2 => '528',
        3 => '698',
      ),
    ),
    56 => 
    array (
      'C' => '56',
      'WX' => '556',
      'N' => 'eight',
      'B' => 
      array (
        0 => '32',
        1 => '-19',
        2 => '524',
        3 => '710',
      ),
    ),
    'eight' => 
    array (
      'C' => '56',
      'WX' => '556',
      'N' => 'eight',
      'B' => 
      array (
        0 => '32',
        1 => '-19',
        2 => '524',
        3 => '710',
      ),
    ),
    57 => 
    array (
      'C' => '57',
      'WX' => '556',
      'N' => 'nine',
      'B' => 
      array (
        0 => '30',
        1 => '-19',
        2 => '522',
        3 => '710',
      ),
    ),
    'nine' => 
    array (
      'C' => '57',
      'WX' => '556',
      'N' => 'nine',
      'B' => 
      array (
        0 => '30',
        1 => '-19',
        2 => '522',
        3 => '710',
      ),
    ),
    58 => 
    array (
      'C' => '58',
      'WX' => '333',
      'N' => 'colon',
      'B' => 
      array (
        0 => '92',
        1 => '0',
        2 => '242',
        3 => '512',
      ),
    ),
    'colon' => 
    array (
      'C' => '58',
      'WX' => '333',
      'N' => 'colon',
      'B' => 
      array (
        0 => '92',
        1 => '0',
        2 => '242',
        3 => '512',
      ),
    ),
    59 => 
    array (
      'C' => '59',
      'WX' => '333',
      'N' => 'semicolon',
      'B' => 
      array (
        0 => '92',
        1 => '-168',
        2 => '242',
        3 => '512',
      ),
    ),
    'semicolon' => 
    array (
      'C' => '59',
      'WX' => '333',
      'N' => 'semicolon',
      'B' => 
      array (
        0 => '92',
        1 => '-168',
        2 => '242',
        3 => '512',
      ),
    ),
    60 => 
    array (
      'C' => '60',
      'WX' => '584',
      'N' => 'less',
      'B' => 
      array (
        0 => '38',
        1 => '-8',
        2 => '546',
        3 => '514',
      ),
    ),
    'less' => 
    array (
      'C' => '60',
      'WX' => '584',
      'N' => 'less',
      'B' => 
      array (
        0 => '38',
        1 => '-8',
        2 => '546',
        3 => '514',
      ),
    ),
    61 => 
    array (
      'C' => '61',
      'WX' => '584',
      'N' => 'equal',
      'B' => 
      array (
        0 => '40',
        1 => '87',
        2 => '544',
        3 => '419',
      ),
    ),
    'equal' => 
    array (
      'C' => '61',
      'WX' => '584',
      'N' => 'equal',
      'B' => 
      array (
        0 => '40',
        1 => '87',
        2 => '544',
        3 => '419',
      ),
    ),
    62 => 
    array (
      'C' => '62',
      'WX' => '584',
      'N' => 'greater',
      'B' => 
      array (
        0 => '38',
        1 => '-8',
        2 => '546',
        3 => '514',
      ),
    ),
    'greater' => 
    array (
      'C' => '62',
      'WX' => '584',
      'N' => 'greater',
      'B' => 
      array (
        0 => '38',
        1 => '-8',
        2 => '546',
        3 => '514',
      ),
    ),
    63 => 
    array (
      'C' => '63',
      'WX' => '611',
      'N' => 'question',
      'B' => 
      array (
        0 => '60',
        1 => '0',
        2 => '556',
        3 => '727',
      ),
    ),
    'question' => 
    array (
      'C' => '63',
      'WX' => '611',
      'N' => 'question',
      'B' => 
      array (
        0 => '60',
        1 => '0',
        2 => '556',
        3 => '727',
      ),
    ),
    64 => 
    array (
      'C' => '64',
      'WX' => '975',
      'N' => 'at',
      'B' => 
      array (
        0 => '118',
        1 => '-19',
        2 => '856',
        3 => '737',
      ),
    ),
    'at' => 
    array (
      'C' => '64',
      'WX' => '975',
      'N' => 'at',
      'B' => 
      array (
        0 => '118',
        1 => '-19',
        2 => '856',
        3 => '737',
      ),
    ),
    65 => 
    array (
      'C' => '65',
      'WX' => '722',
      'N' => 'A',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '702',
        3 => '718',
      ),
    ),
    'A' => 
    array (
      'C' => '65',
      'WX' => '722',
      'N' => 'A',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '702',
        3 => '718',
      ),
    ),
    66 => 
    array (
      'C' => '66',
      'WX' => '722',
      'N' => 'B',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '669',
        3 => '718',
      ),
    ),
    'B' => 
    array (
      'C' => '66',
      'WX' => '722',
      'N' => 'B',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '669',
        3 => '718',
      ),
    ),
    67 => 
    array (
      'C' => '67',
      'WX' => '722',
      'N' => 'C',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '684',
        3 => '737',
      ),
    ),
    'C' => 
    array (
      'C' => '67',
      'WX' => '722',
      'N' => 'C',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '684',
        3 => '737',
      ),
    ),
    68 => 
    array (
      'C' => '68',
      'WX' => '722',
      'N' => 'D',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '685',
        3 => '718',
      ),
    ),
    'D' => 
    array (
      'C' => '68',
      'WX' => '722',
      'N' => 'D',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '685',
        3 => '718',
      ),
    ),
    69 => 
    array (
      'C' => '69',
      'WX' => '667',
      'N' => 'E',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '621',
        3 => '718',
      ),
    ),
    'E' => 
    array (
      'C' => '69',
      'WX' => '667',
      'N' => 'E',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '621',
        3 => '718',
      ),
    ),
    70 => 
    array (
      'C' => '70',
      'WX' => '611',
      'N' => 'F',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '587',
        3 => '718',
      ),
    ),
    'F' => 
    array (
      'C' => '70',
      'WX' => '611',
      'N' => 'F',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '587',
        3 => '718',
      ),
    ),
    71 => 
    array (
      'C' => '71',
      'WX' => '778',
      'N' => 'G',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '713',
        3 => '737',
      ),
    ),
    'G' => 
    array (
      'C' => '71',
      'WX' => '778',
      'N' => 'G',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '713',
        3 => '737',
      ),
    ),
    72 => 
    array (
      'C' => '72',
      'WX' => '722',
      'N' => 'H',
      'B' => 
      array (
        0 => '71',
        1 => '0',
        2 => '651',
        3 => '718',
      ),
    ),
    'H' => 
    array (
      'C' => '72',
      'WX' => '722',
      'N' => 'H',
      'B' => 
      array (
        0 => '71',
        1 => '0',
        2 => '651',
        3 => '718',
      ),
    ),
    73 => 
    array (
      'C' => '73',
      'WX' => '278',
      'N' => 'I',
      'B' => 
      array (
        0 => '64',
        1 => '0',
        2 => '214',
        3 => '718',
      ),
    ),
    'I' => 
    array (
      'C' => '73',
      'WX' => '278',
      'N' => 'I',
      'B' => 
      array (
        0 => '64',
        1 => '0',
        2 => '214',
        3 => '718',
      ),
    ),
    74 => 
    array (
      'C' => '74',
      'WX' => '556',
      'N' => 'J',
      'B' => 
      array (
        0 => '22',
        1 => '-18',
        2 => '484',
        3 => '718',
      ),
    ),
    'J' => 
    array (
      'C' => '74',
      'WX' => '556',
      'N' => 'J',
      'B' => 
      array (
        0 => '22',
        1 => '-18',
        2 => '484',
        3 => '718',
      ),
    ),
    75 => 
    array (
      'C' => '75',
      'WX' => '722',
      'N' => 'K',
      'B' => 
      array (
        0 => '87',
        1 => '0',
        2 => '722',
        3 => '718',
      ),
    ),
    'K' => 
    array (
      'C' => '75',
      'WX' => '722',
      'N' => 'K',
      'B' => 
      array (
        0 => '87',
        1 => '0',
        2 => '722',
        3 => '718',
      ),
    ),
    76 => 
    array (
      'C' => '76',
      'WX' => '611',
      'N' => 'L',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '583',
        3 => '718',
      ),
    ),
    'L' => 
    array (
      'C' => '76',
      'WX' => '611',
      'N' => 'L',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '583',
        3 => '718',
      ),
    ),
    77 => 
    array (
      'C' => '77',
      'WX' => '833',
      'N' => 'M',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '765',
        3 => '718',
      ),
    ),
    'M' => 
    array (
      'C' => '77',
      'WX' => '833',
      'N' => 'M',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '765',
        3 => '718',
      ),
    ),
    78 => 
    array (
      'C' => '78',
      'WX' => '722',
      'N' => 'N',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '654',
        3 => '718',
      ),
    ),
    'N' => 
    array (
      'C' => '78',
      'WX' => '722',
      'N' => 'N',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '654',
        3 => '718',
      ),
    ),
    79 => 
    array (
      'C' => '79',
      'WX' => '778',
      'N' => 'O',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '734',
        3 => '737',
      ),
    ),
    'O' => 
    array (
      'C' => '79',
      'WX' => '778',
      'N' => 'O',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '734',
        3 => '737',
      ),
    ),
    80 => 
    array (
      'C' => '80',
      'WX' => '667',
      'N' => 'P',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '627',
        3 => '718',
      ),
    ),
    'P' => 
    array (
      'C' => '80',
      'WX' => '667',
      'N' => 'P',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '627',
        3 => '718',
      ),
    ),
    81 => 
    array (
      'C' => '81',
      'WX' => '778',
      'N' => 'Q',
      'B' => 
      array (
        0 => '44',
        1 => '-52',
        2 => '737',
        3 => '737',
      ),
    ),
    'Q' => 
    array (
      'C' => '81',
      'WX' => '778',
      'N' => 'Q',
      'B' => 
      array (
        0 => '44',
        1 => '-52',
        2 => '737',
        3 => '737',
      ),
    ),
    82 => 
    array (
      'C' => '82',
      'WX' => '722',
      'N' => 'R',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '677',
        3 => '718',
      ),
    ),
    'R' => 
    array (
      'C' => '82',
      'WX' => '722',
      'N' => 'R',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '677',
        3 => '718',
      ),
    ),
    83 => 
    array (
      'C' => '83',
      'WX' => '667',
      'N' => 'S',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '629',
        3 => '737',
      ),
    ),
    'S' => 
    array (
      'C' => '83',
      'WX' => '667',
      'N' => 'S',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '629',
        3 => '737',
      ),
    ),
    84 => 
    array (
      'C' => '84',
      'WX' => '611',
      'N' => 'T',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '598',
        3 => '718',
      ),
    ),
    'T' => 
    array (
      'C' => '84',
      'WX' => '611',
      'N' => 'T',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '598',
        3 => '718',
      ),
    ),
    85 => 
    array (
      'C' => '85',
      'WX' => '722',
      'N' => 'U',
      'B' => 
      array (
        0 => '72',
        1 => '-19',
        2 => '651',
        3 => '718',
      ),
    ),
    'U' => 
    array (
      'C' => '85',
      'WX' => '722',
      'N' => 'U',
      'B' => 
      array (
        0 => '72',
        1 => '-19',
        2 => '651',
        3 => '718',
      ),
    ),
    86 => 
    array (
      'C' => '86',
      'WX' => '667',
      'N' => 'V',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '648',
        3 => '718',
      ),
    ),
    'V' => 
    array (
      'C' => '86',
      'WX' => '667',
      'N' => 'V',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '648',
        3 => '718',
      ),
    ),
    87 => 
    array (
      'C' => '87',
      'WX' => '944',
      'N' => 'W',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '929',
        3 => '718',
      ),
    ),
    'W' => 
    array (
      'C' => '87',
      'WX' => '944',
      'N' => 'W',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '929',
        3 => '718',
      ),
    ),
    88 => 
    array (
      'C' => '88',
      'WX' => '667',
      'N' => 'X',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '653',
        3 => '718',
      ),
    ),
    'X' => 
    array (
      'C' => '88',
      'WX' => '667',
      'N' => 'X',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '653',
        3 => '718',
      ),
    ),
    89 => 
    array (
      'C' => '89',
      'WX' => '667',
      'N' => 'Y',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '653',
        3 => '718',
      ),
    ),
    'Y' => 
    array (
      'C' => '89',
      'WX' => '667',
      'N' => 'Y',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '653',
        3 => '718',
      ),
    ),
    90 => 
    array (
      'C' => '90',
      'WX' => '611',
      'N' => 'Z',
      'B' => 
      array (
        0 => '25',
        1 => '0',
        2 => '586',
        3 => '718',
      ),
    ),
    'Z' => 
    array (
      'C' => '90',
      'WX' => '611',
      'N' => 'Z',
      'B' => 
      array (
        0 => '25',
        1 => '0',
        2 => '586',
        3 => '718',
      ),
    ),
    91 => 
    array (
      'C' => '91',
      'WX' => '333',
      'N' => 'bracketleft',
      'B' => 
      array (
        0 => '63',
        1 => '-196',
        2 => '309',
        3 => '722',
      ),
    ),
    'bracketleft' => 
    array (
      'C' => '91',
      'WX' => '333',
      'N' => 'bracketleft',
      'B' => 
      array (
        0 => '63',
        1 => '-196',
        2 => '309',
        3 => '722',
      ),
    ),
    92 => 
    array (
      'C' => '92',
      'WX' => '278',
      'N' => 'backslash',
      'B' => 
      array (
        0 => '-33',
        1 => '-19',
        2 => '311',
        3 => '737',
      ),
    ),
    'backslash' => 
    array (
      'C' => '92',
      'WX' => '278',
      'N' => 'backslash',
      'B' => 
      array (
        0 => '-33',
        1 => '-19',
        2 => '311',
        3 => '737',
      ),
    ),
    93 => 
    array (
      'C' => '93',
      'WX' => '333',
      'N' => 'bracketright',
      'B' => 
      array (
        0 => '24',
        1 => '-196',
        2 => '270',
        3 => '722',
      ),
    ),
    'bracketright' => 
    array (
      'C' => '93',
      'WX' => '333',
      'N' => 'bracketright',
      'B' => 
      array (
        0 => '24',
        1 => '-196',
        2 => '270',
        3 => '722',
      ),
    ),
    94 => 
    array (
      'C' => '94',
      'WX' => '584',
      'N' => 'asciicircum',
      'B' => 
      array (
        0 => '62',
        1 => '323',
        2 => '522',
        3 => '698',
      ),
    ),
    'asciicircum' => 
    array (
      'C' => '94',
      'WX' => '584',
      'N' => 'asciicircum',
      'B' => 
      array (
        0 => '62',
        1 => '323',
        2 => '522',
        3 => '698',
      ),
    ),
    95 => 
    array (
      'C' => '95',
      'WX' => '556',
      'N' => 'underscore',
      'B' => 
      array (
        0 => '0',
        1 => '-125',
        2 => '556',
        3 => '-75',
      ),
    ),
    'underscore' => 
    array (
      'C' => '95',
      'WX' => '556',
      'N' => 'underscore',
      'B' => 
      array (
        0 => '0',
        1 => '-125',
        2 => '556',
        3 => '-75',
      ),
    ),
    96 => 
    array (
      'C' => '96',
      'WX' => '278',
      'N' => 'quoteleft',
      'B' => 
      array (
        0 => '69',
        1 => '454',
        2 => '209',
        3 => '727',
      ),
    ),
    'quoteleft' => 
    array (
      'C' => '96',
      'WX' => '278',
      'N' => 'quoteleft',
      'B' => 
      array (
        0 => '69',
        1 => '454',
        2 => '209',
        3 => '727',
      ),
    ),
    97 => 
    array (
      'C' => '97',
      'WX' => '556',
      'N' => 'a',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '527',
        3 => '546',
      ),
    ),
    'a' => 
    array (
      'C' => '97',
      'WX' => '556',
      'N' => 'a',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '527',
        3 => '546',
      ),
    ),
    98 => 
    array (
      'C' => '98',
      'WX' => '611',
      'N' => 'b',
      'B' => 
      array (
        0 => '61',
        1 => '-14',
        2 => '578',
        3 => '718',
      ),
    ),
    'b' => 
    array (
      'C' => '98',
      'WX' => '611',
      'N' => 'b',
      'B' => 
      array (
        0 => '61',
        1 => '-14',
        2 => '578',
        3 => '718',
      ),
    ),
    99 => 
    array (
      'C' => '99',
      'WX' => '556',
      'N' => 'c',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '524',
        3 => '546',
      ),
    ),
    'c' => 
    array (
      'C' => '99',
      'WX' => '556',
      'N' => 'c',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '524',
        3 => '546',
      ),
    ),
    100 => 
    array (
      'C' => '100',
      'WX' => '611',
      'N' => 'd',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '551',
        3 => '718',
      ),
    ),
    'd' => 
    array (
      'C' => '100',
      'WX' => '611',
      'N' => 'd',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '551',
        3 => '718',
      ),
    ),
    101 => 
    array (
      'C' => '101',
      'WX' => '556',
      'N' => 'e',
      'B' => 
      array (
        0 => '23',
        1 => '-14',
        2 => '528',
        3 => '546',
      ),
    ),
    'e' => 
    array (
      'C' => '101',
      'WX' => '556',
      'N' => 'e',
      'B' => 
      array (
        0 => '23',
        1 => '-14',
        2 => '528',
        3 => '546',
      ),
    ),
    102 => 
    array (
      'C' => '102',
      'WX' => '333',
      'N' => 'f',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '318',
        3 => '727',
      ),
      'L' => 
      array (
        0 => 'l',
        1 => 'fl',
      ),
    ),
    'f' => 
    array (
      'C' => '102',
      'WX' => '333',
      'N' => 'f',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '318',
        3 => '727',
      ),
      'L' => 
      array (
        0 => 'l',
        1 => 'fl',
      ),
    ),
    103 => 
    array (
      'C' => '103',
      'WX' => '611',
      'N' => 'g',
      'B' => 
      array (
        0 => '40',
        1 => '-217',
        2 => '553',
        3 => '546',
      ),
    ),
    'g' => 
    array (
      'C' => '103',
      'WX' => '611',
      'N' => 'g',
      'B' => 
      array (
        0 => '40',
        1 => '-217',
        2 => '553',
        3 => '546',
      ),
    ),
    104 => 
    array (
      'C' => '104',
      'WX' => '611',
      'N' => 'h',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '546',
        3 => '718',
      ),
    ),
    'h' => 
    array (
      'C' => '104',
      'WX' => '611',
      'N' => 'h',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '546',
        3 => '718',
      ),
    ),
    105 => 
    array (
      'C' => '105',
      'WX' => '278',
      'N' => 'i',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '209',
        3 => '725',
      ),
    ),
    'i' => 
    array (
      'C' => '105',
      'WX' => '278',
      'N' => 'i',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '209',
        3 => '725',
      ),
    ),
    106 => 
    array (
      'C' => '106',
      'WX' => '278',
      'N' => 'j',
      'B' => 
      array (
        0 => '3',
        1 => '-214',
        2 => '209',
        3 => '725',
      ),
    ),
    'j' => 
    array (
      'C' => '106',
      'WX' => '278',
      'N' => 'j',
      'B' => 
      array (
        0 => '3',
        1 => '-214',
        2 => '209',
        3 => '725',
      ),
    ),
    107 => 
    array (
      'C' => '107',
      'WX' => '556',
      'N' => 'k',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '562',
        3 => '718',
      ),
    ),
    'k' => 
    array (
      'C' => '107',
      'WX' => '556',
      'N' => 'k',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '562',
        3 => '718',
      ),
    ),
    108 => 
    array (
      'C' => '108',
      'WX' => '278',
      'N' => 'l',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '209',
        3 => '718',
      ),
    ),
    'l' => 
    array (
      'C' => '108',
      'WX' => '278',
      'N' => 'l',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '209',
        3 => '718',
      ),
    ),
    109 => 
    array (
      'C' => '109',
      'WX' => '889',
      'N' => 'm',
      'B' => 
      array (
        0 => '64',
        1 => '0',
        2 => '826',
        3 => '546',
      ),
    ),
    'm' => 
    array (
      'C' => '109',
      'WX' => '889',
      'N' => 'm',
      'B' => 
      array (
        0 => '64',
        1 => '0',
        2 => '826',
        3 => '546',
      ),
    ),
    110 => 
    array (
      'C' => '110',
      'WX' => '611',
      'N' => 'n',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '546',
        3 => '546',
      ),
    ),
    'n' => 
    array (
      'C' => '110',
      'WX' => '611',
      'N' => 'n',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '546',
        3 => '546',
      ),
    ),
    111 => 
    array (
      'C' => '111',
      'WX' => '611',
      'N' => 'o',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '578',
        3 => '546',
      ),
    ),
    'o' => 
    array (
      'C' => '111',
      'WX' => '611',
      'N' => 'o',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '578',
        3 => '546',
      ),
    ),
    112 => 
    array (
      'C' => '112',
      'WX' => '611',
      'N' => 'p',
      'B' => 
      array (
        0 => '62',
        1 => '-207',
        2 => '578',
        3 => '546',
      ),
    ),
    'p' => 
    array (
      'C' => '112',
      'WX' => '611',
      'N' => 'p',
      'B' => 
      array (
        0 => '62',
        1 => '-207',
        2 => '578',
        3 => '546',
      ),
    ),
    113 => 
    array (
      'C' => '113',
      'WX' => '611',
      'N' => 'q',
      'B' => 
      array (
        0 => '34',
        1 => '-207',
        2 => '552',
        3 => '546',
      ),
    ),
    'q' => 
    array (
      'C' => '113',
      'WX' => '611',
      'N' => 'q',
      'B' => 
      array (
        0 => '34',
        1 => '-207',
        2 => '552',
        3 => '546',
      ),
    ),
    114 => 
    array (
      'C' => '114',
      'WX' => '389',
      'N' => 'r',
      'B' => 
      array (
        0 => '64',
        1 => '0',
        2 => '373',
        3 => '546',
      ),
    ),
    'r' => 
    array (
      'C' => '114',
      'WX' => '389',
      'N' => 'r',
      'B' => 
      array (
        0 => '64',
        1 => '0',
        2 => '373',
        3 => '546',
      ),
    ),
    115 => 
    array (
      'C' => '115',
      'WX' => '556',
      'N' => 's',
      'B' => 
      array (
        0 => '30',
        1 => '-14',
        2 => '519',
        3 => '546',
      ),
    ),
    's' => 
    array (
      'C' => '115',
      'WX' => '556',
      'N' => 's',
      'B' => 
      array (
        0 => '30',
        1 => '-14',
        2 => '519',
        3 => '546',
      ),
    ),
    116 => 
    array (
      'C' => '116',
      'WX' => '333',
      'N' => 't',
      'B' => 
      array (
        0 => '10',
        1 => '-6',
        2 => '309',
        3 => '676',
      ),
    ),
    't' => 
    array (
      'C' => '116',
      'WX' => '333',
      'N' => 't',
      'B' => 
      array (
        0 => '10',
        1 => '-6',
        2 => '309',
        3 => '676',
      ),
    ),
    117 => 
    array (
      'C' => '117',
      'WX' => '611',
      'N' => 'u',
      'B' => 
      array (
        0 => '66',
        1 => '-14',
        2 => '545',
        3 => '532',
      ),
    ),
    'u' => 
    array (
      'C' => '117',
      'WX' => '611',
      'N' => 'u',
      'B' => 
      array (
        0 => '66',
        1 => '-14',
        2 => '545',
        3 => '532',
      ),
    ),
    118 => 
    array (
      'C' => '118',
      'WX' => '556',
      'N' => 'v',
      'B' => 
      array (
        0 => '13',
        1 => '0',
        2 => '543',
        3 => '532',
      ),
    ),
    'v' => 
    array (
      'C' => '118',
      'WX' => '556',
      'N' => 'v',
      'B' => 
      array (
        0 => '13',
        1 => '0',
        2 => '543',
        3 => '532',
      ),
    ),
    119 => 
    array (
      'C' => '119',
      'WX' => '778',
      'N' => 'w',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '769',
        3 => '532',
      ),
    ),
    'w' => 
    array (
      'C' => '119',
      'WX' => '778',
      'N' => 'w',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '769',
        3 => '532',
      ),
    ),
    120 => 
    array (
      'C' => '120',
      'WX' => '556',
      'N' => 'x',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '541',
        3 => '532',
      ),
    ),
    'x' => 
    array (
      'C' => '120',
      'WX' => '556',
      'N' => 'x',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '541',
        3 => '532',
      ),
    ),
    121 => 
    array (
      'C' => '121',
      'WX' => '556',
      'N' => 'y',
      'B' => 
      array (
        0 => '10',
        1 => '-214',
        2 => '539',
        3 => '532',
      ),
    ),
    'y' => 
    array (
      'C' => '121',
      'WX' => '556',
      'N' => 'y',
      'B' => 
      array (
        0 => '10',
        1 => '-214',
        2 => '539',
        3 => '532',
      ),
    ),
    122 => 
    array (
      'C' => '122',
      'WX' => '500',
      'N' => 'z',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '480',
        3 => '532',
      ),
    ),
    'z' => 
    array (
      'C' => '122',
      'WX' => '500',
      'N' => 'z',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '480',
        3 => '532',
      ),
    ),
    123 => 
    array (
      'C' => '123',
      'WX' => '389',
      'N' => 'braceleft',
      'B' => 
      array (
        0 => '48',
        1 => '-196',
        2 => '365',
        3 => '722',
      ),
    ),
    'braceleft' => 
    array (
      'C' => '123',
      'WX' => '389',
      'N' => 'braceleft',
      'B' => 
      array (
        0 => '48',
        1 => '-196',
        2 => '365',
        3 => '722',
      ),
    ),
    124 => 
    array (
      'C' => '124',
      'WX' => '280',
      'N' => 'bar',
      'B' => 
      array (
        0 => '84',
        1 => '-225',
        2 => '196',
        3 => '775',
      ),
    ),
    'bar' => 
    array (
      'C' => '124',
      'WX' => '280',
      'N' => 'bar',
      'B' => 
      array (
        0 => '84',
        1 => '-225',
        2 => '196',
        3 => '775',
      ),
    ),
    125 => 
    array (
      'C' => '125',
      'WX' => '389',
      'N' => 'braceright',
      'B' => 
      array (
        0 => '24',
        1 => '-196',
        2 => '341',
        3 => '722',
      ),
    ),
    'braceright' => 
    array (
      'C' => '125',
      'WX' => '389',
      'N' => 'braceright',
      'B' => 
      array (
        0 => '24',
        1 => '-196',
        2 => '341',
        3 => '722',
      ),
    ),
    126 => 
    array (
      'C' => '126',
      'WX' => '584',
      'N' => 'asciitilde',
      'B' => 
      array (
        0 => '61',
        1 => '163',
        2 => '523',
        3 => '343',
      ),
    ),
    'asciitilde' => 
    array (
      'C' => '126',
      'WX' => '584',
      'N' => 'asciitilde',
      'B' => 
      array (
        0 => '61',
        1 => '163',
        2 => '523',
        3 => '343',
      ),
    ),
    161 => 
    array (
      'C' => '161',
      'WX' => '333',
      'N' => 'exclamdown',
      'B' => 
      array (
        0 => '90',
        1 => '-186',
        2 => '244',
        3 => '532',
      ),
    ),
    'exclamdown' => 
    array (
      'C' => '161',
      'WX' => '333',
      'N' => 'exclamdown',
      'B' => 
      array (
        0 => '90',
        1 => '-186',
        2 => '244',
        3 => '532',
      ),
    ),
    162 => 
    array (
      'C' => '162',
      'WX' => '556',
      'N' => 'cent',
      'B' => 
      array (
        0 => '34',
        1 => '-118',
        2 => '524',
        3 => '628',
      ),
    ),
    'cent' => 
    array (
      'C' => '162',
      'WX' => '556',
      'N' => 'cent',
      'B' => 
      array (
        0 => '34',
        1 => '-118',
        2 => '524',
        3 => '628',
      ),
    ),
    163 => 
    array (
      'C' => '163',
      'WX' => '556',
      'N' => 'sterling',
      'B' => 
      array (
        0 => '28',
        1 => '-16',
        2 => '541',
        3 => '718',
      ),
    ),
    'sterling' => 
    array (
      'C' => '163',
      'WX' => '556',
      'N' => 'sterling',
      'B' => 
      array (
        0 => '28',
        1 => '-16',
        2 => '541',
        3 => '718',
      ),
    ),
    164 => 
    array (
      'C' => '164',
      'WX' => '167',
      'N' => 'fraction',
      'B' => 
      array (
        0 => '-170',
        1 => '-19',
        2 => '336',
        3 => '710',
      ),
    ),
    'fraction' => 
    array (
      'C' => '164',
      'WX' => '167',
      'N' => 'fraction',
      'B' => 
      array (
        0 => '-170',
        1 => '-19',
        2 => '336',
        3 => '710',
      ),
    ),
    165 => 
    array (
      'C' => '165',
      'WX' => '556',
      'N' => 'yen',
      'B' => 
      array (
        0 => '-9',
        1 => '0',
        2 => '565',
        3 => '698',
      ),
    ),
    'yen' => 
    array (
      'C' => '165',
      'WX' => '556',
      'N' => 'yen',
      'B' => 
      array (
        0 => '-9',
        1 => '0',
        2 => '565',
        3 => '698',
      ),
    ),
    166 => 
    array (
      'C' => '166',
      'WX' => '556',
      'N' => 'florin',
      'B' => 
      array (
        0 => '-10',
        1 => '-210',
        2 => '516',
        3 => '737',
      ),
    ),
    'florin' => 
    array (
      'C' => '166',
      'WX' => '556',
      'N' => 'florin',
      'B' => 
      array (
        0 => '-10',
        1 => '-210',
        2 => '516',
        3 => '737',
      ),
    ),
    167 => 
    array (
      'C' => '167',
      'WX' => '556',
      'N' => 'section',
      'B' => 
      array (
        0 => '34',
        1 => '-184',
        2 => '522',
        3 => '727',
      ),
    ),
    'section' => 
    array (
      'C' => '167',
      'WX' => '556',
      'N' => 'section',
      'B' => 
      array (
        0 => '34',
        1 => '-184',
        2 => '522',
        3 => '727',
      ),
    ),
    168 => 
    array (
      'C' => '168',
      'WX' => '556',
      'N' => 'currency',
      'B' => 
      array (
        0 => '-3',
        1 => '76',
        2 => '559',
        3 => '636',
      ),
    ),
    'currency' => 
    array (
      'C' => '168',
      'WX' => '556',
      'N' => 'currency',
      'B' => 
      array (
        0 => '-3',
        1 => '76',
        2 => '559',
        3 => '636',
      ),
    ),
    169 => 
    array (
      'C' => '169',
      'WX' => '238',
      'N' => 'quotesingle',
      'B' => 
      array (
        0 => '70',
        1 => '447',
        2 => '168',
        3 => '718',
      ),
    ),
    'quotesingle' => 
    array (
      'C' => '169',
      'WX' => '238',
      'N' => 'quotesingle',
      'B' => 
      array (
        0 => '70',
        1 => '447',
        2 => '168',
        3 => '718',
      ),
    ),
    170 => 
    array (
      'C' => '170',
      'WX' => '500',
      'N' => 'quotedblleft',
      'B' => 
      array (
        0 => '64',
        1 => '454',
        2 => '436',
        3 => '727',
      ),
    ),
    'quotedblleft' => 
    array (
      'C' => '170',
      'WX' => '500',
      'N' => 'quotedblleft',
      'B' => 
      array (
        0 => '64',
        1 => '454',
        2 => '436',
        3 => '727',
      ),
    ),
    171 => 
    array (
      'C' => '171',
      'WX' => '556',
      'N' => 'guillemotleft',
      'B' => 
      array (
        0 => '88',
        1 => '76',
        2 => '468',
        3 => '484',
      ),
    ),
    'guillemotleft' => 
    array (
      'C' => '171',
      'WX' => '556',
      'N' => 'guillemotleft',
      'B' => 
      array (
        0 => '88',
        1 => '76',
        2 => '468',
        3 => '484',
      ),
    ),
    172 => 
    array (
      'C' => '172',
      'WX' => '333',
      'N' => 'guilsinglleft',
      'B' => 
      array (
        0 => '83',
        1 => '76',
        2 => '250',
        3 => '484',
      ),
    ),
    'guilsinglleft' => 
    array (
      'C' => '172',
      'WX' => '333',
      'N' => 'guilsinglleft',
      'B' => 
      array (
        0 => '83',
        1 => '76',
        2 => '250',
        3 => '484',
      ),
    ),
    173 => 
    array (
      'C' => '173',
      'WX' => '333',
      'N' => 'guilsinglright',
      'B' => 
      array (
        0 => '83',
        1 => '76',
        2 => '250',
        3 => '484',
      ),
    ),
    'guilsinglright' => 
    array (
      'C' => '173',
      'WX' => '333',
      'N' => 'guilsinglright',
      'B' => 
      array (
        0 => '83',
        1 => '76',
        2 => '250',
        3 => '484',
      ),
    ),
    174 => 
    array (
      'C' => '174',
      'WX' => '611',
      'N' => 'fi',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '542',
        3 => '727',
      ),
    ),
    'fi' => 
    array (
      'C' => '174',
      'WX' => '611',
      'N' => 'fi',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '542',
        3 => '727',
      ),
    ),
    175 => 
    array (
      'C' => '175',
      'WX' => '611',
      'N' => 'fl',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '542',
        3 => '727',
      ),
    ),
    'fl' => 
    array (
      'C' => '175',
      'WX' => '611',
      'N' => 'fl',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '542',
        3 => '727',
      ),
    ),
    177 => 
    array (
      'C' => '177',
      'WX' => '556',
      'N' => 'endash',
      'B' => 
      array (
        0 => '0',
        1 => '227',
        2 => '556',
        3 => '333',
      ),
    ),
    'endash' => 
    array (
      'C' => '177',
      'WX' => '556',
      'N' => 'endash',
      'B' => 
      array (
        0 => '0',
        1 => '227',
        2 => '556',
        3 => '333',
      ),
    ),
    178 => 
    array (
      'C' => '178',
      'WX' => '556',
      'N' => 'dagger',
      'B' => 
      array (
        0 => '36',
        1 => '-171',
        2 => '520',
        3 => '718',
      ),
    ),
    'dagger' => 
    array (
      'C' => '178',
      'WX' => '556',
      'N' => 'dagger',
      'B' => 
      array (
        0 => '36',
        1 => '-171',
        2 => '520',
        3 => '718',
      ),
    ),
    179 => 
    array (
      'C' => '179',
      'WX' => '556',
      'N' => 'daggerdbl',
      'B' => 
      array (
        0 => '36',
        1 => '-171',
        2 => '520',
        3 => '718',
      ),
    ),
    'daggerdbl' => 
    array (
      'C' => '179',
      'WX' => '556',
      'N' => 'daggerdbl',
      'B' => 
      array (
        0 => '36',
        1 => '-171',
        2 => '520',
        3 => '718',
      ),
    ),
    180 => 
    array (
      'C' => '180',
      'WX' => '278',
      'N' => 'periodcentered',
      'B' => 
      array (
        0 => '58',
        1 => '172',
        2 => '220',
        3 => '334',
      ),
    ),
    'periodcentered' => 
    array (
      'C' => '180',
      'WX' => '278',
      'N' => 'periodcentered',
      'B' => 
      array (
        0 => '58',
        1 => '172',
        2 => '220',
        3 => '334',
      ),
    ),
    182 => 
    array (
      'C' => '182',
      'WX' => '556',
      'N' => 'paragraph',
      'B' => 
      array (
        0 => '-8',
        1 => '-191',
        2 => '539',
        3 => '700',
      ),
    ),
    'paragraph' => 
    array (
      'C' => '182',
      'WX' => '556',
      'N' => 'paragraph',
      'B' => 
      array (
        0 => '-8',
        1 => '-191',
        2 => '539',
        3 => '700',
      ),
    ),
    183 => 
    array (
      'C' => '183',
      'WX' => '350',
      'N' => 'bullet',
      'B' => 
      array (
        0 => '10',
        1 => '194',
        2 => '340',
        3 => '524',
      ),
    ),
    'bullet' => 
    array (
      'C' => '183',
      'WX' => '350',
      'N' => 'bullet',
      'B' => 
      array (
        0 => '10',
        1 => '194',
        2 => '340',
        3 => '524',
      ),
    ),
    184 => 
    array (
      'C' => '184',
      'WX' => '278',
      'N' => 'quotesinglbase',
      'B' => 
      array (
        0 => '69',
        1 => '-146',
        2 => '209',
        3 => '127',
      ),
    ),
    'quotesinglbase' => 
    array (
      'C' => '184',
      'WX' => '278',
      'N' => 'quotesinglbase',
      'B' => 
      array (
        0 => '69',
        1 => '-146',
        2 => '209',
        3 => '127',
      ),
    ),
    185 => 
    array (
      'C' => '185',
      'WX' => '500',
      'N' => 'quotedblbase',
      'B' => 
      array (
        0 => '64',
        1 => '-146',
        2 => '436',
        3 => '127',
      ),
    ),
    'quotedblbase' => 
    array (
      'C' => '185',
      'WX' => '500',
      'N' => 'quotedblbase',
      'B' => 
      array (
        0 => '64',
        1 => '-146',
        2 => '436',
        3 => '127',
      ),
    ),
    186 => 
    array (
      'C' => '186',
      'WX' => '500',
      'N' => 'quotedblright',
      'B' => 
      array (
        0 => '64',
        1 => '445',
        2 => '436',
        3 => '718',
      ),
    ),
    'quotedblright' => 
    array (
      'C' => '186',
      'WX' => '500',
      'N' => 'quotedblright',
      'B' => 
      array (
        0 => '64',
        1 => '445',
        2 => '436',
        3 => '718',
      ),
    ),
    187 => 
    array (
      'C' => '187',
      'WX' => '556',
      'N' => 'guillemotright',
      'B' => 
      array (
        0 => '88',
        1 => '76',
        2 => '468',
        3 => '484',
      ),
    ),
    'guillemotright' => 
    array (
      'C' => '187',
      'WX' => '556',
      'N' => 'guillemotright',
      'B' => 
      array (
        0 => '88',
        1 => '76',
        2 => '468',
        3 => '484',
      ),
    ),
    188 => 
    array (
      'C' => '188',
      'WX' => '1000',
      'N' => 'ellipsis',
      'B' => 
      array (
        0 => '92',
        1 => '0',
        2 => '908',
        3 => '146',
      ),
    ),
    'ellipsis' => 
    array (
      'C' => '188',
      'WX' => '1000',
      'N' => 'ellipsis',
      'B' => 
      array (
        0 => '92',
        1 => '0',
        2 => '908',
        3 => '146',
      ),
    ),
    189 => 
    array (
      'C' => '189',
      'WX' => '1000',
      'N' => 'perthousand',
      'B' => 
      array (
        0 => '-3',
        1 => '-19',
        2 => '1003',
        3 => '710',
      ),
    ),
    'perthousand' => 
    array (
      'C' => '189',
      'WX' => '1000',
      'N' => 'perthousand',
      'B' => 
      array (
        0 => '-3',
        1 => '-19',
        2 => '1003',
        3 => '710',
      ),
    ),
    191 => 
    array (
      'C' => '191',
      'WX' => '611',
      'N' => 'questiondown',
      'B' => 
      array (
        0 => '55',
        1 => '-195',
        2 => '551',
        3 => '532',
      ),
    ),
    'questiondown' => 
    array (
      'C' => '191',
      'WX' => '611',
      'N' => 'questiondown',
      'B' => 
      array (
        0 => '55',
        1 => '-195',
        2 => '551',
        3 => '532',
      ),
    ),
    193 => 
    array (
      'C' => '193',
      'WX' => '333',
      'N' => 'grave',
      'B' => 
      array (
        0 => '-23',
        1 => '604',
        2 => '225',
        3 => '750',
      ),
    ),
    'grave' => 
    array (
      'C' => '193',
      'WX' => '333',
      'N' => 'grave',
      'B' => 
      array (
        0 => '-23',
        1 => '604',
        2 => '225',
        3 => '750',
      ),
    ),
    194 => 
    array (
      'C' => '194',
      'WX' => '333',
      'N' => 'acute',
      'B' => 
      array (
        0 => '108',
        1 => '604',
        2 => '356',
        3 => '750',
      ),
    ),
    'acute' => 
    array (
      'C' => '194',
      'WX' => '333',
      'N' => 'acute',
      'B' => 
      array (
        0 => '108',
        1 => '604',
        2 => '356',
        3 => '750',
      ),
    ),
    195 => 
    array (
      'C' => '195',
      'WX' => '333',
      'N' => 'circumflex',
      'B' => 
      array (
        0 => '-10',
        1 => '604',
        2 => '343',
        3 => '750',
      ),
    ),
    'circumflex' => 
    array (
      'C' => '195',
      'WX' => '333',
      'N' => 'circumflex',
      'B' => 
      array (
        0 => '-10',
        1 => '604',
        2 => '343',
        3 => '750',
      ),
    ),
    196 => 
    array (
      'C' => '196',
      'WX' => '333',
      'N' => 'tilde',
      'B' => 
      array (
        0 => '-17',
        1 => '610',
        2 => '350',
        3 => '737',
      ),
    ),
    'tilde' => 
    array (
      'C' => '196',
      'WX' => '333',
      'N' => 'tilde',
      'B' => 
      array (
        0 => '-17',
        1 => '610',
        2 => '350',
        3 => '737',
      ),
    ),
    197 => 
    array (
      'C' => '197',
      'WX' => '333',
      'N' => 'macron',
      'B' => 
      array (
        0 => '-6',
        1 => '604',
        2 => '339',
        3 => '678',
      ),
    ),
    'macron' => 
    array (
      'C' => '197',
      'WX' => '333',
      'N' => 'macron',
      'B' => 
      array (
        0 => '-6',
        1 => '604',
        2 => '339',
        3 => '678',
      ),
    ),
    198 => 
    array (
      'C' => '198',
      'WX' => '333',
      'N' => 'breve',
      'B' => 
      array (
        0 => '-2',
        1 => '604',
        2 => '335',
        3 => '750',
      ),
    ),
    'breve' => 
    array (
      'C' => '198',
      'WX' => '333',
      'N' => 'breve',
      'B' => 
      array (
        0 => '-2',
        1 => '604',
        2 => '335',
        3 => '750',
      ),
    ),
    199 => 
    array (
      'C' => '199',
      'WX' => '333',
      'N' => 'dotaccent',
      'B' => 
      array (
        0 => '104',
        1 => '614',
        2 => '230',
        3 => '729',
      ),
    ),
    'dotaccent' => 
    array (
      'C' => '199',
      'WX' => '333',
      'N' => 'dotaccent',
      'B' => 
      array (
        0 => '104',
        1 => '614',
        2 => '230',
        3 => '729',
      ),
    ),
    200 => 
    array (
      'C' => '200',
      'WX' => '333',
      'N' => 'dieresis',
      'B' => 
      array (
        0 => '6',
        1 => '614',
        2 => '327',
        3 => '729',
      ),
    ),
    'dieresis' => 
    array (
      'C' => '200',
      'WX' => '333',
      'N' => 'dieresis',
      'B' => 
      array (
        0 => '6',
        1 => '614',
        2 => '327',
        3 => '729',
      ),
    ),
    202 => 
    array (
      'C' => '202',
      'WX' => '333',
      'N' => 'ring',
      'B' => 
      array (
        0 => '59',
        1 => '568',
        2 => '275',
        3 => '776',
      ),
    ),
    'ring' => 
    array (
      'C' => '202',
      'WX' => '333',
      'N' => 'ring',
      'B' => 
      array (
        0 => '59',
        1 => '568',
        2 => '275',
        3 => '776',
      ),
    ),
    203 => 
    array (
      'C' => '203',
      'WX' => '333',
      'N' => 'cedilla',
      'B' => 
      array (
        0 => '6',
        1 => '-228',
        2 => '245',
        3 => '0',
      ),
    ),
    'cedilla' => 
    array (
      'C' => '203',
      'WX' => '333',
      'N' => 'cedilla',
      'B' => 
      array (
        0 => '6',
        1 => '-228',
        2 => '245',
        3 => '0',
      ),
    ),
    205 => 
    array (
      'C' => '205',
      'WX' => '333',
      'N' => 'hungarumlaut',
      'B' => 
      array (
        0 => '9',
        1 => '604',
        2 => '486',
        3 => '750',
      ),
    ),
    'hungarumlaut' => 
    array (
      'C' => '205',
      'WX' => '333',
      'N' => 'hungarumlaut',
      'B' => 
      array (
        0 => '9',
        1 => '604',
        2 => '486',
        3 => '750',
      ),
    ),
    206 => 
    array (
      'C' => '206',
      'WX' => '333',
      'N' => 'ogonek',
      'B' => 
      array (
        0 => '71',
        1 => '-228',
        2 => '304',
        3 => '0',
      ),
    ),
    'ogonek' => 
    array (
      'C' => '206',
      'WX' => '333',
      'N' => 'ogonek',
      'B' => 
      array (
        0 => '71',
        1 => '-228',
        2 => '304',
        3 => '0',
      ),
    ),
    207 => 
    array (
      'C' => '207',
      'WX' => '333',
      'N' => 'caron',
      'B' => 
      array (
        0 => '-10',
        1 => '604',
        2 => '343',
        3 => '750',
      ),
    ),
    'caron' => 
    array (
      'C' => '207',
      'WX' => '333',
      'N' => 'caron',
      'B' => 
      array (
        0 => '-10',
        1 => '604',
        2 => '343',
        3 => '750',
      ),
    ),
    208 => 
    array (
      'C' => '208',
      'WX' => '1000',
      'N' => 'emdash',
      'B' => 
      array (
        0 => '0',
        1 => '227',
        2 => '1000',
        3 => '333',
      ),
    ),
    'emdash' => 
    array (
      'C' => '208',
      'WX' => '1000',
      'N' => 'emdash',
      'B' => 
      array (
        0 => '0',
        1 => '227',
        2 => '1000',
        3 => '333',
      ),
    ),
    225 => 
    array (
      'C' => '225',
      'WX' => '1000',
      'N' => 'AE',
      'B' => 
      array (
        0 => '5',
        1 => '0',
        2 => '954',
        3 => '718',
      ),
    ),
    'AE' => 
    array (
      'C' => '225',
      'WX' => '1000',
      'N' => 'AE',
      'B' => 
      array (
        0 => '5',
        1 => '0',
        2 => '954',
        3 => '718',
      ),
    ),
    227 => 
    array (
      'C' => '227',
      'WX' => '370',
      'N' => 'ordfeminine',
      'B' => 
      array (
        0 => '22',
        1 => '401',
        2 => '347',
        3 => '737',
      ),
    ),
    'ordfeminine' => 
    array (
      'C' => '227',
      'WX' => '370',
      'N' => 'ordfeminine',
      'B' => 
      array (
        0 => '22',
        1 => '401',
        2 => '347',
        3 => '737',
      ),
    ),
    232 => 
    array (
      'C' => '232',
      'WX' => '611',
      'N' => 'Lslash',
      'B' => 
      array (
        0 => '-20',
        1 => '0',
        2 => '583',
        3 => '718',
      ),
    ),
    'Lslash' => 
    array (
      'C' => '232',
      'WX' => '611',
      'N' => 'Lslash',
      'B' => 
      array (
        0 => '-20',
        1 => '0',
        2 => '583',
        3 => '718',
      ),
    ),
    233 => 
    array (
      'C' => '233',
      'WX' => '778',
      'N' => 'Oslash',
      'B' => 
      array (
        0 => '33',
        1 => '-27',
        2 => '744',
        3 => '745',
      ),
    ),
    'Oslash' => 
    array (
      'C' => '233',
      'WX' => '778',
      'N' => 'Oslash',
      'B' => 
      array (
        0 => '33',
        1 => '-27',
        2 => '744',
        3 => '745',
      ),
    ),
    234 => 
    array (
      'C' => '234',
      'WX' => '1000',
      'N' => 'OE',
      'B' => 
      array (
        0 => '37',
        1 => '-19',
        2 => '961',
        3 => '737',
      ),
    ),
    'OE' => 
    array (
      'C' => '234',
      'WX' => '1000',
      'N' => 'OE',
      'B' => 
      array (
        0 => '37',
        1 => '-19',
        2 => '961',
        3 => '737',
      ),
    ),
    235 => 
    array (
      'C' => '235',
      'WX' => '365',
      'N' => 'ordmasculine',
      'B' => 
      array (
        0 => '6',
        1 => '401',
        2 => '360',
        3 => '737',
      ),
    ),
    'ordmasculine' => 
    array (
      'C' => '235',
      'WX' => '365',
      'N' => 'ordmasculine',
      'B' => 
      array (
        0 => '6',
        1 => '401',
        2 => '360',
        3 => '737',
      ),
    ),
    241 => 
    array (
      'C' => '241',
      'WX' => '889',
      'N' => 'ae',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '858',
        3 => '546',
      ),
    ),
    'ae' => 
    array (
      'C' => '241',
      'WX' => '889',
      'N' => 'ae',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '858',
        3 => '546',
      ),
    ),
    245 => 
    array (
      'C' => '245',
      'WX' => '278',
      'N' => 'dotlessi',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '209',
        3 => '532',
      ),
    ),
    'dotlessi' => 
    array (
      'C' => '245',
      'WX' => '278',
      'N' => 'dotlessi',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '209',
        3 => '532',
      ),
    ),
    248 => 
    array (
      'C' => '248',
      'WX' => '278',
      'N' => 'lslash',
      'B' => 
      array (
        0 => '-18',
        1 => '0',
        2 => '296',
        3 => '718',
      ),
    ),
    'lslash' => 
    array (
      'C' => '248',
      'WX' => '278',
      'N' => 'lslash',
      'B' => 
      array (
        0 => '-18',
        1 => '0',
        2 => '296',
        3 => '718',
      ),
    ),
    249 => 
    array (
      'C' => '249',
      'WX' => '611',
      'N' => 'oslash',
      'B' => 
      array (
        0 => '22',
        1 => '-29',
        2 => '589',
        3 => '560',
      ),
    ),
    'oslash' => 
    array (
      'C' => '249',
      'WX' => '611',
      'N' => 'oslash',
      'B' => 
      array (
        0 => '22',
        1 => '-29',
        2 => '589',
        3 => '560',
      ),
    ),
    250 => 
    array (
      'C' => '250',
      'WX' => '944',
      'N' => 'oe',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '912',
        3 => '546',
      ),
    ),
    'oe' => 
    array (
      'C' => '250',
      'WX' => '944',
      'N' => 'oe',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '912',
        3 => '546',
      ),
    ),
    251 => 
    array (
      'C' => '251',
      'WX' => '611',
      'N' => 'germandbls',
      'B' => 
      array (
        0 => '69',
        1 => '-14',
        2 => '579',
        3 => '731',
      ),
    ),
    'germandbls' => 
    array (
      'C' => '251',
      'WX' => '611',
      'N' => 'germandbls',
      'B' => 
      array (
        0 => '69',
        1 => '-14',
        2 => '579',
        3 => '731',
      ),
    ),
    'Idieresis' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Idieresis',
      'B' => 
      array (
        0 => '-21',
        1 => '0',
        2 => '300',
        3 => '915',
      ),
    ),
    'eacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'eacute',
      'B' => 
      array (
        0 => '23',
        1 => '-14',
        2 => '528',
        3 => '750',
      ),
    ),
    'abreve' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'abreve',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '527',
        3 => '750',
      ),
    ),
    'uhungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'uhungarumlaut',
      'B' => 
      array (
        0 => '66',
        1 => '-14',
        2 => '625',
        3 => '750',
      ),
    ),
    'ecaron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ecaron',
      'B' => 
      array (
        0 => '23',
        1 => '-14',
        2 => '528',
        3 => '750',
      ),
    ),
    'Ydieresis' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ydieresis',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '653',
        3 => '915',
      ),
    ),
    'divide' => 
    array (
      'C' => '-1',
      'WX' => '584',
      'N' => 'divide',
      'B' => 
      array (
        0 => '40',
        1 => '-42',
        2 => '544',
        3 => '548',
      ),
    ),
    'Yacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Yacute',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '653',
        3 => '936',
      ),
    ),
    'Acircumflex' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Acircumflex',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '702',
        3 => '936',
      ),
    ),
    'aacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'aacute',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '527',
        3 => '750',
      ),
    ),
    'Ucircumflex' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ucircumflex',
      'B' => 
      array (
        0 => '72',
        1 => '-19',
        2 => '651',
        3 => '936',
      ),
    ),
    'yacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'yacute',
      'B' => 
      array (
        0 => '10',
        1 => '-214',
        2 => '539',
        3 => '750',
      ),
    ),
    'scommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'scommaaccent',
      'B' => 
      array (
        0 => '30',
        1 => '-228',
        2 => '519',
        3 => '546',
      ),
    ),
    'ecircumflex' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ecircumflex',
      'B' => 
      array (
        0 => '23',
        1 => '-14',
        2 => '528',
        3 => '750',
      ),
    ),
    'Uring' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uring',
      'B' => 
      array (
        0 => '72',
        1 => '-19',
        2 => '651',
        3 => '962',
      ),
    ),
    'Udieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Udieresis',
      'B' => 
      array (
        0 => '72',
        1 => '-19',
        2 => '651',
        3 => '915',
      ),
    ),
    'aogonek' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'aogonek',
      'B' => 
      array (
        0 => '29',
        1 => '-224',
        2 => '545',
        3 => '546',
      ),
    ),
    'Uacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uacute',
      'B' => 
      array (
        0 => '72',
        1 => '-19',
        2 => '651',
        3 => '936',
      ),
    ),
    'uogonek' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'uogonek',
      'B' => 
      array (
        0 => '66',
        1 => '-228',
        2 => '545',
        3 => '532',
      ),
    ),
    'Edieresis' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Edieresis',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '621',
        3 => '915',
      ),
    ),
    'Dcroat' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Dcroat',
      'B' => 
      array (
        0 => '-5',
        1 => '0',
        2 => '685',
        3 => '718',
      ),
    ),
    'commaaccent' => 
    array (
      'C' => '-1',
      'WX' => '250',
      'N' => 'commaaccent',
      'B' => 
      array (
        0 => '64',
        1 => '-228',
        2 => '199',
        3 => '-50',
      ),
    ),
    'copyright' => 
    array (
      'C' => '-1',
      'WX' => '737',
      'N' => 'copyright',
      'B' => 
      array (
        0 => '-11',
        1 => '-19',
        2 => '749',
        3 => '737',
      ),
    ),
    'Emacron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Emacron',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '621',
        3 => '864',
      ),
    ),
    'ccaron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ccaron',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '524',
        3 => '750',
      ),
    ),
    'aring' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'aring',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '527',
        3 => '776',
      ),
    ),
    'Ncommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ncommaaccent',
      'B' => 
      array (
        0 => '69',
        1 => '-228',
        2 => '654',
        3 => '718',
      ),
    ),
    'lacute' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'lacute',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '329',
        3 => '936',
      ),
    ),
    'agrave' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'agrave',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '527',
        3 => '750',
      ),
    ),
    'Tcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Tcommaaccent',
      'B' => 
      array (
        0 => '14',
        1 => '-228',
        2 => '598',
        3 => '718',
      ),
    ),
    'Cacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Cacute',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '684',
        3 => '936',
      ),
    ),
    'atilde' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'atilde',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '527',
        3 => '737',
      ),
    ),
    'Edotaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Edotaccent',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '621',
        3 => '915',
      ),
    ),
    'scaron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'scaron',
      'B' => 
      array (
        0 => '30',
        1 => '-14',
        2 => '519',
        3 => '750',
      ),
    ),
    'scedilla' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'scedilla',
      'B' => 
      array (
        0 => '30',
        1 => '-228',
        2 => '519',
        3 => '546',
      ),
    ),
    'iacute' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'iacute',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '329',
        3 => '750',
      ),
    ),
    'lozenge' => 
    array (
      'C' => '-1',
      'WX' => '494',
      'N' => 'lozenge',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '484',
        3 => '745',
      ),
    ),
    'Rcaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Rcaron',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '677',
        3 => '936',
      ),
    ),
    'Gcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Gcommaaccent',
      'B' => 
      array (
        0 => '44',
        1 => '-228',
        2 => '713',
        3 => '737',
      ),
    ),
    'ucircumflex' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'ucircumflex',
      'B' => 
      array (
        0 => '66',
        1 => '-14',
        2 => '545',
        3 => '750',
      ),
    ),
    'acircumflex' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'acircumflex',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '527',
        3 => '750',
      ),
    ),
    'Amacron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Amacron',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '702',
        3 => '864',
      ),
    ),
    'rcaron' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'rcaron',
      'B' => 
      array (
        0 => '18',
        1 => '0',
        2 => '373',
        3 => '750',
      ),
    ),
    'ccedilla' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ccedilla',
      'B' => 
      array (
        0 => '34',
        1 => '-228',
        2 => '524',
        3 => '546',
      ),
    ),
    'Zdotaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zdotaccent',
      'B' => 
      array (
        0 => '25',
        1 => '0',
        2 => '586',
        3 => '915',
      ),
    ),
    'Thorn' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Thorn',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '627',
        3 => '718',
      ),
    ),
    'Omacron' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Omacron',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '734',
        3 => '864',
      ),
    ),
    'Racute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Racute',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '677',
        3 => '936',
      ),
    ),
    'Sacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Sacute',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '629',
        3 => '936',
      ),
    ),
    'dcaron' => 
    array (
      'C' => '-1',
      'WX' => '743',
      'N' => 'dcaron',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '750',
        3 => '718',
      ),
    ),
    'Umacron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Umacron',
      'B' => 
      array (
        0 => '72',
        1 => '-19',
        2 => '651',
        3 => '864',
      ),
    ),
    'uring' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'uring',
      'B' => 
      array (
        0 => '66',
        1 => '-14',
        2 => '545',
        3 => '776',
      ),
    ),
    'threesuperior' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'threesuperior',
      'B' => 
      array (
        0 => '8',
        1 => '271',
        2 => '326',
        3 => '710',
      ),
    ),
    'Ograve' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Ograve',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '734',
        3 => '936',
      ),
    ),
    'Agrave' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Agrave',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '702',
        3 => '936',
      ),
    ),
    'Abreve' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Abreve',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '702',
        3 => '936',
      ),
    ),
    'multiply' => 
    array (
      'C' => '-1',
      'WX' => '584',
      'N' => 'multiply',
      'B' => 
      array (
        0 => '40',
        1 => '1',
        2 => '545',
        3 => '505',
      ),
    ),
    'uacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'uacute',
      'B' => 
      array (
        0 => '66',
        1 => '-14',
        2 => '545',
        3 => '750',
      ),
    ),
    'Tcaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Tcaron',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '598',
        3 => '936',
      ),
    ),
    'partialdiff' => 
    array (
      'C' => '-1',
      'WX' => '494',
      'N' => 'partialdiff',
      'B' => 
      array (
        0 => '11',
        1 => '-21',
        2 => '494',
        3 => '750',
      ),
    ),
    'ydieresis' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ydieresis',
      'B' => 
      array (
        0 => '10',
        1 => '-214',
        2 => '539',
        3 => '729',
      ),
    ),
    'Nacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Nacute',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '654',
        3 => '936',
      ),
    ),
    'icircumflex' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'icircumflex',
      'B' => 
      array (
        0 => '-37',
        1 => '0',
        2 => '316',
        3 => '750',
      ),
    ),
    'Ecircumflex' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ecircumflex',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '621',
        3 => '936',
      ),
    ),
    'adieresis' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'adieresis',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '527',
        3 => '729',
      ),
    ),
    'edieresis' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'edieresis',
      'B' => 
      array (
        0 => '23',
        1 => '-14',
        2 => '528',
        3 => '729',
      ),
    ),
    'cacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'cacute',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '524',
        3 => '750',
      ),
    ),
    'nacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'nacute',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '546',
        3 => '750',
      ),
    ),
    'umacron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'umacron',
      'B' => 
      array (
        0 => '66',
        1 => '-14',
        2 => '545',
        3 => '678',
      ),
    ),
    'Ncaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ncaron',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '654',
        3 => '936',
      ),
    ),
    'Iacute' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Iacute',
      'B' => 
      array (
        0 => '64',
        1 => '0',
        2 => '329',
        3 => '936',
      ),
    ),
    'plusminus' => 
    array (
      'C' => '-1',
      'WX' => '584',
      'N' => 'plusminus',
      'B' => 
      array (
        0 => '40',
        1 => '0',
        2 => '544',
        3 => '506',
      ),
    ),
    'brokenbar' => 
    array (
      'C' => '-1',
      'WX' => '280',
      'N' => 'brokenbar',
      'B' => 
      array (
        0 => '84',
        1 => '-150',
        2 => '196',
        3 => '700',
      ),
    ),
    'registered' => 
    array (
      'C' => '-1',
      'WX' => '737',
      'N' => 'registered',
      'B' => 
      array (
        0 => '-11',
        1 => '-19',
        2 => '748',
        3 => '737',
      ),
    ),
    'Gbreve' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Gbreve',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '713',
        3 => '936',
      ),
    ),
    'Idotaccent' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Idotaccent',
      'B' => 
      array (
        0 => '64',
        1 => '0',
        2 => '214',
        3 => '915',
      ),
    ),
    'summation' => 
    array (
      'C' => '-1',
      'WX' => '600',
      'N' => 'summation',
      'B' => 
      array (
        0 => '14',
        1 => '-10',
        2 => '585',
        3 => '706',
      ),
    ),
    'Egrave' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Egrave',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '621',
        3 => '936',
      ),
    ),
    'racute' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'racute',
      'B' => 
      array (
        0 => '64',
        1 => '0',
        2 => '384',
        3 => '750',
      ),
    ),
    'omacron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'omacron',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '578',
        3 => '678',
      ),
    ),
    'Zacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zacute',
      'B' => 
      array (
        0 => '25',
        1 => '0',
        2 => '586',
        3 => '936',
      ),
    ),
    'Zcaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zcaron',
      'B' => 
      array (
        0 => '25',
        1 => '0',
        2 => '586',
        3 => '936',
      ),
    ),
    'greaterequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'greaterequal',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '523',
        3 => '704',
      ),
    ),
    'Eth' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Eth',
      'B' => 
      array (
        0 => '-5',
        1 => '0',
        2 => '685',
        3 => '718',
      ),
    ),
    'Ccedilla' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ccedilla',
      'B' => 
      array (
        0 => '44',
        1 => '-228',
        2 => '684',
        3 => '737',
      ),
    ),
    'lcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'lcommaaccent',
      'B' => 
      array (
        0 => '69',
        1 => '-228',
        2 => '213',
        3 => '718',
      ),
    ),
    'tcaron' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'tcaron',
      'B' => 
      array (
        0 => '10',
        1 => '-6',
        2 => '421',
        3 => '878',
      ),
    ),
    'eogonek' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'eogonek',
      'B' => 
      array (
        0 => '23',
        1 => '-228',
        2 => '528',
        3 => '546',
      ),
    ),
    'Uogonek' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uogonek',
      'B' => 
      array (
        0 => '72',
        1 => '-228',
        2 => '651',
        3 => '718',
      ),
    ),
    'Aacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Aacute',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '702',
        3 => '936',
      ),
    ),
    'Adieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Adieresis',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '702',
        3 => '915',
      ),
    ),
    'egrave' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'egrave',
      'B' => 
      array (
        0 => '23',
        1 => '-14',
        2 => '528',
        3 => '750',
      ),
    ),
    'zacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'zacute',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '480',
        3 => '750',
      ),
    ),
    'iogonek' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'iogonek',
      'B' => 
      array (
        0 => '16',
        1 => '-224',
        2 => '249',
        3 => '725',
      ),
    ),
    'Oacute' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Oacute',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '734',
        3 => '936',
      ),
    ),
    'oacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'oacute',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '578',
        3 => '750',
      ),
    ),
    'amacron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'amacron',
      'B' => 
      array (
        0 => '29',
        1 => '-14',
        2 => '527',
        3 => '678',
      ),
    ),
    'sacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'sacute',
      'B' => 
      array (
        0 => '30',
        1 => '-14',
        2 => '519',
        3 => '750',
      ),
    ),
    'idieresis' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'idieresis',
      'B' => 
      array (
        0 => '-21',
        1 => '0',
        2 => '300',
        3 => '729',
      ),
    ),
    'Ocircumflex' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Ocircumflex',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '734',
        3 => '936',
      ),
    ),
    'Ugrave' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ugrave',
      'B' => 
      array (
        0 => '72',
        1 => '-19',
        2 => '651',
        3 => '936',
      ),
    ),
    'Delta' => 
    array (
      'C' => '-1',
      'WX' => '612',
      'N' => 'Delta',
      'B' => 
      array (
        0 => '6',
        1 => '0',
        2 => '608',
        3 => '688',
      ),
    ),
    'thorn' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'thorn',
      'B' => 
      array (
        0 => '62',
        1 => '-208',
        2 => '578',
        3 => '718',
      ),
    ),
    'twosuperior' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'twosuperior',
      'B' => 
      array (
        0 => '9',
        1 => '283',
        2 => '324',
        3 => '710',
      ),
    ),
    'Odieresis' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Odieresis',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '734',
        3 => '915',
      ),
    ),
    'mu' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'mu',
      'B' => 
      array (
        0 => '66',
        1 => '-207',
        2 => '545',
        3 => '532',
      ),
    ),
    'igrave' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'igrave',
      'B' => 
      array (
        0 => '-50',
        1 => '0',
        2 => '209',
        3 => '750',
      ),
    ),
    'ohungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'ohungarumlaut',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '625',
        3 => '750',
      ),
    ),
    'Eogonek' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Eogonek',
      'B' => 
      array (
        0 => '76',
        1 => '-224',
        2 => '639',
        3 => '718',
      ),
    ),
    'dcroat' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'dcroat',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '650',
        3 => '718',
      ),
    ),
    'threequarters' => 
    array (
      'C' => '-1',
      'WX' => '834',
      'N' => 'threequarters',
      'B' => 
      array (
        0 => '16',
        1 => '-19',
        2 => '799',
        3 => '710',
      ),
    ),
    'Scedilla' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Scedilla',
      'B' => 
      array (
        0 => '39',
        1 => '-228',
        2 => '629',
        3 => '737',
      ),
    ),
    'lcaron' => 
    array (
      'C' => '-1',
      'WX' => '400',
      'N' => 'lcaron',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '408',
        3 => '718',
      ),
    ),
    'Kcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Kcommaaccent',
      'B' => 
      array (
        0 => '87',
        1 => '-228',
        2 => '722',
        3 => '718',
      ),
    ),
    'Lacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Lacute',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '583',
        3 => '936',
      ),
    ),
    'trademark' => 
    array (
      'C' => '-1',
      'WX' => '1000',
      'N' => 'trademark',
      'B' => 
      array (
        0 => '44',
        1 => '306',
        2 => '956',
        3 => '718',
      ),
    ),
    'edotaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'edotaccent',
      'B' => 
      array (
        0 => '23',
        1 => '-14',
        2 => '528',
        3 => '729',
      ),
    ),
    'Igrave' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Igrave',
      'B' => 
      array (
        0 => '-50',
        1 => '0',
        2 => '214',
        3 => '936',
      ),
    ),
    'Imacron' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Imacron',
      'B' => 
      array (
        0 => '-33',
        1 => '0',
        2 => '312',
        3 => '864',
      ),
    ),
    'Lcaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Lcaron',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '583',
        3 => '718',
      ),
    ),
    'onehalf' => 
    array (
      'C' => '-1',
      'WX' => '834',
      'N' => 'onehalf',
      'B' => 
      array (
        0 => '26',
        1 => '-19',
        2 => '794',
        3 => '710',
      ),
    ),
    'lessequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'lessequal',
      'B' => 
      array (
        0 => '29',
        1 => '0',
        2 => '526',
        3 => '704',
      ),
    ),
    'ocircumflex' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'ocircumflex',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '578',
        3 => '750',
      ),
    ),
    'ntilde' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'ntilde',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '546',
        3 => '737',
      ),
    ),
    'Uhungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uhungarumlaut',
      'B' => 
      array (
        0 => '72',
        1 => '-19',
        2 => '681',
        3 => '936',
      ),
    ),
    'Eacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Eacute',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '621',
        3 => '936',
      ),
    ),
    'emacron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'emacron',
      'B' => 
      array (
        0 => '23',
        1 => '-14',
        2 => '528',
        3 => '678',
      ),
    ),
    'gbreve' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'gbreve',
      'B' => 
      array (
        0 => '40',
        1 => '-217',
        2 => '553',
        3 => '750',
      ),
    ),
    'onequarter' => 
    array (
      'C' => '-1',
      'WX' => '834',
      'N' => 'onequarter',
      'B' => 
      array (
        0 => '26',
        1 => '-19',
        2 => '766',
        3 => '710',
      ),
    ),
    'Scaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Scaron',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '629',
        3 => '936',
      ),
    ),
    'Scommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Scommaaccent',
      'B' => 
      array (
        0 => '39',
        1 => '-228',
        2 => '629',
        3 => '737',
      ),
    ),
    'Ohungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Ohungarumlaut',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '734',
        3 => '936',
      ),
    ),
    'degree' => 
    array (
      'C' => '-1',
      'WX' => '400',
      'N' => 'degree',
      'B' => 
      array (
        0 => '57',
        1 => '426',
        2 => '343',
        3 => '712',
      ),
    ),
    'ograve' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'ograve',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '578',
        3 => '750',
      ),
    ),
    'Ccaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ccaron',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '684',
        3 => '936',
      ),
    ),
    'ugrave' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'ugrave',
      'B' => 
      array (
        0 => '66',
        1 => '-14',
        2 => '545',
        3 => '750',
      ),
    ),
    'radical' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'radical',
      'B' => 
      array (
        0 => '10',
        1 => '-46',
        2 => '512',
        3 => '850',
      ),
    ),
    'Dcaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Dcaron',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '685',
        3 => '936',
      ),
    ),
    'rcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'rcommaaccent',
      'B' => 
      array (
        0 => '64',
        1 => '-228',
        2 => '373',
        3 => '546',
      ),
    ),
    'Ntilde' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ntilde',
      'B' => 
      array (
        0 => '69',
        1 => '0',
        2 => '654',
        3 => '923',
      ),
    ),
    'otilde' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'otilde',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '578',
        3 => '737',
      ),
    ),
    'Rcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Rcommaaccent',
      'B' => 
      array (
        0 => '76',
        1 => '-228',
        2 => '677',
        3 => '718',
      ),
    ),
    'Lcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Lcommaaccent',
      'B' => 
      array (
        0 => '76',
        1 => '-228',
        2 => '583',
        3 => '718',
      ),
    ),
    'Atilde' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Atilde',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '702',
        3 => '923',
      ),
    ),
    'Aogonek' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Aogonek',
      'B' => 
      array (
        0 => '20',
        1 => '-224',
        2 => '742',
        3 => '718',
      ),
    ),
    'Aring' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Aring',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '702',
        3 => '962',
      ),
    ),
    'Otilde' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Otilde',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '734',
        3 => '923',
      ),
    ),
    'zdotaccent' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'zdotaccent',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '480',
        3 => '729',
      ),
    ),
    'Ecaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ecaron',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '621',
        3 => '936',
      ),
    ),
    'Iogonek' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Iogonek',
      'B' => 
      array (
        0 => '-11',
        1 => '-228',
        2 => '222',
        3 => '718',
      ),
    ),
    'kcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'kcommaaccent',
      'B' => 
      array (
        0 => '69',
        1 => '-228',
        2 => '562',
        3 => '718',
      ),
    ),
    'minus' => 
    array (
      'C' => '-1',
      'WX' => '584',
      'N' => 'minus',
      'B' => 
      array (
        0 => '40',
        1 => '197',
        2 => '544',
        3 => '309',
      ),
    ),
    'Icircumflex' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Icircumflex',
      'B' => 
      array (
        0 => '-37',
        1 => '0',
        2 => '316',
        3 => '936',
      ),
    ),
    'ncaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'ncaron',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '546',
        3 => '750',
      ),
    ),
    'tcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'tcommaaccent',
      'B' => 
      array (
        0 => '10',
        1 => '-228',
        2 => '309',
        3 => '676',
      ),
    ),
    'logicalnot' => 
    array (
      'C' => '-1',
      'WX' => '584',
      'N' => 'logicalnot',
      'B' => 
      array (
        0 => '40',
        1 => '108',
        2 => '544',
        3 => '419',
      ),
    ),
    'odieresis' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'odieresis',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '578',
        3 => '729',
      ),
    ),
    'udieresis' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'udieresis',
      'B' => 
      array (
        0 => '66',
        1 => '-14',
        2 => '545',
        3 => '729',
      ),
    ),
    'notequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'notequal',
      'B' => 
      array (
        0 => '15',
        1 => '-49',
        2 => '540',
        3 => '570',
      ),
    ),
    'gcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'gcommaaccent',
      'B' => 
      array (
        0 => '40',
        1 => '-217',
        2 => '553',
        3 => '850',
      ),
    ),
    'eth' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'eth',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '578',
        3 => '737',
      ),
    ),
    'zcaron' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'zcaron',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '480',
        3 => '750',
      ),
    ),
    'ncommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'ncommaaccent',
      'B' => 
      array (
        0 => '65',
        1 => '-228',
        2 => '546',
        3 => '546',
      ),
    ),
    'onesuperior' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'onesuperior',
      'B' => 
      array (
        0 => '26',
        1 => '283',
        2 => '237',
        3 => '710',
      ),
    ),
    'imacron' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'imacron',
      'B' => 
      array (
        0 => '-8',
        1 => '0',
        2 => '285',
        3 => '678',
      ),
    ),
    'Euro' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Euro',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
  ),
  'KPX' => 
  array (
    'A' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-50',
      'Gbreve' => '-50',
      'Gcommaaccent' => '-50',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'Q' => '-40',
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-80',
      'W' => '-60',
      'Y' => '-110',
      'Yacute' => '-110',
      'Ydieresis' => '-110',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Aacute' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-50',
      'Gbreve' => '-50',
      'Gcommaaccent' => '-50',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'Q' => '-40',
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-80',
      'W' => '-60',
      'Y' => '-110',
      'Yacute' => '-110',
      'Ydieresis' => '-110',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Abreve' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-50',
      'Gbreve' => '-50',
      'Gcommaaccent' => '-50',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'Q' => '-40',
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-80',
      'W' => '-60',
      'Y' => '-110',
      'Yacute' => '-110',
      'Ydieresis' => '-110',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Acircumflex' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-50',
      'Gbreve' => '-50',
      'Gcommaaccent' => '-50',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'Q' => '-40',
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-80',
      'W' => '-60',
      'Y' => '-110',
      'Yacute' => '-110',
      'Ydieresis' => '-110',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Adieresis' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-50',
      'Gbreve' => '-50',
      'Gcommaaccent' => '-50',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'Q' => '-40',
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-80',
      'W' => '-60',
      'Y' => '-110',
      'Yacute' => '-110',
      'Ydieresis' => '-110',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Agrave' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-50',
      'Gbreve' => '-50',
      'Gcommaaccent' => '-50',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'Q' => '-40',
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-80',
      'W' => '-60',
      'Y' => '-110',
      'Yacute' => '-110',
      'Ydieresis' => '-110',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Amacron' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-50',
      'Gbreve' => '-50',
      'Gcommaaccent' => '-50',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'Q' => '-40',
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-80',
      'W' => '-60',
      'Y' => '-110',
      'Yacute' => '-110',
      'Ydieresis' => '-110',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Aogonek' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-50',
      'Gbreve' => '-50',
      'Gcommaaccent' => '-50',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'Q' => '-40',
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-80',
      'W' => '-60',
      'Y' => '-110',
      'Yacute' => '-110',
      'Ydieresis' => '-110',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Aring' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-50',
      'Gbreve' => '-50',
      'Gcommaaccent' => '-50',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'Q' => '-40',
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-80',
      'W' => '-60',
      'Y' => '-110',
      'Yacute' => '-110',
      'Ydieresis' => '-110',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Atilde' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-50',
      'Gbreve' => '-50',
      'Gcommaaccent' => '-50',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'Q' => '-40',
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-80',
      'W' => '-60',
      'Y' => '-110',
      'Yacute' => '-110',
      'Ydieresis' => '-110',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'B' => 
    array (
      'A' => '-30',
      'Aacute' => '-30',
      'Abreve' => '-30',
      'Acircumflex' => '-30',
      'Adieresis' => '-30',
      'Agrave' => '-30',
      'Amacron' => '-30',
      'Aogonek' => '-30',
      'Aring' => '-30',
      'Atilde' => '-30',
      'U' => '-10',
      'Uacute' => '-10',
      'Ucircumflex' => '-10',
      'Udieresis' => '-10',
      'Ugrave' => '-10',
      'Uhungarumlaut' => '-10',
      'Umacron' => '-10',
      'Uogonek' => '-10',
      'Uring' => '-10',
    ),
    'D' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'V' => '-40',
      'W' => '-40',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-30',
      'period' => '-30',
    ),
    'Dcaron' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'V' => '-40',
      'W' => '-40',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-30',
      'period' => '-30',
    ),
    'Dcroat' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'V' => '-40',
      'W' => '-40',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-30',
      'period' => '-30',
    ),
    'F' => 
    array (
      'A' => '-80',
      'Aacute' => '-80',
      'Abreve' => '-80',
      'Acircumflex' => '-80',
      'Adieresis' => '-80',
      'Agrave' => '-80',
      'Amacron' => '-80',
      'Aogonek' => '-80',
      'Aring' => '-80',
      'Atilde' => '-80',
      'a' => '-20',
      'aacute' => '-20',
      'abreve' => '-20',
      'acircumflex' => '-20',
      'adieresis' => '-20',
      'agrave' => '-20',
      'amacron' => '-20',
      'aogonek' => '-20',
      'aring' => '-20',
      'atilde' => '-20',
      'comma' => '-100',
      'period' => '-100',
    ),
    'J' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
      'comma' => '-20',
      'period' => '-20',
      'u' => '-20',
      'uacute' => '-20',
      'ucircumflex' => '-20',
      'udieresis' => '-20',
      'ugrave' => '-20',
      'uhungarumlaut' => '-20',
      'umacron' => '-20',
      'uogonek' => '-20',
      'uring' => '-20',
    ),
    'K' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'e' => '-15',
      'eacute' => '-15',
      'ecaron' => '-15',
      'ecircumflex' => '-15',
      'edieresis' => '-15',
      'edotaccent' => '-15',
      'egrave' => '-15',
      'emacron' => '-15',
      'eogonek' => '-15',
      'o' => '-35',
      'oacute' => '-35',
      'ocircumflex' => '-35',
      'odieresis' => '-35',
      'ograve' => '-35',
      'ohungarumlaut' => '-35',
      'omacron' => '-35',
      'oslash' => '-35',
      'otilde' => '-35',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'Kcommaaccent' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'e' => '-15',
      'eacute' => '-15',
      'ecaron' => '-15',
      'ecircumflex' => '-15',
      'edieresis' => '-15',
      'edotaccent' => '-15',
      'egrave' => '-15',
      'emacron' => '-15',
      'eogonek' => '-15',
      'o' => '-35',
      'oacute' => '-35',
      'ocircumflex' => '-35',
      'odieresis' => '-35',
      'ograve' => '-35',
      'ohungarumlaut' => '-35',
      'omacron' => '-35',
      'oslash' => '-35',
      'otilde' => '-35',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'L' => 
    array (
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'V' => '-110',
      'W' => '-80',
      'Y' => '-120',
      'Yacute' => '-120',
      'Ydieresis' => '-120',
      'quotedblright' => '-140',
      'quoteright' => '-140',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Lacute' => 
    array (
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'V' => '-110',
      'W' => '-80',
      'Y' => '-120',
      'Yacute' => '-120',
      'Ydieresis' => '-120',
      'quotedblright' => '-140',
      'quoteright' => '-140',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Lcommaaccent' => 
    array (
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'V' => '-110',
      'W' => '-80',
      'Y' => '-120',
      'Yacute' => '-120',
      'Ydieresis' => '-120',
      'quotedblright' => '-140',
      'quoteright' => '-140',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Lslash' => 
    array (
      'T' => '-90',
      'Tcaron' => '-90',
      'Tcommaaccent' => '-90',
      'V' => '-110',
      'W' => '-80',
      'Y' => '-120',
      'Yacute' => '-120',
      'Ydieresis' => '-120',
      'quotedblright' => '-140',
      'quoteright' => '-140',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'O' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-50',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Oacute' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-50',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Ocircumflex' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-50',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Odieresis' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-50',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Ograve' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-50',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Ohungarumlaut' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-50',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Omacron' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-50',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Oslash' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-50',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Otilde' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-50',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'P' => 
    array (
      'A' => '-100',
      'Aacute' => '-100',
      'Abreve' => '-100',
      'Acircumflex' => '-100',
      'Adieresis' => '-100',
      'Agrave' => '-100',
      'Amacron' => '-100',
      'Aogonek' => '-100',
      'Aring' => '-100',
      'Atilde' => '-100',
      'a' => '-30',
      'aacute' => '-30',
      'abreve' => '-30',
      'acircumflex' => '-30',
      'adieresis' => '-30',
      'agrave' => '-30',
      'amacron' => '-30',
      'aogonek' => '-30',
      'aring' => '-30',
      'atilde' => '-30',
      'comma' => '-120',
      'e' => '-30',
      'eacute' => '-30',
      'ecaron' => '-30',
      'ecircumflex' => '-30',
      'edieresis' => '-30',
      'edotaccent' => '-30',
      'egrave' => '-30',
      'emacron' => '-30',
      'eogonek' => '-30',
      'o' => '-40',
      'oacute' => '-40',
      'ocircumflex' => '-40',
      'odieresis' => '-40',
      'ograve' => '-40',
      'ohungarumlaut' => '-40',
      'omacron' => '-40',
      'oslash' => '-40',
      'otilde' => '-40',
      'period' => '-120',
    ),
    'Q' => 
    array (
      'U' => '-10',
      'Uacute' => '-10',
      'Ucircumflex' => '-10',
      'Udieresis' => '-10',
      'Ugrave' => '-10',
      'Uhungarumlaut' => '-10',
      'Umacron' => '-10',
      'Uogonek' => '-10',
      'Uring' => '-10',
      'comma' => '20',
      'period' => '20',
    ),
    'R' => 
    array (
      'O' => '-20',
      'Oacute' => '-20',
      'Ocircumflex' => '-20',
      'Odieresis' => '-20',
      'Ograve' => '-20',
      'Ohungarumlaut' => '-20',
      'Omacron' => '-20',
      'Oslash' => '-20',
      'Otilde' => '-20',
      'T' => '-20',
      'Tcaron' => '-20',
      'Tcommaaccent' => '-20',
      'U' => '-20',
      'Uacute' => '-20',
      'Ucircumflex' => '-20',
      'Udieresis' => '-20',
      'Ugrave' => '-20',
      'Uhungarumlaut' => '-20',
      'Umacron' => '-20',
      'Uogonek' => '-20',
      'Uring' => '-20',
      'V' => '-50',
      'W' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Racute' => 
    array (
      'O' => '-20',
      'Oacute' => '-20',
      'Ocircumflex' => '-20',
      'Odieresis' => '-20',
      'Ograve' => '-20',
      'Ohungarumlaut' => '-20',
      'Omacron' => '-20',
      'Oslash' => '-20',
      'Otilde' => '-20',
      'T' => '-20',
      'Tcaron' => '-20',
      'Tcommaaccent' => '-20',
      'U' => '-20',
      'Uacute' => '-20',
      'Ucircumflex' => '-20',
      'Udieresis' => '-20',
      'Ugrave' => '-20',
      'Uhungarumlaut' => '-20',
      'Umacron' => '-20',
      'Uogonek' => '-20',
      'Uring' => '-20',
      'V' => '-50',
      'W' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Rcaron' => 
    array (
      'O' => '-20',
      'Oacute' => '-20',
      'Ocircumflex' => '-20',
      'Odieresis' => '-20',
      'Ograve' => '-20',
      'Ohungarumlaut' => '-20',
      'Omacron' => '-20',
      'Oslash' => '-20',
      'Otilde' => '-20',
      'T' => '-20',
      'Tcaron' => '-20',
      'Tcommaaccent' => '-20',
      'U' => '-20',
      'Uacute' => '-20',
      'Ucircumflex' => '-20',
      'Udieresis' => '-20',
      'Ugrave' => '-20',
      'Uhungarumlaut' => '-20',
      'Umacron' => '-20',
      'Uogonek' => '-20',
      'Uring' => '-20',
      'V' => '-50',
      'W' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Rcommaaccent' => 
    array (
      'O' => '-20',
      'Oacute' => '-20',
      'Ocircumflex' => '-20',
      'Odieresis' => '-20',
      'Ograve' => '-20',
      'Ohungarumlaut' => '-20',
      'Omacron' => '-20',
      'Oslash' => '-20',
      'Otilde' => '-20',
      'T' => '-20',
      'Tcaron' => '-20',
      'Tcommaaccent' => '-20',
      'U' => '-20',
      'Uacute' => '-20',
      'Ucircumflex' => '-20',
      'Udieresis' => '-20',
      'Ugrave' => '-20',
      'Uhungarumlaut' => '-20',
      'Umacron' => '-20',
      'Uogonek' => '-20',
      'Uring' => '-20',
      'V' => '-50',
      'W' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'T' => 
    array (
      'A' => '-90',
      'Aacute' => '-90',
      'Abreve' => '-90',
      'Acircumflex' => '-90',
      'Adieresis' => '-90',
      'Agrave' => '-90',
      'Amacron' => '-90',
      'Aogonek' => '-90',
      'Aring' => '-90',
      'Atilde' => '-90',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'a' => '-80',
      'aacute' => '-80',
      'abreve' => '-80',
      'acircumflex' => '-80',
      'adieresis' => '-80',
      'agrave' => '-80',
      'amacron' => '-80',
      'aogonek' => '-80',
      'aring' => '-80',
      'atilde' => '-80',
      'colon' => '-40',
      'comma' => '-80',
      'e' => '-60',
      'eacute' => '-60',
      'ecaron' => '-60',
      'ecircumflex' => '-60',
      'edieresis' => '-60',
      'edotaccent' => '-60',
      'egrave' => '-60',
      'emacron' => '-60',
      'eogonek' => '-60',
      'hyphen' => '-120',
      'o' => '-80',
      'oacute' => '-80',
      'ocircumflex' => '-80',
      'odieresis' => '-80',
      'ograve' => '-80',
      'ohungarumlaut' => '-80',
      'omacron' => '-80',
      'oslash' => '-80',
      'otilde' => '-80',
      'period' => '-80',
      'r' => '-80',
      'racute' => '-80',
      'rcommaaccent' => '-80',
      'semicolon' => '-40',
      'u' => '-90',
      'uacute' => '-90',
      'ucircumflex' => '-90',
      'udieresis' => '-90',
      'ugrave' => '-90',
      'uhungarumlaut' => '-90',
      'umacron' => '-90',
      'uogonek' => '-90',
      'uring' => '-90',
      'w' => '-60',
      'y' => '-60',
      'yacute' => '-60',
      'ydieresis' => '-60',
    ),
    'Tcaron' => 
    array (
      'A' => '-90',
      'Aacute' => '-90',
      'Abreve' => '-90',
      'Acircumflex' => '-90',
      'Adieresis' => '-90',
      'Agrave' => '-90',
      'Amacron' => '-90',
      'Aogonek' => '-90',
      'Aring' => '-90',
      'Atilde' => '-90',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'a' => '-80',
      'aacute' => '-80',
      'abreve' => '-80',
      'acircumflex' => '-80',
      'adieresis' => '-80',
      'agrave' => '-80',
      'amacron' => '-80',
      'aogonek' => '-80',
      'aring' => '-80',
      'atilde' => '-80',
      'colon' => '-40',
      'comma' => '-80',
      'e' => '-60',
      'eacute' => '-60',
      'ecaron' => '-60',
      'ecircumflex' => '-60',
      'edieresis' => '-60',
      'edotaccent' => '-60',
      'egrave' => '-60',
      'emacron' => '-60',
      'eogonek' => '-60',
      'hyphen' => '-120',
      'o' => '-80',
      'oacute' => '-80',
      'ocircumflex' => '-80',
      'odieresis' => '-80',
      'ograve' => '-80',
      'ohungarumlaut' => '-80',
      'omacron' => '-80',
      'oslash' => '-80',
      'otilde' => '-80',
      'period' => '-80',
      'r' => '-80',
      'racute' => '-80',
      'rcommaaccent' => '-80',
      'semicolon' => '-40',
      'u' => '-90',
      'uacute' => '-90',
      'ucircumflex' => '-90',
      'udieresis' => '-90',
      'ugrave' => '-90',
      'uhungarumlaut' => '-90',
      'umacron' => '-90',
      'uogonek' => '-90',
      'uring' => '-90',
      'w' => '-60',
      'y' => '-60',
      'yacute' => '-60',
      'ydieresis' => '-60',
    ),
    'Tcommaaccent' => 
    array (
      'A' => '-90',
      'Aacute' => '-90',
      'Abreve' => '-90',
      'Acircumflex' => '-90',
      'Adieresis' => '-90',
      'Agrave' => '-90',
      'Amacron' => '-90',
      'Aogonek' => '-90',
      'Aring' => '-90',
      'Atilde' => '-90',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'a' => '-80',
      'aacute' => '-80',
      'abreve' => '-80',
      'acircumflex' => '-80',
      'adieresis' => '-80',
      'agrave' => '-80',
      'amacron' => '-80',
      'aogonek' => '-80',
      'aring' => '-80',
      'atilde' => '-80',
      'colon' => '-40',
      'comma' => '-80',
      'e' => '-60',
      'eacute' => '-60',
      'ecaron' => '-60',
      'ecircumflex' => '-60',
      'edieresis' => '-60',
      'edotaccent' => '-60',
      'egrave' => '-60',
      'emacron' => '-60',
      'eogonek' => '-60',
      'hyphen' => '-120',
      'o' => '-80',
      'oacute' => '-80',
      'ocircumflex' => '-80',
      'odieresis' => '-80',
      'ograve' => '-80',
      'ohungarumlaut' => '-80',
      'omacron' => '-80',
      'oslash' => '-80',
      'otilde' => '-80',
      'period' => '-80',
      'r' => '-80',
      'racute' => '-80',
      'rcommaaccent' => '-80',
      'semicolon' => '-40',
      'u' => '-90',
      'uacute' => '-90',
      'ucircumflex' => '-90',
      'udieresis' => '-90',
      'ugrave' => '-90',
      'uhungarumlaut' => '-90',
      'umacron' => '-90',
      'uogonek' => '-90',
      'uring' => '-90',
      'w' => '-60',
      'y' => '-60',
      'yacute' => '-60',
      'ydieresis' => '-60',
    ),
    'U' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'comma' => '-30',
      'period' => '-30',
    ),
    'Uacute' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'comma' => '-30',
      'period' => '-30',
    ),
    'Ucircumflex' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'comma' => '-30',
      'period' => '-30',
    ),
    'Udieresis' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'comma' => '-30',
      'period' => '-30',
    ),
    'Ugrave' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'comma' => '-30',
      'period' => '-30',
    ),
    'Uhungarumlaut' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'comma' => '-30',
      'period' => '-30',
    ),
    'Umacron' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'comma' => '-30',
      'period' => '-30',
    ),
    'Uogonek' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'comma' => '-30',
      'period' => '-30',
    ),
    'Uring' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'comma' => '-30',
      'period' => '-30',
    ),
    'V' => 
    array (
      'A' => '-80',
      'Aacute' => '-80',
      'Abreve' => '-80',
      'Acircumflex' => '-80',
      'Adieresis' => '-80',
      'Agrave' => '-80',
      'Amacron' => '-80',
      'Aogonek' => '-80',
      'Aring' => '-80',
      'Atilde' => '-80',
      'G' => '-50',
      'Gbreve' => '-50',
      'Gcommaaccent' => '-50',
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'a' => '-60',
      'aacute' => '-60',
      'abreve' => '-60',
      'acircumflex' => '-60',
      'adieresis' => '-60',
      'agrave' => '-60',
      'amacron' => '-60',
      'aogonek' => '-60',
      'aring' => '-60',
      'atilde' => '-60',
      'colon' => '-40',
      'comma' => '-120',
      'e' => '-50',
      'eacute' => '-50',
      'ecaron' => '-50',
      'ecircumflex' => '-50',
      'edieresis' => '-50',
      'edotaccent' => '-50',
      'egrave' => '-50',
      'emacron' => '-50',
      'eogonek' => '-50',
      'hyphen' => '-80',
      'o' => '-90',
      'oacute' => '-90',
      'ocircumflex' => '-90',
      'odieresis' => '-90',
      'ograve' => '-90',
      'ohungarumlaut' => '-90',
      'omacron' => '-90',
      'oslash' => '-90',
      'otilde' => '-90',
      'period' => '-120',
      'semicolon' => '-40',
      'u' => '-60',
      'uacute' => '-60',
      'ucircumflex' => '-60',
      'udieresis' => '-60',
      'ugrave' => '-60',
      'uhungarumlaut' => '-60',
      'umacron' => '-60',
      'uogonek' => '-60',
      'uring' => '-60',
    ),
    'W' => 
    array (
      'A' => '-60',
      'Aacute' => '-60',
      'Abreve' => '-60',
      'Acircumflex' => '-60',
      'Adieresis' => '-60',
      'Agrave' => '-60',
      'Amacron' => '-60',
      'Aogonek' => '-60',
      'Aring' => '-60',
      'Atilde' => '-60',
      'O' => '-20',
      'Oacute' => '-20',
      'Ocircumflex' => '-20',
      'Odieresis' => '-20',
      'Ograve' => '-20',
      'Ohungarumlaut' => '-20',
      'Omacron' => '-20',
      'Oslash' => '-20',
      'Otilde' => '-20',
      'a' => '-40',
      'aacute' => '-40',
      'abreve' => '-40',
      'acircumflex' => '-40',
      'adieresis' => '-40',
      'agrave' => '-40',
      'amacron' => '-40',
      'aogonek' => '-40',
      'aring' => '-40',
      'atilde' => '-40',
      'colon' => '-10',
      'comma' => '-80',
      'e' => '-35',
      'eacute' => '-35',
      'ecaron' => '-35',
      'ecircumflex' => '-35',
      'edieresis' => '-35',
      'edotaccent' => '-35',
      'egrave' => '-35',
      'emacron' => '-35',
      'eogonek' => '-35',
      'hyphen' => '-40',
      'o' => '-60',
      'oacute' => '-60',
      'ocircumflex' => '-60',
      'odieresis' => '-60',
      'ograve' => '-60',
      'ohungarumlaut' => '-60',
      'omacron' => '-60',
      'oslash' => '-60',
      'otilde' => '-60',
      'period' => '-80',
      'semicolon' => '-10',
      'u' => '-45',
      'uacute' => '-45',
      'ucircumflex' => '-45',
      'udieresis' => '-45',
      'ugrave' => '-45',
      'uhungarumlaut' => '-45',
      'umacron' => '-45',
      'uogonek' => '-45',
      'uring' => '-45',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'Y' => 
    array (
      'A' => '-110',
      'Aacute' => '-110',
      'Abreve' => '-110',
      'Acircumflex' => '-110',
      'Adieresis' => '-110',
      'Agrave' => '-110',
      'Amacron' => '-110',
      'Aogonek' => '-110',
      'Aring' => '-110',
      'Atilde' => '-110',
      'O' => '-70',
      'Oacute' => '-70',
      'Ocircumflex' => '-70',
      'Odieresis' => '-70',
      'Ograve' => '-70',
      'Ohungarumlaut' => '-70',
      'Omacron' => '-70',
      'Oslash' => '-70',
      'Otilde' => '-70',
      'a' => '-90',
      'aacute' => '-90',
      'abreve' => '-90',
      'acircumflex' => '-90',
      'adieresis' => '-90',
      'agrave' => '-90',
      'amacron' => '-90',
      'aogonek' => '-90',
      'aring' => '-90',
      'atilde' => '-90',
      'colon' => '-50',
      'comma' => '-100',
      'e' => '-80',
      'eacute' => '-80',
      'ecaron' => '-80',
      'ecircumflex' => '-80',
      'edieresis' => '-80',
      'edotaccent' => '-80',
      'egrave' => '-80',
      'emacron' => '-80',
      'eogonek' => '-80',
      'o' => '-100',
      'oacute' => '-100',
      'ocircumflex' => '-100',
      'odieresis' => '-100',
      'ograve' => '-100',
      'ohungarumlaut' => '-100',
      'omacron' => '-100',
      'oslash' => '-100',
      'otilde' => '-100',
      'period' => '-100',
      'semicolon' => '-50',
      'u' => '-100',
      'uacute' => '-100',
      'ucircumflex' => '-100',
      'udieresis' => '-100',
      'ugrave' => '-100',
      'uhungarumlaut' => '-100',
      'umacron' => '-100',
      'uogonek' => '-100',
      'uring' => '-100',
    ),
    'Yacute' => 
    array (
      'A' => '-110',
      'Aacute' => '-110',
      'Abreve' => '-110',
      'Acircumflex' => '-110',
      'Adieresis' => '-110',
      'Agrave' => '-110',
      'Amacron' => '-110',
      'Aogonek' => '-110',
      'Aring' => '-110',
      'Atilde' => '-110',
      'O' => '-70',
      'Oacute' => '-70',
      'Ocircumflex' => '-70',
      'Odieresis' => '-70',
      'Ograve' => '-70',
      'Ohungarumlaut' => '-70',
      'Omacron' => '-70',
      'Oslash' => '-70',
      'Otilde' => '-70',
      'a' => '-90',
      'aacute' => '-90',
      'abreve' => '-90',
      'acircumflex' => '-90',
      'adieresis' => '-90',
      'agrave' => '-90',
      'amacron' => '-90',
      'aogonek' => '-90',
      'aring' => '-90',
      'atilde' => '-90',
      'colon' => '-50',
      'comma' => '-100',
      'e' => '-80',
      'eacute' => '-80',
      'ecaron' => '-80',
      'ecircumflex' => '-80',
      'edieresis' => '-80',
      'edotaccent' => '-80',
      'egrave' => '-80',
      'emacron' => '-80',
      'eogonek' => '-80',
      'o' => '-100',
      'oacute' => '-100',
      'ocircumflex' => '-100',
      'odieresis' => '-100',
      'ograve' => '-100',
      'ohungarumlaut' => '-100',
      'omacron' => '-100',
      'oslash' => '-100',
      'otilde' => '-100',
      'period' => '-100',
      'semicolon' => '-50',
      'u' => '-100',
      'uacute' => '-100',
      'ucircumflex' => '-100',
      'udieresis' => '-100',
      'ugrave' => '-100',
      'uhungarumlaut' => '-100',
      'umacron' => '-100',
      'uogonek' => '-100',
      'uring' => '-100',
    ),
    'Ydieresis' => 
    array (
      'A' => '-110',
      'Aacute' => '-110',
      'Abreve' => '-110',
      'Acircumflex' => '-110',
      'Adieresis' => '-110',
      'Agrave' => '-110',
      'Amacron' => '-110',
      'Aogonek' => '-110',
      'Aring' => '-110',
      'Atilde' => '-110',
      'O' => '-70',
      'Oacute' => '-70',
      'Ocircumflex' => '-70',
      'Odieresis' => '-70',
      'Ograve' => '-70',
      'Ohungarumlaut' => '-70',
      'Omacron' => '-70',
      'Oslash' => '-70',
      'Otilde' => '-70',
      'a' => '-90',
      'aacute' => '-90',
      'abreve' => '-90',
      'acircumflex' => '-90',
      'adieresis' => '-90',
      'agrave' => '-90',
      'amacron' => '-90',
      'aogonek' => '-90',
      'aring' => '-90',
      'atilde' => '-90',
      'colon' => '-50',
      'comma' => '-100',
      'e' => '-80',
      'eacute' => '-80',
      'ecaron' => '-80',
      'ecircumflex' => '-80',
      'edieresis' => '-80',
      'edotaccent' => '-80',
      'egrave' => '-80',
      'emacron' => '-80',
      'eogonek' => '-80',
      'o' => '-100',
      'oacute' => '-100',
      'ocircumflex' => '-100',
      'odieresis' => '-100',
      'ograve' => '-100',
      'ohungarumlaut' => '-100',
      'omacron' => '-100',
      'oslash' => '-100',
      'otilde' => '-100',
      'period' => '-100',
      'semicolon' => '-50',
      'u' => '-100',
      'uacute' => '-100',
      'ucircumflex' => '-100',
      'udieresis' => '-100',
      'ugrave' => '-100',
      'uhungarumlaut' => '-100',
      'umacron' => '-100',
      'uogonek' => '-100',
      'uring' => '-100',
    ),
    'a' => 
    array (
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'aacute' => 
    array (
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'abreve' => 
    array (
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'acircumflex' => 
    array (
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'adieresis' => 
    array (
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'agrave' => 
    array (
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'amacron' => 
    array (
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'aogonek' => 
    array (
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'aring' => 
    array (
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'atilde' => 
    array (
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'b' => 
    array (
      'l' => '-10',
      'lacute' => '-10',
      'lcommaaccent' => '-10',
      'lslash' => '-10',
      'u' => '-20',
      'uacute' => '-20',
      'ucircumflex' => '-20',
      'udieresis' => '-20',
      'ugrave' => '-20',
      'uhungarumlaut' => '-20',
      'umacron' => '-20',
      'uogonek' => '-20',
      'uring' => '-20',
      'v' => '-20',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'c' => 
    array (
      'h' => '-10',
      'k' => '-20',
      'kcommaaccent' => '-20',
      'l' => '-20',
      'lacute' => '-20',
      'lcommaaccent' => '-20',
      'lslash' => '-20',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'cacute' => 
    array (
      'h' => '-10',
      'k' => '-20',
      'kcommaaccent' => '-20',
      'l' => '-20',
      'lacute' => '-20',
      'lcommaaccent' => '-20',
      'lslash' => '-20',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'ccaron' => 
    array (
      'h' => '-10',
      'k' => '-20',
      'kcommaaccent' => '-20',
      'l' => '-20',
      'lacute' => '-20',
      'lcommaaccent' => '-20',
      'lslash' => '-20',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'ccedilla' => 
    array (
      'h' => '-10',
      'k' => '-20',
      'kcommaaccent' => '-20',
      'l' => '-20',
      'lacute' => '-20',
      'lcommaaccent' => '-20',
      'lslash' => '-20',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'colon' => 
    array (
      'space' => '-40',
    ),
    'comma' => 
    array (
      'quotedblright' => '-120',
      'quoteright' => '-120',
      'space' => '-40',
    ),
    'd' => 
    array (
      'd' => '-10',
      'dcroat' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'dcroat' => 
    array (
      'd' => '-10',
      'dcroat' => '-10',
      'v' => '-15',
      'w' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'e' => 
    array (
      'comma' => '10',
      'period' => '20',
      'v' => '-15',
      'w' => '-15',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'eacute' => 
    array (
      'comma' => '10',
      'period' => '20',
      'v' => '-15',
      'w' => '-15',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ecaron' => 
    array (
      'comma' => '10',
      'period' => '20',
      'v' => '-15',
      'w' => '-15',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ecircumflex' => 
    array (
      'comma' => '10',
      'period' => '20',
      'v' => '-15',
      'w' => '-15',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'edieresis' => 
    array (
      'comma' => '10',
      'period' => '20',
      'v' => '-15',
      'w' => '-15',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'edotaccent' => 
    array (
      'comma' => '10',
      'period' => '20',
      'v' => '-15',
      'w' => '-15',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'egrave' => 
    array (
      'comma' => '10',
      'period' => '20',
      'v' => '-15',
      'w' => '-15',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'emacron' => 
    array (
      'comma' => '10',
      'period' => '20',
      'v' => '-15',
      'w' => '-15',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'eogonek' => 
    array (
      'comma' => '10',
      'period' => '20',
      'v' => '-15',
      'w' => '-15',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'f' => 
    array (
      'comma' => '-10',
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
      'period' => '-10',
      'quotedblright' => '30',
      'quoteright' => '30',
    ),
    'g' => 
    array (
      'e' => '10',
      'eacute' => '10',
      'ecaron' => '10',
      'ecircumflex' => '10',
      'edieresis' => '10',
      'edotaccent' => '10',
      'egrave' => '10',
      'emacron' => '10',
      'eogonek' => '10',
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
    ),
    'gbreve' => 
    array (
      'e' => '10',
      'eacute' => '10',
      'ecaron' => '10',
      'ecircumflex' => '10',
      'edieresis' => '10',
      'edotaccent' => '10',
      'egrave' => '10',
      'emacron' => '10',
      'eogonek' => '10',
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
    ),
    'gcommaaccent' => 
    array (
      'e' => '10',
      'eacute' => '10',
      'ecaron' => '10',
      'ecircumflex' => '10',
      'edieresis' => '10',
      'edotaccent' => '10',
      'egrave' => '10',
      'emacron' => '10',
      'eogonek' => '10',
      'g' => '-10',
      'gbreve' => '-10',
      'gcommaaccent' => '-10',
    ),
    'h' => 
    array (
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'k' => 
    array (
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
    ),
    'kcommaaccent' => 
    array (
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
    ),
    'l' => 
    array (
      'w' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'lacute' => 
    array (
      'w' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'lcommaaccent' => 
    array (
      'w' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'lslash' => 
    array (
      'w' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'm' => 
    array (
      'u' => '-20',
      'uacute' => '-20',
      'ucircumflex' => '-20',
      'udieresis' => '-20',
      'ugrave' => '-20',
      'uhungarumlaut' => '-20',
      'umacron' => '-20',
      'uogonek' => '-20',
      'uring' => '-20',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'n' => 
    array (
      'u' => '-10',
      'uacute' => '-10',
      'ucircumflex' => '-10',
      'udieresis' => '-10',
      'ugrave' => '-10',
      'uhungarumlaut' => '-10',
      'umacron' => '-10',
      'uogonek' => '-10',
      'uring' => '-10',
      'v' => '-40',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'nacute' => 
    array (
      'u' => '-10',
      'uacute' => '-10',
      'ucircumflex' => '-10',
      'udieresis' => '-10',
      'ugrave' => '-10',
      'uhungarumlaut' => '-10',
      'umacron' => '-10',
      'uogonek' => '-10',
      'uring' => '-10',
      'v' => '-40',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'ncaron' => 
    array (
      'u' => '-10',
      'uacute' => '-10',
      'ucircumflex' => '-10',
      'udieresis' => '-10',
      'ugrave' => '-10',
      'uhungarumlaut' => '-10',
      'umacron' => '-10',
      'uogonek' => '-10',
      'uring' => '-10',
      'v' => '-40',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'ncommaaccent' => 
    array (
      'u' => '-10',
      'uacute' => '-10',
      'ucircumflex' => '-10',
      'udieresis' => '-10',
      'ugrave' => '-10',
      'uhungarumlaut' => '-10',
      'umacron' => '-10',
      'uogonek' => '-10',
      'uring' => '-10',
      'v' => '-40',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'ntilde' => 
    array (
      'u' => '-10',
      'uacute' => '-10',
      'ucircumflex' => '-10',
      'udieresis' => '-10',
      'ugrave' => '-10',
      'uhungarumlaut' => '-10',
      'umacron' => '-10',
      'uogonek' => '-10',
      'uring' => '-10',
      'v' => '-40',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'o' => 
    array (
      'v' => '-20',
      'w' => '-15',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'oacute' => 
    array (
      'v' => '-20',
      'w' => '-15',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'ocircumflex' => 
    array (
      'v' => '-20',
      'w' => '-15',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'odieresis' => 
    array (
      'v' => '-20',
      'w' => '-15',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'ograve' => 
    array (
      'v' => '-20',
      'w' => '-15',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'ohungarumlaut' => 
    array (
      'v' => '-20',
      'w' => '-15',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'omacron' => 
    array (
      'v' => '-20',
      'w' => '-15',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'oslash' => 
    array (
      'v' => '-20',
      'w' => '-15',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'otilde' => 
    array (
      'v' => '-20',
      'w' => '-15',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'p' => 
    array (
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'period' => 
    array (
      'quotedblright' => '-120',
      'quoteright' => '-120',
      'space' => '-40',
    ),
    'quotedblright' => 
    array (
      'space' => '-80',
    ),
    'quoteleft' => 
    array (
      'quoteleft' => '-46',
    ),
    'quoteright' => 
    array (
      'd' => '-80',
      'dcroat' => '-80',
      'l' => '-20',
      'lacute' => '-20',
      'lcommaaccent' => '-20',
      'lslash' => '-20',
      'quoteright' => '-46',
      'r' => '-40',
      'racute' => '-40',
      'rcaron' => '-40',
      'rcommaaccent' => '-40',
      's' => '-60',
      'sacute' => '-60',
      'scaron' => '-60',
      'scedilla' => '-60',
      'scommaaccent' => '-60',
      'space' => '-80',
      'v' => '-20',
    ),
    'r' => 
    array (
      'c' => '-20',
      'cacute' => '-20',
      'ccaron' => '-20',
      'ccedilla' => '-20',
      'comma' => '-60',
      'd' => '-20',
      'dcroat' => '-20',
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'hyphen' => '-20',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
      'period' => '-60',
      'q' => '-20',
      's' => '-15',
      'sacute' => '-15',
      'scaron' => '-15',
      'scedilla' => '-15',
      'scommaaccent' => '-15',
      't' => '20',
      'tcommaaccent' => '20',
      'v' => '10',
      'y' => '10',
      'yacute' => '10',
      'ydieresis' => '10',
    ),
    'racute' => 
    array (
      'c' => '-20',
      'cacute' => '-20',
      'ccaron' => '-20',
      'ccedilla' => '-20',
      'comma' => '-60',
      'd' => '-20',
      'dcroat' => '-20',
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'hyphen' => '-20',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
      'period' => '-60',
      'q' => '-20',
      's' => '-15',
      'sacute' => '-15',
      'scaron' => '-15',
      'scedilla' => '-15',
      'scommaaccent' => '-15',
      't' => '20',
      'tcommaaccent' => '20',
      'v' => '10',
      'y' => '10',
      'yacute' => '10',
      'ydieresis' => '10',
    ),
    'rcaron' => 
    array (
      'c' => '-20',
      'cacute' => '-20',
      'ccaron' => '-20',
      'ccedilla' => '-20',
      'comma' => '-60',
      'd' => '-20',
      'dcroat' => '-20',
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'hyphen' => '-20',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
      'period' => '-60',
      'q' => '-20',
      's' => '-15',
      'sacute' => '-15',
      'scaron' => '-15',
      'scedilla' => '-15',
      'scommaaccent' => '-15',
      't' => '20',
      'tcommaaccent' => '20',
      'v' => '10',
      'y' => '10',
      'yacute' => '10',
      'ydieresis' => '10',
    ),
    'rcommaaccent' => 
    array (
      'c' => '-20',
      'cacute' => '-20',
      'ccaron' => '-20',
      'ccedilla' => '-20',
      'comma' => '-60',
      'd' => '-20',
      'dcroat' => '-20',
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'hyphen' => '-20',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
      'period' => '-60',
      'q' => '-20',
      's' => '-15',
      'sacute' => '-15',
      'scaron' => '-15',
      'scedilla' => '-15',
      'scommaaccent' => '-15',
      't' => '20',
      'tcommaaccent' => '20',
      'v' => '10',
      'y' => '10',
      'yacute' => '10',
      'ydieresis' => '10',
    ),
    's' => 
    array (
      'w' => '-15',
    ),
    'sacute' => 
    array (
      'w' => '-15',
    ),
    'scaron' => 
    array (
      'w' => '-15',
    ),
    'scedilla' => 
    array (
      'w' => '-15',
    ),
    'scommaaccent' => 
    array (
      'w' => '-15',
    ),
    'semicolon' => 
    array (
      'space' => '-40',
    ),
    'space' => 
    array (
      'T' => '-100',
      'Tcaron' => '-100',
      'Tcommaaccent' => '-100',
      'V' => '-80',
      'W' => '-80',
      'Y' => '-120',
      'Yacute' => '-120',
      'Ydieresis' => '-120',
      'quotedblleft' => '-80',
      'quoteleft' => '-60',
    ),
    'v' => 
    array (
      'a' => '-20',
      'aacute' => '-20',
      'abreve' => '-20',
      'acircumflex' => '-20',
      'adieresis' => '-20',
      'agrave' => '-20',
      'amacron' => '-20',
      'aogonek' => '-20',
      'aring' => '-20',
      'atilde' => '-20',
      'comma' => '-80',
      'o' => '-30',
      'oacute' => '-30',
      'ocircumflex' => '-30',
      'odieresis' => '-30',
      'ograve' => '-30',
      'ohungarumlaut' => '-30',
      'omacron' => '-30',
      'oslash' => '-30',
      'otilde' => '-30',
      'period' => '-80',
    ),
    'w' => 
    array (
      'comma' => '-40',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
      'period' => '-40',
    ),
    'x' => 
    array (
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
    ),
    'y' => 
    array (
      'a' => '-30',
      'aacute' => '-30',
      'abreve' => '-30',
      'acircumflex' => '-30',
      'adieresis' => '-30',
      'agrave' => '-30',
      'amacron' => '-30',
      'aogonek' => '-30',
      'aring' => '-30',
      'atilde' => '-30',
      'comma' => '-80',
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'period' => '-80',
    ),
    'yacute' => 
    array (
      'a' => '-30',
      'aacute' => '-30',
      'abreve' => '-30',
      'acircumflex' => '-30',
      'adieresis' => '-30',
      'agrave' => '-30',
      'amacron' => '-30',
      'aogonek' => '-30',
      'aring' => '-30',
      'atilde' => '-30',
      'comma' => '-80',
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'period' => '-80',
    ),
    'ydieresis' => 
    array (
      'a' => '-30',
      'aacute' => '-30',
      'abreve' => '-30',
      'acircumflex' => '-30',
      'adieresis' => '-30',
      'agrave' => '-30',
      'amacron' => '-30',
      'aogonek' => '-30',
      'aring' => '-30',
      'atilde' => '-30',
      'comma' => '-80',
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'period' => '-80',
    ),
    'z' => 
    array (
      'e' => '10',
      'eacute' => '10',
      'ecaron' => '10',
      'ecircumflex' => '10',
      'edieresis' => '10',
      'edotaccent' => '10',
      'egrave' => '10',
      'emacron' => '10',
      'eogonek' => '10',
    ),
    'zacute' => 
    array (
      'e' => '10',
      'eacute' => '10',
      'ecaron' => '10',
      'ecircumflex' => '10',
      'edieresis' => '10',
      'edotaccent' => '10',
      'egrave' => '10',
      'emacron' => '10',
      'eogonek' => '10',
    ),
    'zcaron' => 
    array (
      'e' => '10',
      'eacute' => '10',
      'ecaron' => '10',
      'ecircumflex' => '10',
      'edieresis' => '10',
      'edotaccent' => '10',
      'egrave' => '10',
      'emacron' => '10',
      'eogonek' => '10',
    ),
    'zdotaccent' => 
    array (
      'e' => '10',
      'eacute' => '10',
      'ecaron' => '10',
      'ecircumflex' => '10',
      'edieresis' => '10',
      'edotaccent' => '10',
      'egrave' => '10',
      'emacron' => '10',
      'eogonek' => '10',
    ),
  ),
  '_version_' => 1,
);