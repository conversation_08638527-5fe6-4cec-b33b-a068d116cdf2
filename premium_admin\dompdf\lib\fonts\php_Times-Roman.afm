$this->fonts[$font]=array (
  'FontName' => 'Times-Roman',
  'FullName' => 'Times Roman',
  'FamilyName' => 'Times',
  'Weight' => 'Roman',
  'ItalicAngle' => '0',
  'IsFixedPitch' => 'false',
  'CharacterSet' => 'ExtendedRoman',
  'FontBBox' => 
  array (
    0 => '-168',
    1 => '-218',
    2 => '1000',
    3 => '898',
  ),
  'UnderlinePosition' => '-100',
  'UnderlineThickness' => '50',
  'Version' => '002.000',
  'EncodingScheme' => 'AdobeStandardEncoding',
  'CapHeight' => '662',
  'XHeight' => '450',
  'Ascender' => '683',
  'Descender' => '-217',
  'StdHW' => '28',
  'StdVW' => '84',
  'StartCharMetrics' => '315',
  'C' => 
  array (
    32 => 
    array (
      'C' => '32',
      'WX' => '250',
      'N' => 'space',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
    'space' => 
    array (
      'C' => '32',
      'WX' => '250',
      'N' => 'space',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
    33 => 
    array (
      'C' => '33',
      'WX' => '333',
      'N' => 'exclam',
      'B' => 
      array (
        0 => '130',
        1 => '-9',
        2 => '238',
        3 => '676',
      ),
    ),
    'exclam' => 
    array (
      'C' => '33',
      'WX' => '333',
      'N' => 'exclam',
      'B' => 
      array (
        0 => '130',
        1 => '-9',
        2 => '238',
        3 => '676',
      ),
    ),
    34 => 
    array (
      'C' => '34',
      'WX' => '408',
      'N' => 'quotedbl',
      'B' => 
      array (
        0 => '77',
        1 => '431',
        2 => '331',
        3 => '676',
      ),
    ),
    'quotedbl' => 
    array (
      'C' => '34',
      'WX' => '408',
      'N' => 'quotedbl',
      'B' => 
      array (
        0 => '77',
        1 => '431',
        2 => '331',
        3 => '676',
      ),
    ),
    35 => 
    array (
      'C' => '35',
      'WX' => '500',
      'N' => 'numbersign',
      'B' => 
      array (
        0 => '5',
        1 => '0',
        2 => '496',
        3 => '662',
      ),
    ),
    'numbersign' => 
    array (
      'C' => '35',
      'WX' => '500',
      'N' => 'numbersign',
      'B' => 
      array (
        0 => '5',
        1 => '0',
        2 => '496',
        3 => '662',
      ),
    ),
    36 => 
    array (
      'C' => '36',
      'WX' => '500',
      'N' => 'dollar',
      'B' => 
      array (
        0 => '44',
        1 => '-87',
        2 => '457',
        3 => '727',
      ),
    ),
    'dollar' => 
    array (
      'C' => '36',
      'WX' => '500',
      'N' => 'dollar',
      'B' => 
      array (
        0 => '44',
        1 => '-87',
        2 => '457',
        3 => '727',
      ),
    ),
    37 => 
    array (
      'C' => '37',
      'WX' => '833',
      'N' => 'percent',
      'B' => 
      array (
        0 => '61',
        1 => '-13',
        2 => '772',
        3 => '676',
      ),
    ),
    'percent' => 
    array (
      'C' => '37',
      'WX' => '833',
      'N' => 'percent',
      'B' => 
      array (
        0 => '61',
        1 => '-13',
        2 => '772',
        3 => '676',
      ),
    ),
    38 => 
    array (
      'C' => '38',
      'WX' => '778',
      'N' => 'ampersand',
      'B' => 
      array (
        0 => '42',
        1 => '-13',
        2 => '750',
        3 => '676',
      ),
    ),
    'ampersand' => 
    array (
      'C' => '38',
      'WX' => '778',
      'N' => 'ampersand',
      'B' => 
      array (
        0 => '42',
        1 => '-13',
        2 => '750',
        3 => '676',
      ),
    ),
    39 => 
    array (
      'C' => '39',
      'WX' => '333',
      'N' => 'quoteright',
      'B' => 
      array (
        0 => '79',
        1 => '433',
        2 => '218',
        3 => '676',
      ),
    ),
    'quoteright' => 
    array (
      'C' => '39',
      'WX' => '333',
      'N' => 'quoteright',
      'B' => 
      array (
        0 => '79',
        1 => '433',
        2 => '218',
        3 => '676',
      ),
    ),
    40 => 
    array (
      'C' => '40',
      'WX' => '333',
      'N' => 'parenleft',
      'B' => 
      array (
        0 => '48',
        1 => '-177',
        2 => '304',
        3 => '676',
      ),
    ),
    'parenleft' => 
    array (
      'C' => '40',
      'WX' => '333',
      'N' => 'parenleft',
      'B' => 
      array (
        0 => '48',
        1 => '-177',
        2 => '304',
        3 => '676',
      ),
    ),
    41 => 
    array (
      'C' => '41',
      'WX' => '333',
      'N' => 'parenright',
      'B' => 
      array (
        0 => '29',
        1 => '-177',
        2 => '285',
        3 => '676',
      ),
    ),
    'parenright' => 
    array (
      'C' => '41',
      'WX' => '333',
      'N' => 'parenright',
      'B' => 
      array (
        0 => '29',
        1 => '-177',
        2 => '285',
        3 => '676',
      ),
    ),
    42 => 
    array (
      'C' => '42',
      'WX' => '500',
      'N' => 'asterisk',
      'B' => 
      array (
        0 => '69',
        1 => '265',
        2 => '432',
        3 => '676',
      ),
    ),
    'asterisk' => 
    array (
      'C' => '42',
      'WX' => '500',
      'N' => 'asterisk',
      'B' => 
      array (
        0 => '69',
        1 => '265',
        2 => '432',
        3 => '676',
      ),
    ),
    43 => 
    array (
      'C' => '43',
      'WX' => '564',
      'N' => 'plus',
      'B' => 
      array (
        0 => '30',
        1 => '0',
        2 => '534',
        3 => '506',
      ),
    ),
    'plus' => 
    array (
      'C' => '43',
      'WX' => '564',
      'N' => 'plus',
      'B' => 
      array (
        0 => '30',
        1 => '0',
        2 => '534',
        3 => '506',
      ),
    ),
    44 => 
    array (
      'C' => '44',
      'WX' => '250',
      'N' => 'comma',
      'B' => 
      array (
        0 => '56',
        1 => '-141',
        2 => '195',
        3 => '102',
      ),
    ),
    'comma' => 
    array (
      'C' => '44',
      'WX' => '250',
      'N' => 'comma',
      'B' => 
      array (
        0 => '56',
        1 => '-141',
        2 => '195',
        3 => '102',
      ),
    ),
    45 => 
    array (
      'C' => '45',
      'WX' => '333',
      'N' => 'hyphen',
      'B' => 
      array (
        0 => '39',
        1 => '194',
        2 => '285',
        3 => '257',
      ),
    ),
    'hyphen' => 
    array (
      'C' => '45',
      'WX' => '333',
      'N' => 'hyphen',
      'B' => 
      array (
        0 => '39',
        1 => '194',
        2 => '285',
        3 => '257',
      ),
    ),
    46 => 
    array (
      'C' => '46',
      'WX' => '250',
      'N' => 'period',
      'B' => 
      array (
        0 => '70',
        1 => '-11',
        2 => '181',
        3 => '100',
      ),
    ),
    'period' => 
    array (
      'C' => '46',
      'WX' => '250',
      'N' => 'period',
      'B' => 
      array (
        0 => '70',
        1 => '-11',
        2 => '181',
        3 => '100',
      ),
    ),
    47 => 
    array (
      'C' => '47',
      'WX' => '278',
      'N' => 'slash',
      'B' => 
      array (
        0 => '-9',
        1 => '-14',
        2 => '287',
        3 => '676',
      ),
    ),
    'slash' => 
    array (
      'C' => '47',
      'WX' => '278',
      'N' => 'slash',
      'B' => 
      array (
        0 => '-9',
        1 => '-14',
        2 => '287',
        3 => '676',
      ),
    ),
    48 => 
    array (
      'C' => '48',
      'WX' => '500',
      'N' => 'zero',
      'B' => 
      array (
        0 => '24',
        1 => '-14',
        2 => '476',
        3 => '676',
      ),
    ),
    'zero' => 
    array (
      'C' => '48',
      'WX' => '500',
      'N' => 'zero',
      'B' => 
      array (
        0 => '24',
        1 => '-14',
        2 => '476',
        3 => '676',
      ),
    ),
    49 => 
    array (
      'C' => '49',
      'WX' => '500',
      'N' => 'one',
      'B' => 
      array (
        0 => '111',
        1 => '0',
        2 => '394',
        3 => '676',
      ),
    ),
    'one' => 
    array (
      'C' => '49',
      'WX' => '500',
      'N' => 'one',
      'B' => 
      array (
        0 => '111',
        1 => '0',
        2 => '394',
        3 => '676',
      ),
    ),
    50 => 
    array (
      'C' => '50',
      'WX' => '500',
      'N' => 'two',
      'B' => 
      array (
        0 => '30',
        1 => '0',
        2 => '475',
        3 => '676',
      ),
    ),
    'two' => 
    array (
      'C' => '50',
      'WX' => '500',
      'N' => 'two',
      'B' => 
      array (
        0 => '30',
        1 => '0',
        2 => '475',
        3 => '676',
      ),
    ),
    51 => 
    array (
      'C' => '51',
      'WX' => '500',
      'N' => 'three',
      'B' => 
      array (
        0 => '43',
        1 => '-14',
        2 => '431',
        3 => '676',
      ),
    ),
    'three' => 
    array (
      'C' => '51',
      'WX' => '500',
      'N' => 'three',
      'B' => 
      array (
        0 => '43',
        1 => '-14',
        2 => '431',
        3 => '676',
      ),
    ),
    52 => 
    array (
      'C' => '52',
      'WX' => '500',
      'N' => 'four',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '472',
        3 => '676',
      ),
    ),
    'four' => 
    array (
      'C' => '52',
      'WX' => '500',
      'N' => 'four',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '472',
        3 => '676',
      ),
    ),
    53 => 
    array (
      'C' => '53',
      'WX' => '500',
      'N' => 'five',
      'B' => 
      array (
        0 => '32',
        1 => '-14',
        2 => '438',
        3 => '688',
      ),
    ),
    'five' => 
    array (
      'C' => '53',
      'WX' => '500',
      'N' => 'five',
      'B' => 
      array (
        0 => '32',
        1 => '-14',
        2 => '438',
        3 => '688',
      ),
    ),
    54 => 
    array (
      'C' => '54',
      'WX' => '500',
      'N' => 'six',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '468',
        3 => '684',
      ),
    ),
    'six' => 
    array (
      'C' => '54',
      'WX' => '500',
      'N' => 'six',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '468',
        3 => '684',
      ),
    ),
    55 => 
    array (
      'C' => '55',
      'WX' => '500',
      'N' => 'seven',
      'B' => 
      array (
        0 => '20',
        1 => '-8',
        2 => '449',
        3 => '662',
      ),
    ),
    'seven' => 
    array (
      'C' => '55',
      'WX' => '500',
      'N' => 'seven',
      'B' => 
      array (
        0 => '20',
        1 => '-8',
        2 => '449',
        3 => '662',
      ),
    ),
    56 => 
    array (
      'C' => '56',
      'WX' => '500',
      'N' => 'eight',
      'B' => 
      array (
        0 => '56',
        1 => '-14',
        2 => '445',
        3 => '676',
      ),
    ),
    'eight' => 
    array (
      'C' => '56',
      'WX' => '500',
      'N' => 'eight',
      'B' => 
      array (
        0 => '56',
        1 => '-14',
        2 => '445',
        3 => '676',
      ),
    ),
    57 => 
    array (
      'C' => '57',
      'WX' => '500',
      'N' => 'nine',
      'B' => 
      array (
        0 => '30',
        1 => '-22',
        2 => '459',
        3 => '676',
      ),
    ),
    'nine' => 
    array (
      'C' => '57',
      'WX' => '500',
      'N' => 'nine',
      'B' => 
      array (
        0 => '30',
        1 => '-22',
        2 => '459',
        3 => '676',
      ),
    ),
    58 => 
    array (
      'C' => '58',
      'WX' => '278',
      'N' => 'colon',
      'B' => 
      array (
        0 => '81',
        1 => '-11',
        2 => '192',
        3 => '459',
      ),
    ),
    'colon' => 
    array (
      'C' => '58',
      'WX' => '278',
      'N' => 'colon',
      'B' => 
      array (
        0 => '81',
        1 => '-11',
        2 => '192',
        3 => '459',
      ),
    ),
    59 => 
    array (
      'C' => '59',
      'WX' => '278',
      'N' => 'semicolon',
      'B' => 
      array (
        0 => '80',
        1 => '-141',
        2 => '219',
        3 => '459',
      ),
    ),
    'semicolon' => 
    array (
      'C' => '59',
      'WX' => '278',
      'N' => 'semicolon',
      'B' => 
      array (
        0 => '80',
        1 => '-141',
        2 => '219',
        3 => '459',
      ),
    ),
    60 => 
    array (
      'C' => '60',
      'WX' => '564',
      'N' => 'less',
      'B' => 
      array (
        0 => '28',
        1 => '-8',
        2 => '536',
        3 => '514',
      ),
    ),
    'less' => 
    array (
      'C' => '60',
      'WX' => '564',
      'N' => 'less',
      'B' => 
      array (
        0 => '28',
        1 => '-8',
        2 => '536',
        3 => '514',
      ),
    ),
    61 => 
    array (
      'C' => '61',
      'WX' => '564',
      'N' => 'equal',
      'B' => 
      array (
        0 => '30',
        1 => '120',
        2 => '534',
        3 => '386',
      ),
    ),
    'equal' => 
    array (
      'C' => '61',
      'WX' => '564',
      'N' => 'equal',
      'B' => 
      array (
        0 => '30',
        1 => '120',
        2 => '534',
        3 => '386',
      ),
    ),
    62 => 
    array (
      'C' => '62',
      'WX' => '564',
      'N' => 'greater',
      'B' => 
      array (
        0 => '28',
        1 => '-8',
        2 => '536',
        3 => '514',
      ),
    ),
    'greater' => 
    array (
      'C' => '62',
      'WX' => '564',
      'N' => 'greater',
      'B' => 
      array (
        0 => '28',
        1 => '-8',
        2 => '536',
        3 => '514',
      ),
    ),
    63 => 
    array (
      'C' => '63',
      'WX' => '444',
      'N' => 'question',
      'B' => 
      array (
        0 => '68',
        1 => '-8',
        2 => '414',
        3 => '676',
      ),
    ),
    'question' => 
    array (
      'C' => '63',
      'WX' => '444',
      'N' => 'question',
      'B' => 
      array (
        0 => '68',
        1 => '-8',
        2 => '414',
        3 => '676',
      ),
    ),
    64 => 
    array (
      'C' => '64',
      'WX' => '921',
      'N' => 'at',
      'B' => 
      array (
        0 => '116',
        1 => '-14',
        2 => '809',
        3 => '676',
      ),
    ),
    'at' => 
    array (
      'C' => '64',
      'WX' => '921',
      'N' => 'at',
      'B' => 
      array (
        0 => '116',
        1 => '-14',
        2 => '809',
        3 => '676',
      ),
    ),
    65 => 
    array (
      'C' => '65',
      'WX' => '722',
      'N' => 'A',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '706',
        3 => '674',
      ),
    ),
    'A' => 
    array (
      'C' => '65',
      'WX' => '722',
      'N' => 'A',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '706',
        3 => '674',
      ),
    ),
    66 => 
    array (
      'C' => '66',
      'WX' => '667',
      'N' => 'B',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '593',
        3 => '662',
      ),
    ),
    'B' => 
    array (
      'C' => '66',
      'WX' => '667',
      'N' => 'B',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '593',
        3 => '662',
      ),
    ),
    67 => 
    array (
      'C' => '67',
      'WX' => '667',
      'N' => 'C',
      'B' => 
      array (
        0 => '28',
        1 => '-14',
        2 => '633',
        3 => '676',
      ),
    ),
    'C' => 
    array (
      'C' => '67',
      'WX' => '667',
      'N' => 'C',
      'B' => 
      array (
        0 => '28',
        1 => '-14',
        2 => '633',
        3 => '676',
      ),
    ),
    68 => 
    array (
      'C' => '68',
      'WX' => '722',
      'N' => 'D',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '685',
        3 => '662',
      ),
    ),
    'D' => 
    array (
      'C' => '68',
      'WX' => '722',
      'N' => 'D',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '685',
        3 => '662',
      ),
    ),
    69 => 
    array (
      'C' => '69',
      'WX' => '611',
      'N' => 'E',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '597',
        3 => '662',
      ),
    ),
    'E' => 
    array (
      'C' => '69',
      'WX' => '611',
      'N' => 'E',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '597',
        3 => '662',
      ),
    ),
    70 => 
    array (
      'C' => '70',
      'WX' => '556',
      'N' => 'F',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '546',
        3 => '662',
      ),
    ),
    'F' => 
    array (
      'C' => '70',
      'WX' => '556',
      'N' => 'F',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '546',
        3 => '662',
      ),
    ),
    71 => 
    array (
      'C' => '71',
      'WX' => '722',
      'N' => 'G',
      'B' => 
      array (
        0 => '32',
        1 => '-14',
        2 => '709',
        3 => '676',
      ),
    ),
    'G' => 
    array (
      'C' => '71',
      'WX' => '722',
      'N' => 'G',
      'B' => 
      array (
        0 => '32',
        1 => '-14',
        2 => '709',
        3 => '676',
      ),
    ),
    72 => 
    array (
      'C' => '72',
      'WX' => '722',
      'N' => 'H',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '702',
        3 => '662',
      ),
    ),
    'H' => 
    array (
      'C' => '72',
      'WX' => '722',
      'N' => 'H',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '702',
        3 => '662',
      ),
    ),
    73 => 
    array (
      'C' => '73',
      'WX' => '333',
      'N' => 'I',
      'B' => 
      array (
        0 => '18',
        1 => '0',
        2 => '315',
        3 => '662',
      ),
    ),
    'I' => 
    array (
      'C' => '73',
      'WX' => '333',
      'N' => 'I',
      'B' => 
      array (
        0 => '18',
        1 => '0',
        2 => '315',
        3 => '662',
      ),
    ),
    74 => 
    array (
      'C' => '74',
      'WX' => '389',
      'N' => 'J',
      'B' => 
      array (
        0 => '10',
        1 => '-14',
        2 => '370',
        3 => '662',
      ),
    ),
    'J' => 
    array (
      'C' => '74',
      'WX' => '389',
      'N' => 'J',
      'B' => 
      array (
        0 => '10',
        1 => '-14',
        2 => '370',
        3 => '662',
      ),
    ),
    75 => 
    array (
      'C' => '75',
      'WX' => '722',
      'N' => 'K',
      'B' => 
      array (
        0 => '34',
        1 => '0',
        2 => '723',
        3 => '662',
      ),
    ),
    'K' => 
    array (
      'C' => '75',
      'WX' => '722',
      'N' => 'K',
      'B' => 
      array (
        0 => '34',
        1 => '0',
        2 => '723',
        3 => '662',
      ),
    ),
    76 => 
    array (
      'C' => '76',
      'WX' => '611',
      'N' => 'L',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '598',
        3 => '662',
      ),
    ),
    'L' => 
    array (
      'C' => '76',
      'WX' => '611',
      'N' => 'L',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '598',
        3 => '662',
      ),
    ),
    77 => 
    array (
      'C' => '77',
      'WX' => '889',
      'N' => 'M',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '863',
        3 => '662',
      ),
    ),
    'M' => 
    array (
      'C' => '77',
      'WX' => '889',
      'N' => 'M',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '863',
        3 => '662',
      ),
    ),
    78 => 
    array (
      'C' => '78',
      'WX' => '722',
      'N' => 'N',
      'B' => 
      array (
        0 => '12',
        1 => '-11',
        2 => '707',
        3 => '662',
      ),
    ),
    'N' => 
    array (
      'C' => '78',
      'WX' => '722',
      'N' => 'N',
      'B' => 
      array (
        0 => '12',
        1 => '-11',
        2 => '707',
        3 => '662',
      ),
    ),
    79 => 
    array (
      'C' => '79',
      'WX' => '722',
      'N' => 'O',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '688',
        3 => '676',
      ),
    ),
    'O' => 
    array (
      'C' => '79',
      'WX' => '722',
      'N' => 'O',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '688',
        3 => '676',
      ),
    ),
    80 => 
    array (
      'C' => '80',
      'WX' => '556',
      'N' => 'P',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '542',
        3 => '662',
      ),
    ),
    'P' => 
    array (
      'C' => '80',
      'WX' => '556',
      'N' => 'P',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '542',
        3 => '662',
      ),
    ),
    81 => 
    array (
      'C' => '81',
      'WX' => '722',
      'N' => 'Q',
      'B' => 
      array (
        0 => '34',
        1 => '-178',
        2 => '701',
        3 => '676',
      ),
    ),
    'Q' => 
    array (
      'C' => '81',
      'WX' => '722',
      'N' => 'Q',
      'B' => 
      array (
        0 => '34',
        1 => '-178',
        2 => '701',
        3 => '676',
      ),
    ),
    82 => 
    array (
      'C' => '82',
      'WX' => '667',
      'N' => 'R',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '659',
        3 => '662',
      ),
    ),
    'R' => 
    array (
      'C' => '82',
      'WX' => '667',
      'N' => 'R',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '659',
        3 => '662',
      ),
    ),
    83 => 
    array (
      'C' => '83',
      'WX' => '556',
      'N' => 'S',
      'B' => 
      array (
        0 => '42',
        1 => '-14',
        2 => '491',
        3 => '676',
      ),
    ),
    'S' => 
    array (
      'C' => '83',
      'WX' => '556',
      'N' => 'S',
      'B' => 
      array (
        0 => '42',
        1 => '-14',
        2 => '491',
        3 => '676',
      ),
    ),
    84 => 
    array (
      'C' => '84',
      'WX' => '611',
      'N' => 'T',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '593',
        3 => '662',
      ),
    ),
    'T' => 
    array (
      'C' => '84',
      'WX' => '611',
      'N' => 'T',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '593',
        3 => '662',
      ),
    ),
    85 => 
    array (
      'C' => '85',
      'WX' => '722',
      'N' => 'U',
      'B' => 
      array (
        0 => '14',
        1 => '-14',
        2 => '705',
        3 => '662',
      ),
    ),
    'U' => 
    array (
      'C' => '85',
      'WX' => '722',
      'N' => 'U',
      'B' => 
      array (
        0 => '14',
        1 => '-14',
        2 => '705',
        3 => '662',
      ),
    ),
    86 => 
    array (
      'C' => '86',
      'WX' => '722',
      'N' => 'V',
      'B' => 
      array (
        0 => '16',
        1 => '-11',
        2 => '697',
        3 => '662',
      ),
    ),
    'V' => 
    array (
      'C' => '86',
      'WX' => '722',
      'N' => 'V',
      'B' => 
      array (
        0 => '16',
        1 => '-11',
        2 => '697',
        3 => '662',
      ),
    ),
    87 => 
    array (
      'C' => '87',
      'WX' => '944',
      'N' => 'W',
      'B' => 
      array (
        0 => '5',
        1 => '-11',
        2 => '932',
        3 => '662',
      ),
    ),
    'W' => 
    array (
      'C' => '87',
      'WX' => '944',
      'N' => 'W',
      'B' => 
      array (
        0 => '5',
        1 => '-11',
        2 => '932',
        3 => '662',
      ),
    ),
    88 => 
    array (
      'C' => '88',
      'WX' => '722',
      'N' => 'X',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '704',
        3 => '662',
      ),
    ),
    'X' => 
    array (
      'C' => '88',
      'WX' => '722',
      'N' => 'X',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '704',
        3 => '662',
      ),
    ),
    89 => 
    array (
      'C' => '89',
      'WX' => '722',
      'N' => 'Y',
      'B' => 
      array (
        0 => '22',
        1 => '0',
        2 => '703',
        3 => '662',
      ),
    ),
    'Y' => 
    array (
      'C' => '89',
      'WX' => '722',
      'N' => 'Y',
      'B' => 
      array (
        0 => '22',
        1 => '0',
        2 => '703',
        3 => '662',
      ),
    ),
    90 => 
    array (
      'C' => '90',
      'WX' => '611',
      'N' => 'Z',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '597',
        3 => '662',
      ),
    ),
    'Z' => 
    array (
      'C' => '90',
      'WX' => '611',
      'N' => 'Z',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '597',
        3 => '662',
      ),
    ),
    91 => 
    array (
      'C' => '91',
      'WX' => '333',
      'N' => 'bracketleft',
      'B' => 
      array (
        0 => '88',
        1 => '-156',
        2 => '299',
        3 => '662',
      ),
    ),
    'bracketleft' => 
    array (
      'C' => '91',
      'WX' => '333',
      'N' => 'bracketleft',
      'B' => 
      array (
        0 => '88',
        1 => '-156',
        2 => '299',
        3 => '662',
      ),
    ),
    92 => 
    array (
      'C' => '92',
      'WX' => '278',
      'N' => 'backslash',
      'B' => 
      array (
        0 => '-9',
        1 => '-14',
        2 => '287',
        3 => '676',
      ),
    ),
    'backslash' => 
    array (
      'C' => '92',
      'WX' => '278',
      'N' => 'backslash',
      'B' => 
      array (
        0 => '-9',
        1 => '-14',
        2 => '287',
        3 => '676',
      ),
    ),
    93 => 
    array (
      'C' => '93',
      'WX' => '333',
      'N' => 'bracketright',
      'B' => 
      array (
        0 => '34',
        1 => '-156',
        2 => '245',
        3 => '662',
      ),
    ),
    'bracketright' => 
    array (
      'C' => '93',
      'WX' => '333',
      'N' => 'bracketright',
      'B' => 
      array (
        0 => '34',
        1 => '-156',
        2 => '245',
        3 => '662',
      ),
    ),
    94 => 
    array (
      'C' => '94',
      'WX' => '469',
      'N' => 'asciicircum',
      'B' => 
      array (
        0 => '24',
        1 => '297',
        2 => '446',
        3 => '662',
      ),
    ),
    'asciicircum' => 
    array (
      'C' => '94',
      'WX' => '469',
      'N' => 'asciicircum',
      'B' => 
      array (
        0 => '24',
        1 => '297',
        2 => '446',
        3 => '662',
      ),
    ),
    95 => 
    array (
      'C' => '95',
      'WX' => '500',
      'N' => 'underscore',
      'B' => 
      array (
        0 => '0',
        1 => '-125',
        2 => '500',
        3 => '-75',
      ),
    ),
    'underscore' => 
    array (
      'C' => '95',
      'WX' => '500',
      'N' => 'underscore',
      'B' => 
      array (
        0 => '0',
        1 => '-125',
        2 => '500',
        3 => '-75',
      ),
    ),
    96 => 
    array (
      'C' => '96',
      'WX' => '333',
      'N' => 'quoteleft',
      'B' => 
      array (
        0 => '115',
        1 => '433',
        2 => '254',
        3 => '676',
      ),
    ),
    'quoteleft' => 
    array (
      'C' => '96',
      'WX' => '333',
      'N' => 'quoteleft',
      'B' => 
      array (
        0 => '115',
        1 => '433',
        2 => '254',
        3 => '676',
      ),
    ),
    97 => 
    array (
      'C' => '97',
      'WX' => '444',
      'N' => 'a',
      'B' => 
      array (
        0 => '37',
        1 => '-10',
        2 => '442',
        3 => '460',
      ),
    ),
    'a' => 
    array (
      'C' => '97',
      'WX' => '444',
      'N' => 'a',
      'B' => 
      array (
        0 => '37',
        1 => '-10',
        2 => '442',
        3 => '460',
      ),
    ),
    98 => 
    array (
      'C' => '98',
      'WX' => '500',
      'N' => 'b',
      'B' => 
      array (
        0 => '3',
        1 => '-10',
        2 => '468',
        3 => '683',
      ),
    ),
    'b' => 
    array (
      'C' => '98',
      'WX' => '500',
      'N' => 'b',
      'B' => 
      array (
        0 => '3',
        1 => '-10',
        2 => '468',
        3 => '683',
      ),
    ),
    99 => 
    array (
      'C' => '99',
      'WX' => '444',
      'N' => 'c',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '412',
        3 => '460',
      ),
    ),
    'c' => 
    array (
      'C' => '99',
      'WX' => '444',
      'N' => 'c',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '412',
        3 => '460',
      ),
    ),
    100 => 
    array (
      'C' => '100',
      'WX' => '500',
      'N' => 'd',
      'B' => 
      array (
        0 => '27',
        1 => '-10',
        2 => '491',
        3 => '683',
      ),
    ),
    'd' => 
    array (
      'C' => '100',
      'WX' => '500',
      'N' => 'd',
      'B' => 
      array (
        0 => '27',
        1 => '-10',
        2 => '491',
        3 => '683',
      ),
    ),
    101 => 
    array (
      'C' => '101',
      'WX' => '444',
      'N' => 'e',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '424',
        3 => '460',
      ),
    ),
    'e' => 
    array (
      'C' => '101',
      'WX' => '444',
      'N' => 'e',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '424',
        3 => '460',
      ),
    ),
    102 => 
    array (
      'C' => '102',
      'WX' => '333',
      'N' => 'f',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '383',
        3 => '683',
      ),
      'L' => 
      array (
        0 => 'l',
        1 => 'fl',
      ),
    ),
    'f' => 
    array (
      'C' => '102',
      'WX' => '333',
      'N' => 'f',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '383',
        3 => '683',
      ),
      'L' => 
      array (
        0 => 'l',
        1 => 'fl',
      ),
    ),
    103 => 
    array (
      'C' => '103',
      'WX' => '500',
      'N' => 'g',
      'B' => 
      array (
        0 => '28',
        1 => '-218',
        2 => '470',
        3 => '460',
      ),
    ),
    'g' => 
    array (
      'C' => '103',
      'WX' => '500',
      'N' => 'g',
      'B' => 
      array (
        0 => '28',
        1 => '-218',
        2 => '470',
        3 => '460',
      ),
    ),
    104 => 
    array (
      'C' => '104',
      'WX' => '500',
      'N' => 'h',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '487',
        3 => '683',
      ),
    ),
    'h' => 
    array (
      'C' => '104',
      'WX' => '500',
      'N' => 'h',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '487',
        3 => '683',
      ),
    ),
    105 => 
    array (
      'C' => '105',
      'WX' => '278',
      'N' => 'i',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '253',
        3 => '683',
      ),
    ),
    'i' => 
    array (
      'C' => '105',
      'WX' => '278',
      'N' => 'i',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '253',
        3 => '683',
      ),
    ),
    106 => 
    array (
      'C' => '106',
      'WX' => '278',
      'N' => 'j',
      'B' => 
      array (
        0 => '-70',
        1 => '-218',
        2 => '194',
        3 => '683',
      ),
    ),
    'j' => 
    array (
      'C' => '106',
      'WX' => '278',
      'N' => 'j',
      'B' => 
      array (
        0 => '-70',
        1 => '-218',
        2 => '194',
        3 => '683',
      ),
    ),
    107 => 
    array (
      'C' => '107',
      'WX' => '500',
      'N' => 'k',
      'B' => 
      array (
        0 => '7',
        1 => '0',
        2 => '505',
        3 => '683',
      ),
    ),
    'k' => 
    array (
      'C' => '107',
      'WX' => '500',
      'N' => 'k',
      'B' => 
      array (
        0 => '7',
        1 => '0',
        2 => '505',
        3 => '683',
      ),
    ),
    108 => 
    array (
      'C' => '108',
      'WX' => '278',
      'N' => 'l',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '257',
        3 => '683',
      ),
    ),
    'l' => 
    array (
      'C' => '108',
      'WX' => '278',
      'N' => 'l',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '257',
        3 => '683',
      ),
    ),
    109 => 
    array (
      'C' => '109',
      'WX' => '778',
      'N' => 'm',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '775',
        3 => '460',
      ),
    ),
    'm' => 
    array (
      'C' => '109',
      'WX' => '778',
      'N' => 'm',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '775',
        3 => '460',
      ),
    ),
    110 => 
    array (
      'C' => '110',
      'WX' => '500',
      'N' => 'n',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '485',
        3 => '460',
      ),
    ),
    'n' => 
    array (
      'C' => '110',
      'WX' => '500',
      'N' => 'n',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '485',
        3 => '460',
      ),
    ),
    111 => 
    array (
      'C' => '111',
      'WX' => '500',
      'N' => 'o',
      'B' => 
      array (
        0 => '29',
        1 => '-10',
        2 => '470',
        3 => '460',
      ),
    ),
    'o' => 
    array (
      'C' => '111',
      'WX' => '500',
      'N' => 'o',
      'B' => 
      array (
        0 => '29',
        1 => '-10',
        2 => '470',
        3 => '460',
      ),
    ),
    112 => 
    array (
      'C' => '112',
      'WX' => '500',
      'N' => 'p',
      'B' => 
      array (
        0 => '5',
        1 => '-217',
        2 => '470',
        3 => '460',
      ),
    ),
    'p' => 
    array (
      'C' => '112',
      'WX' => '500',
      'N' => 'p',
      'B' => 
      array (
        0 => '5',
        1 => '-217',
        2 => '470',
        3 => '460',
      ),
    ),
    113 => 
    array (
      'C' => '113',
      'WX' => '500',
      'N' => 'q',
      'B' => 
      array (
        0 => '24',
        1 => '-217',
        2 => '488',
        3 => '460',
      ),
    ),
    'q' => 
    array (
      'C' => '113',
      'WX' => '500',
      'N' => 'q',
      'B' => 
      array (
        0 => '24',
        1 => '-217',
        2 => '488',
        3 => '460',
      ),
    ),
    114 => 
    array (
      'C' => '114',
      'WX' => '333',
      'N' => 'r',
      'B' => 
      array (
        0 => '5',
        1 => '0',
        2 => '335',
        3 => '460',
      ),
    ),
    'r' => 
    array (
      'C' => '114',
      'WX' => '333',
      'N' => 'r',
      'B' => 
      array (
        0 => '5',
        1 => '0',
        2 => '335',
        3 => '460',
      ),
    ),
    115 => 
    array (
      'C' => '115',
      'WX' => '389',
      'N' => 's',
      'B' => 
      array (
        0 => '51',
        1 => '-10',
        2 => '348',
        3 => '460',
      ),
    ),
    's' => 
    array (
      'C' => '115',
      'WX' => '389',
      'N' => 's',
      'B' => 
      array (
        0 => '51',
        1 => '-10',
        2 => '348',
        3 => '460',
      ),
    ),
    116 => 
    array (
      'C' => '116',
      'WX' => '278',
      'N' => 't',
      'B' => 
      array (
        0 => '13',
        1 => '-10',
        2 => '279',
        3 => '579',
      ),
    ),
    't' => 
    array (
      'C' => '116',
      'WX' => '278',
      'N' => 't',
      'B' => 
      array (
        0 => '13',
        1 => '-10',
        2 => '279',
        3 => '579',
      ),
    ),
    117 => 
    array (
      'C' => '117',
      'WX' => '500',
      'N' => 'u',
      'B' => 
      array (
        0 => '9',
        1 => '-10',
        2 => '479',
        3 => '450',
      ),
    ),
    'u' => 
    array (
      'C' => '117',
      'WX' => '500',
      'N' => 'u',
      'B' => 
      array (
        0 => '9',
        1 => '-10',
        2 => '479',
        3 => '450',
      ),
    ),
    118 => 
    array (
      'C' => '118',
      'WX' => '500',
      'N' => 'v',
      'B' => 
      array (
        0 => '19',
        1 => '-14',
        2 => '477',
        3 => '450',
      ),
    ),
    'v' => 
    array (
      'C' => '118',
      'WX' => '500',
      'N' => 'v',
      'B' => 
      array (
        0 => '19',
        1 => '-14',
        2 => '477',
        3 => '450',
      ),
    ),
    119 => 
    array (
      'C' => '119',
      'WX' => '722',
      'N' => 'w',
      'B' => 
      array (
        0 => '21',
        1 => '-14',
        2 => '694',
        3 => '450',
      ),
    ),
    'w' => 
    array (
      'C' => '119',
      'WX' => '722',
      'N' => 'w',
      'B' => 
      array (
        0 => '21',
        1 => '-14',
        2 => '694',
        3 => '450',
      ),
    ),
    120 => 
    array (
      'C' => '120',
      'WX' => '500',
      'N' => 'x',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '479',
        3 => '450',
      ),
    ),
    'x' => 
    array (
      'C' => '120',
      'WX' => '500',
      'N' => 'x',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '479',
        3 => '450',
      ),
    ),
    121 => 
    array (
      'C' => '121',
      'WX' => '500',
      'N' => 'y',
      'B' => 
      array (
        0 => '14',
        1 => '-218',
        2 => '475',
        3 => '450',
      ),
    ),
    'y' => 
    array (
      'C' => '121',
      'WX' => '500',
      'N' => 'y',
      'B' => 
      array (
        0 => '14',
        1 => '-218',
        2 => '475',
        3 => '450',
      ),
    ),
    122 => 
    array (
      'C' => '122',
      'WX' => '444',
      'N' => 'z',
      'B' => 
      array (
        0 => '27',
        1 => '0',
        2 => '418',
        3 => '450',
      ),
    ),
    'z' => 
    array (
      'C' => '122',
      'WX' => '444',
      'N' => 'z',
      'B' => 
      array (
        0 => '27',
        1 => '0',
        2 => '418',
        3 => '450',
      ),
    ),
    123 => 
    array (
      'C' => '123',
      'WX' => '480',
      'N' => 'braceleft',
      'B' => 
      array (
        0 => '100',
        1 => '-181',
        2 => '350',
        3 => '680',
      ),
    ),
    'braceleft' => 
    array (
      'C' => '123',
      'WX' => '480',
      'N' => 'braceleft',
      'B' => 
      array (
        0 => '100',
        1 => '-181',
        2 => '350',
        3 => '680',
      ),
    ),
    124 => 
    array (
      'C' => '124',
      'WX' => '200',
      'N' => 'bar',
      'B' => 
      array (
        0 => '67',
        1 => '-218',
        2 => '133',
        3 => '782',
      ),
    ),
    'bar' => 
    array (
      'C' => '124',
      'WX' => '200',
      'N' => 'bar',
      'B' => 
      array (
        0 => '67',
        1 => '-218',
        2 => '133',
        3 => '782',
      ),
    ),
    125 => 
    array (
      'C' => '125',
      'WX' => '480',
      'N' => 'braceright',
      'B' => 
      array (
        0 => '130',
        1 => '-181',
        2 => '380',
        3 => '680',
      ),
    ),
    'braceright' => 
    array (
      'C' => '125',
      'WX' => '480',
      'N' => 'braceright',
      'B' => 
      array (
        0 => '130',
        1 => '-181',
        2 => '380',
        3 => '680',
      ),
    ),
    126 => 
    array (
      'C' => '126',
      'WX' => '541',
      'N' => 'asciitilde',
      'B' => 
      array (
        0 => '40',
        1 => '183',
        2 => '502',
        3 => '323',
      ),
    ),
    'asciitilde' => 
    array (
      'C' => '126',
      'WX' => '541',
      'N' => 'asciitilde',
      'B' => 
      array (
        0 => '40',
        1 => '183',
        2 => '502',
        3 => '323',
      ),
    ),
    161 => 
    array (
      'C' => '161',
      'WX' => '333',
      'N' => 'exclamdown',
      'B' => 
      array (
        0 => '97',
        1 => '-218',
        2 => '205',
        3 => '467',
      ),
    ),
    'exclamdown' => 
    array (
      'C' => '161',
      'WX' => '333',
      'N' => 'exclamdown',
      'B' => 
      array (
        0 => '97',
        1 => '-218',
        2 => '205',
        3 => '467',
      ),
    ),
    162 => 
    array (
      'C' => '162',
      'WX' => '500',
      'N' => 'cent',
      'B' => 
      array (
        0 => '53',
        1 => '-138',
        2 => '448',
        3 => '579',
      ),
    ),
    'cent' => 
    array (
      'C' => '162',
      'WX' => '500',
      'N' => 'cent',
      'B' => 
      array (
        0 => '53',
        1 => '-138',
        2 => '448',
        3 => '579',
      ),
    ),
    163 => 
    array (
      'C' => '163',
      'WX' => '500',
      'N' => 'sterling',
      'B' => 
      array (
        0 => '12',
        1 => '-8',
        2 => '490',
        3 => '676',
      ),
    ),
    'sterling' => 
    array (
      'C' => '163',
      'WX' => '500',
      'N' => 'sterling',
      'B' => 
      array (
        0 => '12',
        1 => '-8',
        2 => '490',
        3 => '676',
      ),
    ),
    164 => 
    array (
      'C' => '164',
      'WX' => '167',
      'N' => 'fraction',
      'B' => 
      array (
        0 => '-168',
        1 => '-14',
        2 => '331',
        3 => '676',
      ),
    ),
    'fraction' => 
    array (
      'C' => '164',
      'WX' => '167',
      'N' => 'fraction',
      'B' => 
      array (
        0 => '-168',
        1 => '-14',
        2 => '331',
        3 => '676',
      ),
    ),
    165 => 
    array (
      'C' => '165',
      'WX' => '500',
      'N' => 'yen',
      'B' => 
      array (
        0 => '-53',
        1 => '0',
        2 => '512',
        3 => '662',
      ),
    ),
    'yen' => 
    array (
      'C' => '165',
      'WX' => '500',
      'N' => 'yen',
      'B' => 
      array (
        0 => '-53',
        1 => '0',
        2 => '512',
        3 => '662',
      ),
    ),
    166 => 
    array (
      'C' => '166',
      'WX' => '500',
      'N' => 'florin',
      'B' => 
      array (
        0 => '7',
        1 => '-189',
        2 => '490',
        3 => '676',
      ),
    ),
    'florin' => 
    array (
      'C' => '166',
      'WX' => '500',
      'N' => 'florin',
      'B' => 
      array (
        0 => '7',
        1 => '-189',
        2 => '490',
        3 => '676',
      ),
    ),
    167 => 
    array (
      'C' => '167',
      'WX' => '500',
      'N' => 'section',
      'B' => 
      array (
        0 => '70',
        1 => '-148',
        2 => '426',
        3 => '676',
      ),
    ),
    'section' => 
    array (
      'C' => '167',
      'WX' => '500',
      'N' => 'section',
      'B' => 
      array (
        0 => '70',
        1 => '-148',
        2 => '426',
        3 => '676',
      ),
    ),
    168 => 
    array (
      'C' => '168',
      'WX' => '500',
      'N' => 'currency',
      'B' => 
      array (
        0 => '-22',
        1 => '58',
        2 => '522',
        3 => '602',
      ),
    ),
    'currency' => 
    array (
      'C' => '168',
      'WX' => '500',
      'N' => 'currency',
      'B' => 
      array (
        0 => '-22',
        1 => '58',
        2 => '522',
        3 => '602',
      ),
    ),
    169 => 
    array (
      'C' => '169',
      'WX' => '180',
      'N' => 'quotesingle',
      'B' => 
      array (
        0 => '48',
        1 => '431',
        2 => '133',
        3 => '676',
      ),
    ),
    'quotesingle' => 
    array (
      'C' => '169',
      'WX' => '180',
      'N' => 'quotesingle',
      'B' => 
      array (
        0 => '48',
        1 => '431',
        2 => '133',
        3 => '676',
      ),
    ),
    170 => 
    array (
      'C' => '170',
      'WX' => '444',
      'N' => 'quotedblleft',
      'B' => 
      array (
        0 => '43',
        1 => '433',
        2 => '414',
        3 => '676',
      ),
    ),
    'quotedblleft' => 
    array (
      'C' => '170',
      'WX' => '444',
      'N' => 'quotedblleft',
      'B' => 
      array (
        0 => '43',
        1 => '433',
        2 => '414',
        3 => '676',
      ),
    ),
    171 => 
    array (
      'C' => '171',
      'WX' => '500',
      'N' => 'guillemotleft',
      'B' => 
      array (
        0 => '42',
        1 => '33',
        2 => '456',
        3 => '416',
      ),
    ),
    'guillemotleft' => 
    array (
      'C' => '171',
      'WX' => '500',
      'N' => 'guillemotleft',
      'B' => 
      array (
        0 => '42',
        1 => '33',
        2 => '456',
        3 => '416',
      ),
    ),
    172 => 
    array (
      'C' => '172',
      'WX' => '333',
      'N' => 'guilsinglleft',
      'B' => 
      array (
        0 => '63',
        1 => '33',
        2 => '285',
        3 => '416',
      ),
    ),
    'guilsinglleft' => 
    array (
      'C' => '172',
      'WX' => '333',
      'N' => 'guilsinglleft',
      'B' => 
      array (
        0 => '63',
        1 => '33',
        2 => '285',
        3 => '416',
      ),
    ),
    173 => 
    array (
      'C' => '173',
      'WX' => '333',
      'N' => 'guilsinglright',
      'B' => 
      array (
        0 => '48',
        1 => '33',
        2 => '270',
        3 => '416',
      ),
    ),
    'guilsinglright' => 
    array (
      'C' => '173',
      'WX' => '333',
      'N' => 'guilsinglright',
      'B' => 
      array (
        0 => '48',
        1 => '33',
        2 => '270',
        3 => '416',
      ),
    ),
    174 => 
    array (
      'C' => '174',
      'WX' => '556',
      'N' => 'fi',
      'B' => 
      array (
        0 => '31',
        1 => '0',
        2 => '521',
        3 => '683',
      ),
    ),
    'fi' => 
    array (
      'C' => '174',
      'WX' => '556',
      'N' => 'fi',
      'B' => 
      array (
        0 => '31',
        1 => '0',
        2 => '521',
        3 => '683',
      ),
    ),
    175 => 
    array (
      'C' => '175',
      'WX' => '556',
      'N' => 'fl',
      'B' => 
      array (
        0 => '32',
        1 => '0',
        2 => '521',
        3 => '683',
      ),
    ),
    'fl' => 
    array (
      'C' => '175',
      'WX' => '556',
      'N' => 'fl',
      'B' => 
      array (
        0 => '32',
        1 => '0',
        2 => '521',
        3 => '683',
      ),
    ),
    177 => 
    array (
      'C' => '177',
      'WX' => '500',
      'N' => 'endash',
      'B' => 
      array (
        0 => '0',
        1 => '201',
        2 => '500',
        3 => '250',
      ),
    ),
    'endash' => 
    array (
      'C' => '177',
      'WX' => '500',
      'N' => 'endash',
      'B' => 
      array (
        0 => '0',
        1 => '201',
        2 => '500',
        3 => '250',
      ),
    ),
    178 => 
    array (
      'C' => '178',
      'WX' => '500',
      'N' => 'dagger',
      'B' => 
      array (
        0 => '59',
        1 => '-149',
        2 => '442',
        3 => '676',
      ),
    ),
    'dagger' => 
    array (
      'C' => '178',
      'WX' => '500',
      'N' => 'dagger',
      'B' => 
      array (
        0 => '59',
        1 => '-149',
        2 => '442',
        3 => '676',
      ),
    ),
    179 => 
    array (
      'C' => '179',
      'WX' => '500',
      'N' => 'daggerdbl',
      'B' => 
      array (
        0 => '58',
        1 => '-153',
        2 => '442',
        3 => '676',
      ),
    ),
    'daggerdbl' => 
    array (
      'C' => '179',
      'WX' => '500',
      'N' => 'daggerdbl',
      'B' => 
      array (
        0 => '58',
        1 => '-153',
        2 => '442',
        3 => '676',
      ),
    ),
    180 => 
    array (
      'C' => '180',
      'WX' => '250',
      'N' => 'periodcentered',
      'B' => 
      array (
        0 => '70',
        1 => '199',
        2 => '181',
        3 => '310',
      ),
    ),
    'periodcentered' => 
    array (
      'C' => '180',
      'WX' => '250',
      'N' => 'periodcentered',
      'B' => 
      array (
        0 => '70',
        1 => '199',
        2 => '181',
        3 => '310',
      ),
    ),
    182 => 
    array (
      'C' => '182',
      'WX' => '453',
      'N' => 'paragraph',
      'B' => 
      array (
        0 => '-22',
        1 => '-154',
        2 => '450',
        3 => '662',
      ),
    ),
    'paragraph' => 
    array (
      'C' => '182',
      'WX' => '453',
      'N' => 'paragraph',
      'B' => 
      array (
        0 => '-22',
        1 => '-154',
        2 => '450',
        3 => '662',
      ),
    ),
    183 => 
    array (
      'C' => '183',
      'WX' => '350',
      'N' => 'bullet',
      'B' => 
      array (
        0 => '40',
        1 => '196',
        2 => '310',
        3 => '466',
      ),
    ),
    'bullet' => 
    array (
      'C' => '183',
      'WX' => '350',
      'N' => 'bullet',
      'B' => 
      array (
        0 => '40',
        1 => '196',
        2 => '310',
        3 => '466',
      ),
    ),
    184 => 
    array (
      'C' => '184',
      'WX' => '333',
      'N' => 'quotesinglbase',
      'B' => 
      array (
        0 => '79',
        1 => '-141',
        2 => '218',
        3 => '102',
      ),
    ),
    'quotesinglbase' => 
    array (
      'C' => '184',
      'WX' => '333',
      'N' => 'quotesinglbase',
      'B' => 
      array (
        0 => '79',
        1 => '-141',
        2 => '218',
        3 => '102',
      ),
    ),
    185 => 
    array (
      'C' => '185',
      'WX' => '444',
      'N' => 'quotedblbase',
      'B' => 
      array (
        0 => '45',
        1 => '-141',
        2 => '416',
        3 => '102',
      ),
    ),
    'quotedblbase' => 
    array (
      'C' => '185',
      'WX' => '444',
      'N' => 'quotedblbase',
      'B' => 
      array (
        0 => '45',
        1 => '-141',
        2 => '416',
        3 => '102',
      ),
    ),
    186 => 
    array (
      'C' => '186',
      'WX' => '444',
      'N' => 'quotedblright',
      'B' => 
      array (
        0 => '30',
        1 => '433',
        2 => '401',
        3 => '676',
      ),
    ),
    'quotedblright' => 
    array (
      'C' => '186',
      'WX' => '444',
      'N' => 'quotedblright',
      'B' => 
      array (
        0 => '30',
        1 => '433',
        2 => '401',
        3 => '676',
      ),
    ),
    187 => 
    array (
      'C' => '187',
      'WX' => '500',
      'N' => 'guillemotright',
      'B' => 
      array (
        0 => '44',
        1 => '33',
        2 => '458',
        3 => '416',
      ),
    ),
    'guillemotright' => 
    array (
      'C' => '187',
      'WX' => '500',
      'N' => 'guillemotright',
      'B' => 
      array (
        0 => '44',
        1 => '33',
        2 => '458',
        3 => '416',
      ),
    ),
    188 => 
    array (
      'C' => '188',
      'WX' => '1000',
      'N' => 'ellipsis',
      'B' => 
      array (
        0 => '111',
        1 => '-11',
        2 => '888',
        3 => '100',
      ),
    ),
    'ellipsis' => 
    array (
      'C' => '188',
      'WX' => '1000',
      'N' => 'ellipsis',
      'B' => 
      array (
        0 => '111',
        1 => '-11',
        2 => '888',
        3 => '100',
      ),
    ),
    189 => 
    array (
      'C' => '189',
      'WX' => '1000',
      'N' => 'perthousand',
      'B' => 
      array (
        0 => '7',
        1 => '-19',
        2 => '994',
        3 => '706',
      ),
    ),
    'perthousand' => 
    array (
      'C' => '189',
      'WX' => '1000',
      'N' => 'perthousand',
      'B' => 
      array (
        0 => '7',
        1 => '-19',
        2 => '994',
        3 => '706',
      ),
    ),
    191 => 
    array (
      'C' => '191',
      'WX' => '444',
      'N' => 'questiondown',
      'B' => 
      array (
        0 => '30',
        1 => '-218',
        2 => '376',
        3 => '466',
      ),
    ),
    'questiondown' => 
    array (
      'C' => '191',
      'WX' => '444',
      'N' => 'questiondown',
      'B' => 
      array (
        0 => '30',
        1 => '-218',
        2 => '376',
        3 => '466',
      ),
    ),
    193 => 
    array (
      'C' => '193',
      'WX' => '333',
      'N' => 'grave',
      'B' => 
      array (
        0 => '19',
        1 => '507',
        2 => '242',
        3 => '678',
      ),
    ),
    'grave' => 
    array (
      'C' => '193',
      'WX' => '333',
      'N' => 'grave',
      'B' => 
      array (
        0 => '19',
        1 => '507',
        2 => '242',
        3 => '678',
      ),
    ),
    194 => 
    array (
      'C' => '194',
      'WX' => '333',
      'N' => 'acute',
      'B' => 
      array (
        0 => '93',
        1 => '507',
        2 => '317',
        3 => '678',
      ),
    ),
    'acute' => 
    array (
      'C' => '194',
      'WX' => '333',
      'N' => 'acute',
      'B' => 
      array (
        0 => '93',
        1 => '507',
        2 => '317',
        3 => '678',
      ),
    ),
    195 => 
    array (
      'C' => '195',
      'WX' => '333',
      'N' => 'circumflex',
      'B' => 
      array (
        0 => '11',
        1 => '507',
        2 => '322',
        3 => '674',
      ),
    ),
    'circumflex' => 
    array (
      'C' => '195',
      'WX' => '333',
      'N' => 'circumflex',
      'B' => 
      array (
        0 => '11',
        1 => '507',
        2 => '322',
        3 => '674',
      ),
    ),
    196 => 
    array (
      'C' => '196',
      'WX' => '333',
      'N' => 'tilde',
      'B' => 
      array (
        0 => '1',
        1 => '532',
        2 => '331',
        3 => '638',
      ),
    ),
    'tilde' => 
    array (
      'C' => '196',
      'WX' => '333',
      'N' => 'tilde',
      'B' => 
      array (
        0 => '1',
        1 => '532',
        2 => '331',
        3 => '638',
      ),
    ),
    197 => 
    array (
      'C' => '197',
      'WX' => '333',
      'N' => 'macron',
      'B' => 
      array (
        0 => '11',
        1 => '547',
        2 => '322',
        3 => '601',
      ),
    ),
    'macron' => 
    array (
      'C' => '197',
      'WX' => '333',
      'N' => 'macron',
      'B' => 
      array (
        0 => '11',
        1 => '547',
        2 => '322',
        3 => '601',
      ),
    ),
    198 => 
    array (
      'C' => '198',
      'WX' => '333',
      'N' => 'breve',
      'B' => 
      array (
        0 => '26',
        1 => '507',
        2 => '307',
        3 => '664',
      ),
    ),
    'breve' => 
    array (
      'C' => '198',
      'WX' => '333',
      'N' => 'breve',
      'B' => 
      array (
        0 => '26',
        1 => '507',
        2 => '307',
        3 => '664',
      ),
    ),
    199 => 
    array (
      'C' => '199',
      'WX' => '333',
      'N' => 'dotaccent',
      'B' => 
      array (
        0 => '118',
        1 => '581',
        2 => '216',
        3 => '681',
      ),
    ),
    'dotaccent' => 
    array (
      'C' => '199',
      'WX' => '333',
      'N' => 'dotaccent',
      'B' => 
      array (
        0 => '118',
        1 => '581',
        2 => '216',
        3 => '681',
      ),
    ),
    200 => 
    array (
      'C' => '200',
      'WX' => '333',
      'N' => 'dieresis',
      'B' => 
      array (
        0 => '18',
        1 => '581',
        2 => '315',
        3 => '681',
      ),
    ),
    'dieresis' => 
    array (
      'C' => '200',
      'WX' => '333',
      'N' => 'dieresis',
      'B' => 
      array (
        0 => '18',
        1 => '581',
        2 => '315',
        3 => '681',
      ),
    ),
    202 => 
    array (
      'C' => '202',
      'WX' => '333',
      'N' => 'ring',
      'B' => 
      array (
        0 => '67',
        1 => '512',
        2 => '266',
        3 => '711',
      ),
    ),
    'ring' => 
    array (
      'C' => '202',
      'WX' => '333',
      'N' => 'ring',
      'B' => 
      array (
        0 => '67',
        1 => '512',
        2 => '266',
        3 => '711',
      ),
    ),
    203 => 
    array (
      'C' => '203',
      'WX' => '333',
      'N' => 'cedilla',
      'B' => 
      array (
        0 => '52',
        1 => '-215',
        2 => '261',
        3 => '0',
      ),
    ),
    'cedilla' => 
    array (
      'C' => '203',
      'WX' => '333',
      'N' => 'cedilla',
      'B' => 
      array (
        0 => '52',
        1 => '-215',
        2 => '261',
        3 => '0',
      ),
    ),
    205 => 
    array (
      'C' => '205',
      'WX' => '333',
      'N' => 'hungarumlaut',
      'B' => 
      array (
        0 => '-3',
        1 => '507',
        2 => '377',
        3 => '678',
      ),
    ),
    'hungarumlaut' => 
    array (
      'C' => '205',
      'WX' => '333',
      'N' => 'hungarumlaut',
      'B' => 
      array (
        0 => '-3',
        1 => '507',
        2 => '377',
        3 => '678',
      ),
    ),
    206 => 
    array (
      'C' => '206',
      'WX' => '333',
      'N' => 'ogonek',
      'B' => 
      array (
        0 => '62',
        1 => '-165',
        2 => '243',
        3 => '0',
      ),
    ),
    'ogonek' => 
    array (
      'C' => '206',
      'WX' => '333',
      'N' => 'ogonek',
      'B' => 
      array (
        0 => '62',
        1 => '-165',
        2 => '243',
        3 => '0',
      ),
    ),
    207 => 
    array (
      'C' => '207',
      'WX' => '333',
      'N' => 'caron',
      'B' => 
      array (
        0 => '11',
        1 => '507',
        2 => '322',
        3 => '674',
      ),
    ),
    'caron' => 
    array (
      'C' => '207',
      'WX' => '333',
      'N' => 'caron',
      'B' => 
      array (
        0 => '11',
        1 => '507',
        2 => '322',
        3 => '674',
      ),
    ),
    208 => 
    array (
      'C' => '208',
      'WX' => '1000',
      'N' => 'emdash',
      'B' => 
      array (
        0 => '0',
        1 => '201',
        2 => '1000',
        3 => '250',
      ),
    ),
    'emdash' => 
    array (
      'C' => '208',
      'WX' => '1000',
      'N' => 'emdash',
      'B' => 
      array (
        0 => '0',
        1 => '201',
        2 => '1000',
        3 => '250',
      ),
    ),
    225 => 
    array (
      'C' => '225',
      'WX' => '889',
      'N' => 'AE',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '863',
        3 => '662',
      ),
    ),
    'AE' => 
    array (
      'C' => '225',
      'WX' => '889',
      'N' => 'AE',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '863',
        3 => '662',
      ),
    ),
    227 => 
    array (
      'C' => '227',
      'WX' => '276',
      'N' => 'ordfeminine',
      'B' => 
      array (
        0 => '4',
        1 => '394',
        2 => '270',
        3 => '676',
      ),
    ),
    'ordfeminine' => 
    array (
      'C' => '227',
      'WX' => '276',
      'N' => 'ordfeminine',
      'B' => 
      array (
        0 => '4',
        1 => '394',
        2 => '270',
        3 => '676',
      ),
    ),
    232 => 
    array (
      'C' => '232',
      'WX' => '611',
      'N' => 'Lslash',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '598',
        3 => '662',
      ),
    ),
    'Lslash' => 
    array (
      'C' => '232',
      'WX' => '611',
      'N' => 'Lslash',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '598',
        3 => '662',
      ),
    ),
    233 => 
    array (
      'C' => '233',
      'WX' => '722',
      'N' => 'Oslash',
      'B' => 
      array (
        0 => '34',
        1 => '-80',
        2 => '688',
        3 => '734',
      ),
    ),
    'Oslash' => 
    array (
      'C' => '233',
      'WX' => '722',
      'N' => 'Oslash',
      'B' => 
      array (
        0 => '34',
        1 => '-80',
        2 => '688',
        3 => '734',
      ),
    ),
    234 => 
    array (
      'C' => '234',
      'WX' => '889',
      'N' => 'OE',
      'B' => 
      array (
        0 => '30',
        1 => '-6',
        2 => '885',
        3 => '668',
      ),
    ),
    'OE' => 
    array (
      'C' => '234',
      'WX' => '889',
      'N' => 'OE',
      'B' => 
      array (
        0 => '30',
        1 => '-6',
        2 => '885',
        3 => '668',
      ),
    ),
    235 => 
    array (
      'C' => '235',
      'WX' => '310',
      'N' => 'ordmasculine',
      'B' => 
      array (
        0 => '6',
        1 => '394',
        2 => '304',
        3 => '676',
      ),
    ),
    'ordmasculine' => 
    array (
      'C' => '235',
      'WX' => '310',
      'N' => 'ordmasculine',
      'B' => 
      array (
        0 => '6',
        1 => '394',
        2 => '304',
        3 => '676',
      ),
    ),
    241 => 
    array (
      'C' => '241',
      'WX' => '667',
      'N' => 'ae',
      'B' => 
      array (
        0 => '38',
        1 => '-10',
        2 => '632',
        3 => '460',
      ),
    ),
    'ae' => 
    array (
      'C' => '241',
      'WX' => '667',
      'N' => 'ae',
      'B' => 
      array (
        0 => '38',
        1 => '-10',
        2 => '632',
        3 => '460',
      ),
    ),
    245 => 
    array (
      'C' => '245',
      'WX' => '278',
      'N' => 'dotlessi',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '253',
        3 => '460',
      ),
    ),
    'dotlessi' => 
    array (
      'C' => '245',
      'WX' => '278',
      'N' => 'dotlessi',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '253',
        3 => '460',
      ),
    ),
    248 => 
    array (
      'C' => '248',
      'WX' => '278',
      'N' => 'lslash',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '259',
        3 => '683',
      ),
    ),
    'lslash' => 
    array (
      'C' => '248',
      'WX' => '278',
      'N' => 'lslash',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '259',
        3 => '683',
      ),
    ),
    249 => 
    array (
      'C' => '249',
      'WX' => '500',
      'N' => 'oslash',
      'B' => 
      array (
        0 => '29',
        1 => '-112',
        2 => '470',
        3 => '551',
      ),
    ),
    'oslash' => 
    array (
      'C' => '249',
      'WX' => '500',
      'N' => 'oslash',
      'B' => 
      array (
        0 => '29',
        1 => '-112',
        2 => '470',
        3 => '551',
      ),
    ),
    250 => 
    array (
      'C' => '250',
      'WX' => '722',
      'N' => 'oe',
      'B' => 
      array (
        0 => '30',
        1 => '-10',
        2 => '690',
        3 => '460',
      ),
    ),
    'oe' => 
    array (
      'C' => '250',
      'WX' => '722',
      'N' => 'oe',
      'B' => 
      array (
        0 => '30',
        1 => '-10',
        2 => '690',
        3 => '460',
      ),
    ),
    251 => 
    array (
      'C' => '251',
      'WX' => '500',
      'N' => 'germandbls',
      'B' => 
      array (
        0 => '12',
        1 => '-9',
        2 => '468',
        3 => '683',
      ),
    ),
    'germandbls' => 
    array (
      'C' => '251',
      'WX' => '500',
      'N' => 'germandbls',
      'B' => 
      array (
        0 => '12',
        1 => '-9',
        2 => '468',
        3 => '683',
      ),
    ),
    'Idieresis' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'Idieresis',
      'B' => 
      array (
        0 => '18',
        1 => '0',
        2 => '315',
        3 => '835',
      ),
    ),
    'eacute' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'eacute',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '424',
        3 => '678',
      ),
    ),
    'abreve' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'abreve',
      'B' => 
      array (
        0 => '37',
        1 => '-10',
        2 => '442',
        3 => '664',
      ),
    ),
    'uhungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'uhungarumlaut',
      'B' => 
      array (
        0 => '9',
        1 => '-10',
        2 => '501',
        3 => '678',
      ),
    ),
    'ecaron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ecaron',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '424',
        3 => '674',
      ),
    ),
    'Ydieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ydieresis',
      'B' => 
      array (
        0 => '22',
        1 => '0',
        2 => '703',
        3 => '835',
      ),
    ),
    'divide' => 
    array (
      'C' => '-1',
      'WX' => '564',
      'N' => 'divide',
      'B' => 
      array (
        0 => '30',
        1 => '-10',
        2 => '534',
        3 => '516',
      ),
    ),
    'Yacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Yacute',
      'B' => 
      array (
        0 => '22',
        1 => '0',
        2 => '703',
        3 => '890',
      ),
    ),
    'Acircumflex' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Acircumflex',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '706',
        3 => '886',
      ),
    ),
    'aacute' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'aacute',
      'B' => 
      array (
        0 => '37',
        1 => '-10',
        2 => '442',
        3 => '678',
      ),
    ),
    'Ucircumflex' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ucircumflex',
      'B' => 
      array (
        0 => '14',
        1 => '-14',
        2 => '705',
        3 => '886',
      ),
    ),
    'yacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'yacute',
      'B' => 
      array (
        0 => '14',
        1 => '-218',
        2 => '475',
        3 => '678',
      ),
    ),
    'scommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'scommaaccent',
      'B' => 
      array (
        0 => '51',
        1 => '-218',
        2 => '348',
        3 => '460',
      ),
    ),
    'ecircumflex' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ecircumflex',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '424',
        3 => '674',
      ),
    ),
    'Uring' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uring',
      'B' => 
      array (
        0 => '14',
        1 => '-14',
        2 => '705',
        3 => '898',
      ),
    ),
    'Udieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Udieresis',
      'B' => 
      array (
        0 => '14',
        1 => '-14',
        2 => '705',
        3 => '835',
      ),
    ),
    'aogonek' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'aogonek',
      'B' => 
      array (
        0 => '37',
        1 => '-165',
        2 => '469',
        3 => '460',
      ),
    ),
    'Uacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uacute',
      'B' => 
      array (
        0 => '14',
        1 => '-14',
        2 => '705',
        3 => '890',
      ),
    ),
    'uogonek' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'uogonek',
      'B' => 
      array (
        0 => '9',
        1 => '-155',
        2 => '487',
        3 => '450',
      ),
    ),
    'Edieresis' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Edieresis',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '597',
        3 => '835',
      ),
    ),
    'Dcroat' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Dcroat',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '685',
        3 => '662',
      ),
    ),
    'commaaccent' => 
    array (
      'C' => '-1',
      'WX' => '250',
      'N' => 'commaaccent',
      'B' => 
      array (
        0 => '59',
        1 => '-218',
        2 => '184',
        3 => '-50',
      ),
    ),
    'copyright' => 
    array (
      'C' => '-1',
      'WX' => '760',
      'N' => 'copyright',
      'B' => 
      array (
        0 => '38',
        1 => '-14',
        2 => '722',
        3 => '676',
      ),
    ),
    'Emacron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Emacron',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '597',
        3 => '813',
      ),
    ),
    'ccaron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ccaron',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '412',
        3 => '674',
      ),
    ),
    'aring' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'aring',
      'B' => 
      array (
        0 => '37',
        1 => '-10',
        2 => '442',
        3 => '711',
      ),
    ),
    'Ncommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ncommaaccent',
      'B' => 
      array (
        0 => '12',
        1 => '-198',
        2 => '707',
        3 => '662',
      ),
    ),
    'lacute' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'lacute',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '290',
        3 => '890',
      ),
    ),
    'agrave' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'agrave',
      'B' => 
      array (
        0 => '37',
        1 => '-10',
        2 => '442',
        3 => '678',
      ),
    ),
    'Tcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Tcommaaccent',
      'B' => 
      array (
        0 => '17',
        1 => '-218',
        2 => '593',
        3 => '662',
      ),
    ),
    'Cacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Cacute',
      'B' => 
      array (
        0 => '28',
        1 => '-14',
        2 => '633',
        3 => '890',
      ),
    ),
    'atilde' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'atilde',
      'B' => 
      array (
        0 => '37',
        1 => '-10',
        2 => '442',
        3 => '638',
      ),
    ),
    'Edotaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Edotaccent',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '597',
        3 => '835',
      ),
    ),
    'scaron' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'scaron',
      'B' => 
      array (
        0 => '39',
        1 => '-10',
        2 => '350',
        3 => '674',
      ),
    ),
    'scedilla' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'scedilla',
      'B' => 
      array (
        0 => '51',
        1 => '-215',
        2 => '348',
        3 => '460',
      ),
    ),
    'iacute' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'iacute',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '290',
        3 => '678',
      ),
    ),
    'lozenge' => 
    array (
      'C' => '-1',
      'WX' => '471',
      'N' => 'lozenge',
      'B' => 
      array (
        0 => '13',
        1 => '0',
        2 => '459',
        3 => '724',
      ),
    ),
    'Rcaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Rcaron',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '659',
        3 => '886',
      ),
    ),
    'Gcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Gcommaaccent',
      'B' => 
      array (
        0 => '32',
        1 => '-218',
        2 => '709',
        3 => '676',
      ),
    ),
    'ucircumflex' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ucircumflex',
      'B' => 
      array (
        0 => '9',
        1 => '-10',
        2 => '479',
        3 => '674',
      ),
    ),
    'acircumflex' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'acircumflex',
      'B' => 
      array (
        0 => '37',
        1 => '-10',
        2 => '442',
        3 => '674',
      ),
    ),
    'Amacron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Amacron',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '706',
        3 => '813',
      ),
    ),
    'rcaron' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'rcaron',
      'B' => 
      array (
        0 => '5',
        1 => '0',
        2 => '335',
        3 => '674',
      ),
    ),
    'ccedilla' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ccedilla',
      'B' => 
      array (
        0 => '25',
        1 => '-215',
        2 => '412',
        3 => '460',
      ),
    ),
    'Zdotaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zdotaccent',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '597',
        3 => '835',
      ),
    ),
    'Thorn' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Thorn',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '542',
        3 => '662',
      ),
    ),
    'Omacron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Omacron',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '688',
        3 => '813',
      ),
    ),
    'Racute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Racute',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '659',
        3 => '890',
      ),
    ),
    'Sacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Sacute',
      'B' => 
      array (
        0 => '42',
        1 => '-14',
        2 => '491',
        3 => '890',
      ),
    ),
    'dcaron' => 
    array (
      'C' => '-1',
      'WX' => '588',
      'N' => 'dcaron',
      'B' => 
      array (
        0 => '27',
        1 => '-10',
        2 => '589',
        3 => '695',
      ),
    ),
    'Umacron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Umacron',
      'B' => 
      array (
        0 => '14',
        1 => '-14',
        2 => '705',
        3 => '813',
      ),
    ),
    'uring' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'uring',
      'B' => 
      array (
        0 => '9',
        1 => '-10',
        2 => '479',
        3 => '711',
      ),
    ),
    'threesuperior' => 
    array (
      'C' => '-1',
      'WX' => '300',
      'N' => 'threesuperior',
      'B' => 
      array (
        0 => '15',
        1 => '262',
        2 => '291',
        3 => '676',
      ),
    ),
    'Ograve' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ograve',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '688',
        3 => '890',
      ),
    ),
    'Agrave' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Agrave',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '706',
        3 => '890',
      ),
    ),
    'Abreve' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Abreve',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '706',
        3 => '876',
      ),
    ),
    'multiply' => 
    array (
      'C' => '-1',
      'WX' => '564',
      'N' => 'multiply',
      'B' => 
      array (
        0 => '38',
        1 => '8',
        2 => '527',
        3 => '497',
      ),
    ),
    'uacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'uacute',
      'B' => 
      array (
        0 => '9',
        1 => '-10',
        2 => '479',
        3 => '678',
      ),
    ),
    'Tcaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Tcaron',
      'B' => 
      array (
        0 => '17',
        1 => '0',
        2 => '593',
        3 => '886',
      ),
    ),
    'partialdiff' => 
    array (
      'C' => '-1',
      'WX' => '476',
      'N' => 'partialdiff',
      'B' => 
      array (
        0 => '17',
        1 => '-38',
        2 => '459',
        3 => '710',
      ),
    ),
    'ydieresis' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ydieresis',
      'B' => 
      array (
        0 => '14',
        1 => '-218',
        2 => '475',
        3 => '623',
      ),
    ),
    'Nacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Nacute',
      'B' => 
      array (
        0 => '12',
        1 => '-11',
        2 => '707',
        3 => '890',
      ),
    ),
    'icircumflex' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'icircumflex',
      'B' => 
      array (
        0 => '-16',
        1 => '0',
        2 => '295',
        3 => '674',
      ),
    ),
    'Ecircumflex' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Ecircumflex',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '597',
        3 => '886',
      ),
    ),
    'adieresis' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'adieresis',
      'B' => 
      array (
        0 => '37',
        1 => '-10',
        2 => '442',
        3 => '623',
      ),
    ),
    'edieresis' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'edieresis',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '424',
        3 => '623',
      ),
    ),
    'cacute' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'cacute',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '413',
        3 => '678',
      ),
    ),
    'nacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'nacute',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '485',
        3 => '678',
      ),
    ),
    'umacron' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'umacron',
      'B' => 
      array (
        0 => '9',
        1 => '-10',
        2 => '479',
        3 => '601',
      ),
    ),
    'Ncaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ncaron',
      'B' => 
      array (
        0 => '12',
        1 => '-11',
        2 => '707',
        3 => '886',
      ),
    ),
    'Iacute' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'Iacute',
      'B' => 
      array (
        0 => '18',
        1 => '0',
        2 => '317',
        3 => '890',
      ),
    ),
    'plusminus' => 
    array (
      'C' => '-1',
      'WX' => '564',
      'N' => 'plusminus',
      'B' => 
      array (
        0 => '30',
        1 => '0',
        2 => '534',
        3 => '506',
      ),
    ),
    'brokenbar' => 
    array (
      'C' => '-1',
      'WX' => '200',
      'N' => 'brokenbar',
      'B' => 
      array (
        0 => '67',
        1 => '-143',
        2 => '133',
        3 => '707',
      ),
    ),
    'registered' => 
    array (
      'C' => '-1',
      'WX' => '760',
      'N' => 'registered',
      'B' => 
      array (
        0 => '38',
        1 => '-14',
        2 => '722',
        3 => '676',
      ),
    ),
    'Gbreve' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Gbreve',
      'B' => 
      array (
        0 => '32',
        1 => '-14',
        2 => '709',
        3 => '876',
      ),
    ),
    'Idotaccent' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'Idotaccent',
      'B' => 
      array (
        0 => '18',
        1 => '0',
        2 => '315',
        3 => '835',
      ),
    ),
    'summation' => 
    array (
      'C' => '-1',
      'WX' => '600',
      'N' => 'summation',
      'B' => 
      array (
        0 => '15',
        1 => '-10',
        2 => '585',
        3 => '706',
      ),
    ),
    'Egrave' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Egrave',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '597',
        3 => '890',
      ),
    ),
    'racute' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'racute',
      'B' => 
      array (
        0 => '5',
        1 => '0',
        2 => '335',
        3 => '678',
      ),
    ),
    'omacron' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'omacron',
      'B' => 
      array (
        0 => '29',
        1 => '-10',
        2 => '470',
        3 => '601',
      ),
    ),
    'Zacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zacute',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '597',
        3 => '890',
      ),
    ),
    'Zcaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zcaron',
      'B' => 
      array (
        0 => '9',
        1 => '0',
        2 => '597',
        3 => '886',
      ),
    ),
    'greaterequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'greaterequal',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '523',
        3 => '666',
      ),
    ),
    'Eth' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Eth',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '685',
        3 => '662',
      ),
    ),
    'Ccedilla' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ccedilla',
      'B' => 
      array (
        0 => '28',
        1 => '-215',
        2 => '633',
        3 => '676',
      ),
    ),
    'lcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'lcommaaccent',
      'B' => 
      array (
        0 => '19',
        1 => '-218',
        2 => '257',
        3 => '683',
      ),
    ),
    'tcaron' => 
    array (
      'C' => '-1',
      'WX' => '326',
      'N' => 'tcaron',
      'B' => 
      array (
        0 => '13',
        1 => '-10',
        2 => '318',
        3 => '722',
      ),
    ),
    'eogonek' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'eogonek',
      'B' => 
      array (
        0 => '25',
        1 => '-165',
        2 => '424',
        3 => '460',
      ),
    ),
    'Uogonek' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uogonek',
      'B' => 
      array (
        0 => '14',
        1 => '-165',
        2 => '705',
        3 => '662',
      ),
    ),
    'Aacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Aacute',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '706',
        3 => '890',
      ),
    ),
    'Adieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Adieresis',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '706',
        3 => '835',
      ),
    ),
    'egrave' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'egrave',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '424',
        3 => '678',
      ),
    ),
    'zacute' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'zacute',
      'B' => 
      array (
        0 => '27',
        1 => '0',
        2 => '418',
        3 => '678',
      ),
    ),
    'iogonek' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'iogonek',
      'B' => 
      array (
        0 => '16',
        1 => '-165',
        2 => '265',
        3 => '683',
      ),
    ),
    'Oacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Oacute',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '688',
        3 => '890',
      ),
    ),
    'oacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'oacute',
      'B' => 
      array (
        0 => '29',
        1 => '-10',
        2 => '470',
        3 => '678',
      ),
    ),
    'amacron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'amacron',
      'B' => 
      array (
        0 => '37',
        1 => '-10',
        2 => '442',
        3 => '601',
      ),
    ),
    'sacute' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'sacute',
      'B' => 
      array (
        0 => '51',
        1 => '-10',
        2 => '348',
        3 => '678',
      ),
    ),
    'idieresis' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'idieresis',
      'B' => 
      array (
        0 => '-9',
        1 => '0',
        2 => '288',
        3 => '623',
      ),
    ),
    'Ocircumflex' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ocircumflex',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '688',
        3 => '886',
      ),
    ),
    'Ugrave' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ugrave',
      'B' => 
      array (
        0 => '14',
        1 => '-14',
        2 => '705',
        3 => '890',
      ),
    ),
    'Delta' => 
    array (
      'C' => '-1',
      'WX' => '612',
      'N' => 'Delta',
      'B' => 
      array (
        0 => '6',
        1 => '0',
        2 => '608',
        3 => '688',
      ),
    ),
    'thorn' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'thorn',
      'B' => 
      array (
        0 => '5',
        1 => '-217',
        2 => '470',
        3 => '683',
      ),
    ),
    'twosuperior' => 
    array (
      'C' => '-1',
      'WX' => '300',
      'N' => 'twosuperior',
      'B' => 
      array (
        0 => '1',
        1 => '270',
        2 => '296',
        3 => '676',
      ),
    ),
    'Odieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Odieresis',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '688',
        3 => '835',
      ),
    ),
    'mu' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'mu',
      'B' => 
      array (
        0 => '36',
        1 => '-218',
        2 => '512',
        3 => '450',
      ),
    ),
    'igrave' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'igrave',
      'B' => 
      array (
        0 => '-8',
        1 => '0',
        2 => '253',
        3 => '678',
      ),
    ),
    'ohungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ohungarumlaut',
      'B' => 
      array (
        0 => '29',
        1 => '-10',
        2 => '491',
        3 => '678',
      ),
    ),
    'Eogonek' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Eogonek',
      'B' => 
      array (
        0 => '12',
        1 => '-165',
        2 => '597',
        3 => '662',
      ),
    ),
    'dcroat' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'dcroat',
      'B' => 
      array (
        0 => '27',
        1 => '-10',
        2 => '500',
        3 => '683',
      ),
    ),
    'threequarters' => 
    array (
      'C' => '-1',
      'WX' => '750',
      'N' => 'threequarters',
      'B' => 
      array (
        0 => '15',
        1 => '-14',
        2 => '718',
        3 => '676',
      ),
    ),
    'Scedilla' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Scedilla',
      'B' => 
      array (
        0 => '42',
        1 => '-215',
        2 => '491',
        3 => '676',
      ),
    ),
    'lcaron' => 
    array (
      'C' => '-1',
      'WX' => '344',
      'N' => 'lcaron',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '347',
        3 => '695',
      ),
    ),
    'Kcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Kcommaaccent',
      'B' => 
      array (
        0 => '34',
        1 => '-198',
        2 => '723',
        3 => '662',
      ),
    ),
    'Lacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Lacute',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '598',
        3 => '890',
      ),
    ),
    'trademark' => 
    array (
      'C' => '-1',
      'WX' => '980',
      'N' => 'trademark',
      'B' => 
      array (
        0 => '30',
        1 => '256',
        2 => '957',
        3 => '662',
      ),
    ),
    'edotaccent' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'edotaccent',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '424',
        3 => '623',
      ),
    ),
    'Igrave' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'Igrave',
      'B' => 
      array (
        0 => '18',
        1 => '0',
        2 => '315',
        3 => '890',
      ),
    ),
    'Imacron' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'Imacron',
      'B' => 
      array (
        0 => '11',
        1 => '0',
        2 => '322',
        3 => '813',
      ),
    ),
    'Lcaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Lcaron',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '598',
        3 => '676',
      ),
    ),
    'onehalf' => 
    array (
      'C' => '-1',
      'WX' => '750',
      'N' => 'onehalf',
      'B' => 
      array (
        0 => '31',
        1 => '-14',
        2 => '746',
        3 => '676',
      ),
    ),
    'lessequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'lessequal',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '523',
        3 => '666',
      ),
    ),
    'ocircumflex' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ocircumflex',
      'B' => 
      array (
        0 => '29',
        1 => '-10',
        2 => '470',
        3 => '674',
      ),
    ),
    'ntilde' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ntilde',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '485',
        3 => '638',
      ),
    ),
    'Uhungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uhungarumlaut',
      'B' => 
      array (
        0 => '14',
        1 => '-14',
        2 => '705',
        3 => '890',
      ),
    ),
    'Eacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Eacute',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '597',
        3 => '890',
      ),
    ),
    'emacron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'emacron',
      'B' => 
      array (
        0 => '25',
        1 => '-10',
        2 => '424',
        3 => '601',
      ),
    ),
    'gbreve' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'gbreve',
      'B' => 
      array (
        0 => '28',
        1 => '-218',
        2 => '470',
        3 => '664',
      ),
    ),
    'onequarter' => 
    array (
      'C' => '-1',
      'WX' => '750',
      'N' => 'onequarter',
      'B' => 
      array (
        0 => '37',
        1 => '-14',
        2 => '718',
        3 => '676',
      ),
    ),
    'Scaron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Scaron',
      'B' => 
      array (
        0 => '42',
        1 => '-14',
        2 => '491',
        3 => '886',
      ),
    ),
    'Scommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Scommaaccent',
      'B' => 
      array (
        0 => '42',
        1 => '-218',
        2 => '491',
        3 => '676',
      ),
    ),
    'Ohungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ohungarumlaut',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '688',
        3 => '890',
      ),
    ),
    'degree' => 
    array (
      'C' => '-1',
      'WX' => '400',
      'N' => 'degree',
      'B' => 
      array (
        0 => '57',
        1 => '390',
        2 => '343',
        3 => '676',
      ),
    ),
    'ograve' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ograve',
      'B' => 
      array (
        0 => '29',
        1 => '-10',
        2 => '470',
        3 => '678',
      ),
    ),
    'Ccaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ccaron',
      'B' => 
      array (
        0 => '28',
        1 => '-14',
        2 => '633',
        3 => '886',
      ),
    ),
    'ugrave' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ugrave',
      'B' => 
      array (
        0 => '9',
        1 => '-10',
        2 => '479',
        3 => '678',
      ),
    ),
    'radical' => 
    array (
      'C' => '-1',
      'WX' => '453',
      'N' => 'radical',
      'B' => 
      array (
        0 => '2',
        1 => '-60',
        2 => '452',
        3 => '768',
      ),
    ),
    'Dcaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Dcaron',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '685',
        3 => '886',
      ),
    ),
    'rcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'rcommaaccent',
      'B' => 
      array (
        0 => '5',
        1 => '-218',
        2 => '335',
        3 => '460',
      ),
    ),
    'Ntilde' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ntilde',
      'B' => 
      array (
        0 => '12',
        1 => '-11',
        2 => '707',
        3 => '850',
      ),
    ),
    'otilde' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'otilde',
      'B' => 
      array (
        0 => '29',
        1 => '-10',
        2 => '470',
        3 => '638',
      ),
    ),
    'Rcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Rcommaaccent',
      'B' => 
      array (
        0 => '17',
        1 => '-198',
        2 => '659',
        3 => '662',
      ),
    ),
    'Lcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Lcommaaccent',
      'B' => 
      array (
        0 => '12',
        1 => '-218',
        2 => '598',
        3 => '662',
      ),
    ),
    'Atilde' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Atilde',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '706',
        3 => '850',
      ),
    ),
    'Aogonek' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Aogonek',
      'B' => 
      array (
        0 => '15',
        1 => '-165',
        2 => '738',
        3 => '674',
      ),
    ),
    'Aring' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Aring',
      'B' => 
      array (
        0 => '15',
        1 => '0',
        2 => '706',
        3 => '898',
      ),
    ),
    'Otilde' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Otilde',
      'B' => 
      array (
        0 => '34',
        1 => '-14',
        2 => '688',
        3 => '850',
      ),
    ),
    'zdotaccent' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'zdotaccent',
      'B' => 
      array (
        0 => '27',
        1 => '0',
        2 => '418',
        3 => '623',
      ),
    ),
    'Ecaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Ecaron',
      'B' => 
      array (
        0 => '12',
        1 => '0',
        2 => '597',
        3 => '886',
      ),
    ),
    'Iogonek' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'Iogonek',
      'B' => 
      array (
        0 => '18',
        1 => '-165',
        2 => '315',
        3 => '662',
      ),
    ),
    'kcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'kcommaaccent',
      'B' => 
      array (
        0 => '7',
        1 => '-218',
        2 => '505',
        3 => '683',
      ),
    ),
    'minus' => 
    array (
      'C' => '-1',
      'WX' => '564',
      'N' => 'minus',
      'B' => 
      array (
        0 => '30',
        1 => '220',
        2 => '534',
        3 => '286',
      ),
    ),
    'Icircumflex' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'Icircumflex',
      'B' => 
      array (
        0 => '11',
        1 => '0',
        2 => '322',
        3 => '886',
      ),
    ),
    'ncaron' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ncaron',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '485',
        3 => '674',
      ),
    ),
    'tcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'tcommaaccent',
      'B' => 
      array (
        0 => '13',
        1 => '-218',
        2 => '279',
        3 => '579',
      ),
    ),
    'logicalnot' => 
    array (
      'C' => '-1',
      'WX' => '564',
      'N' => 'logicalnot',
      'B' => 
      array (
        0 => '30',
        1 => '108',
        2 => '534',
        3 => '386',
      ),
    ),
    'odieresis' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'odieresis',
      'B' => 
      array (
        0 => '29',
        1 => '-10',
        2 => '470',
        3 => '623',
      ),
    ),
    'udieresis' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'udieresis',
      'B' => 
      array (
        0 => '9',
        1 => '-10',
        2 => '479',
        3 => '623',
      ),
    ),
    'notequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'notequal',
      'B' => 
      array (
        0 => '12',
        1 => '-31',
        2 => '537',
        3 => '547',
      ),
    ),
    'gcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'gcommaaccent',
      'B' => 
      array (
        0 => '28',
        1 => '-218',
        2 => '470',
        3 => '749',
      ),
    ),
    'eth' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'eth',
      'B' => 
      array (
        0 => '29',
        1 => '-10',
        2 => '471',
        3 => '686',
      ),
    ),
    'zcaron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'zcaron',
      'B' => 
      array (
        0 => '27',
        1 => '0',
        2 => '418',
        3 => '674',
      ),
    ),
    'ncommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ncommaaccent',
      'B' => 
      array (
        0 => '16',
        1 => '-218',
        2 => '485',
        3 => '460',
      ),
    ),
    'onesuperior' => 
    array (
      'C' => '-1',
      'WX' => '300',
      'N' => 'onesuperior',
      'B' => 
      array (
        0 => '57',
        1 => '270',
        2 => '248',
        3 => '676',
      ),
    ),
    'imacron' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'imacron',
      'B' => 
      array (
        0 => '6',
        1 => '0',
        2 => '271',
        3 => '601',
      ),
    ),
    'Euro' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'Euro',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
  ),
  'KPX' => 
  array (
    'A' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-40',
      'Gbreve' => '-40',
      'Gcommaaccent' => '-40',
      'O' => '-55',
      'Oacute' => '-55',
      'Ocircumflex' => '-55',
      'Odieresis' => '-55',
      'Ograve' => '-55',
      'Ohungarumlaut' => '-55',
      'Omacron' => '-55',
      'Oslash' => '-55',
      'Otilde' => '-55',
      'Q' => '-55',
      'T' => '-111',
      'Tcaron' => '-111',
      'Tcommaaccent' => '-111',
      'U' => '-55',
      'Uacute' => '-55',
      'Ucircumflex' => '-55',
      'Udieresis' => '-55',
      'Ugrave' => '-55',
      'Uhungarumlaut' => '-55',
      'Umacron' => '-55',
      'Uogonek' => '-55',
      'Uring' => '-55',
      'V' => '-135',
      'W' => '-90',
      'Y' => '-105',
      'Yacute' => '-105',
      'Ydieresis' => '-105',
      'quoteright' => '-111',
      'v' => '-74',
      'w' => '-92',
      'y' => '-92',
      'yacute' => '-92',
      'ydieresis' => '-92',
    ),
    'Aacute' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-40',
      'Gbreve' => '-40',
      'Gcommaaccent' => '-40',
      'O' => '-55',
      'Oacute' => '-55',
      'Ocircumflex' => '-55',
      'Odieresis' => '-55',
      'Ograve' => '-55',
      'Ohungarumlaut' => '-55',
      'Omacron' => '-55',
      'Oslash' => '-55',
      'Otilde' => '-55',
      'Q' => '-55',
      'T' => '-111',
      'Tcaron' => '-111',
      'Tcommaaccent' => '-111',
      'U' => '-55',
      'Uacute' => '-55',
      'Ucircumflex' => '-55',
      'Udieresis' => '-55',
      'Ugrave' => '-55',
      'Uhungarumlaut' => '-55',
      'Umacron' => '-55',
      'Uogonek' => '-55',
      'Uring' => '-55',
      'V' => '-135',
      'W' => '-90',
      'Y' => '-105',
      'Yacute' => '-105',
      'Ydieresis' => '-105',
      'quoteright' => '-111',
      'v' => '-74',
      'w' => '-92',
      'y' => '-92',
      'yacute' => '-92',
      'ydieresis' => '-92',
    ),
    'Abreve' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-40',
      'Gbreve' => '-40',
      'Gcommaaccent' => '-40',
      'O' => '-55',
      'Oacute' => '-55',
      'Ocircumflex' => '-55',
      'Odieresis' => '-55',
      'Ograve' => '-55',
      'Ohungarumlaut' => '-55',
      'Omacron' => '-55',
      'Oslash' => '-55',
      'Otilde' => '-55',
      'Q' => '-55',
      'T' => '-111',
      'Tcaron' => '-111',
      'Tcommaaccent' => '-111',
      'U' => '-55',
      'Uacute' => '-55',
      'Ucircumflex' => '-55',
      'Udieresis' => '-55',
      'Ugrave' => '-55',
      'Uhungarumlaut' => '-55',
      'Umacron' => '-55',
      'Uogonek' => '-55',
      'Uring' => '-55',
      'V' => '-135',
      'W' => '-90',
      'Y' => '-105',
      'Yacute' => '-105',
      'Ydieresis' => '-105',
      'quoteright' => '-111',
      'v' => '-74',
      'w' => '-92',
      'y' => '-92',
      'yacute' => '-92',
      'ydieresis' => '-92',
    ),
    'Acircumflex' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-40',
      'Gbreve' => '-40',
      'Gcommaaccent' => '-40',
      'O' => '-55',
      'Oacute' => '-55',
      'Ocircumflex' => '-55',
      'Odieresis' => '-55',
      'Ograve' => '-55',
      'Ohungarumlaut' => '-55',
      'Omacron' => '-55',
      'Oslash' => '-55',
      'Otilde' => '-55',
      'Q' => '-55',
      'T' => '-111',
      'Tcaron' => '-111',
      'Tcommaaccent' => '-111',
      'U' => '-55',
      'Uacute' => '-55',
      'Ucircumflex' => '-55',
      'Udieresis' => '-55',
      'Ugrave' => '-55',
      'Uhungarumlaut' => '-55',
      'Umacron' => '-55',
      'Uogonek' => '-55',
      'Uring' => '-55',
      'V' => '-135',
      'W' => '-90',
      'Y' => '-105',
      'Yacute' => '-105',
      'Ydieresis' => '-105',
      'quoteright' => '-111',
      'v' => '-74',
      'w' => '-92',
      'y' => '-92',
      'yacute' => '-92',
      'ydieresis' => '-92',
    ),
    'Adieresis' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-40',
      'Gbreve' => '-40',
      'Gcommaaccent' => '-40',
      'O' => '-55',
      'Oacute' => '-55',
      'Ocircumflex' => '-55',
      'Odieresis' => '-55',
      'Ograve' => '-55',
      'Ohungarumlaut' => '-55',
      'Omacron' => '-55',
      'Oslash' => '-55',
      'Otilde' => '-55',
      'Q' => '-55',
      'T' => '-111',
      'Tcaron' => '-111',
      'Tcommaaccent' => '-111',
      'U' => '-55',
      'Uacute' => '-55',
      'Ucircumflex' => '-55',
      'Udieresis' => '-55',
      'Ugrave' => '-55',
      'Uhungarumlaut' => '-55',
      'Umacron' => '-55',
      'Uogonek' => '-55',
      'Uring' => '-55',
      'V' => '-135',
      'W' => '-90',
      'Y' => '-105',
      'Yacute' => '-105',
      'Ydieresis' => '-105',
      'quoteright' => '-111',
      'v' => '-74',
      'w' => '-92',
      'y' => '-92',
      'yacute' => '-92',
      'ydieresis' => '-92',
    ),
    'Agrave' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-40',
      'Gbreve' => '-40',
      'Gcommaaccent' => '-40',
      'O' => '-55',
      'Oacute' => '-55',
      'Ocircumflex' => '-55',
      'Odieresis' => '-55',
      'Ograve' => '-55',
      'Ohungarumlaut' => '-55',
      'Omacron' => '-55',
      'Oslash' => '-55',
      'Otilde' => '-55',
      'Q' => '-55',
      'T' => '-111',
      'Tcaron' => '-111',
      'Tcommaaccent' => '-111',
      'U' => '-55',
      'Uacute' => '-55',
      'Ucircumflex' => '-55',
      'Udieresis' => '-55',
      'Ugrave' => '-55',
      'Uhungarumlaut' => '-55',
      'Umacron' => '-55',
      'Uogonek' => '-55',
      'Uring' => '-55',
      'V' => '-135',
      'W' => '-90',
      'Y' => '-105',
      'Yacute' => '-105',
      'Ydieresis' => '-105',
      'quoteright' => '-111',
      'v' => '-74',
      'w' => '-92',
      'y' => '-92',
      'yacute' => '-92',
      'ydieresis' => '-92',
    ),
    'Amacron' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-40',
      'Gbreve' => '-40',
      'Gcommaaccent' => '-40',
      'O' => '-55',
      'Oacute' => '-55',
      'Ocircumflex' => '-55',
      'Odieresis' => '-55',
      'Ograve' => '-55',
      'Ohungarumlaut' => '-55',
      'Omacron' => '-55',
      'Oslash' => '-55',
      'Otilde' => '-55',
      'Q' => '-55',
      'T' => '-111',
      'Tcaron' => '-111',
      'Tcommaaccent' => '-111',
      'U' => '-55',
      'Uacute' => '-55',
      'Ucircumflex' => '-55',
      'Udieresis' => '-55',
      'Ugrave' => '-55',
      'Uhungarumlaut' => '-55',
      'Umacron' => '-55',
      'Uogonek' => '-55',
      'Uring' => '-55',
      'V' => '-135',
      'W' => '-90',
      'Y' => '-105',
      'Yacute' => '-105',
      'Ydieresis' => '-105',
      'quoteright' => '-111',
      'v' => '-74',
      'w' => '-92',
      'y' => '-92',
      'yacute' => '-92',
      'ydieresis' => '-92',
    ),
    'Aogonek' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-40',
      'Gbreve' => '-40',
      'Gcommaaccent' => '-40',
      'O' => '-55',
      'Oacute' => '-55',
      'Ocircumflex' => '-55',
      'Odieresis' => '-55',
      'Ograve' => '-55',
      'Ohungarumlaut' => '-55',
      'Omacron' => '-55',
      'Oslash' => '-55',
      'Otilde' => '-55',
      'Q' => '-55',
      'T' => '-111',
      'Tcaron' => '-111',
      'Tcommaaccent' => '-111',
      'U' => '-55',
      'Uacute' => '-55',
      'Ucircumflex' => '-55',
      'Udieresis' => '-55',
      'Ugrave' => '-55',
      'Uhungarumlaut' => '-55',
      'Umacron' => '-55',
      'Uogonek' => '-55',
      'Uring' => '-55',
      'V' => '-135',
      'W' => '-90',
      'Y' => '-105',
      'Yacute' => '-105',
      'Ydieresis' => '-105',
      'quoteright' => '-111',
      'v' => '-74',
      'w' => '-52',
      'y' => '-52',
      'yacute' => '-52',
      'ydieresis' => '-52',
    ),
    'Aring' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-40',
      'Gbreve' => '-40',
      'Gcommaaccent' => '-40',
      'O' => '-55',
      'Oacute' => '-55',
      'Ocircumflex' => '-55',
      'Odieresis' => '-55',
      'Ograve' => '-55',
      'Ohungarumlaut' => '-55',
      'Omacron' => '-55',
      'Oslash' => '-55',
      'Otilde' => '-55',
      'Q' => '-55',
      'T' => '-111',
      'Tcaron' => '-111',
      'Tcommaaccent' => '-111',
      'U' => '-55',
      'Uacute' => '-55',
      'Ucircumflex' => '-55',
      'Udieresis' => '-55',
      'Ugrave' => '-55',
      'Uhungarumlaut' => '-55',
      'Umacron' => '-55',
      'Uogonek' => '-55',
      'Uring' => '-55',
      'V' => '-135',
      'W' => '-90',
      'Y' => '-105',
      'Yacute' => '-105',
      'Ydieresis' => '-105',
      'quoteright' => '-111',
      'v' => '-74',
      'w' => '-92',
      'y' => '-92',
      'yacute' => '-92',
      'ydieresis' => '-92',
    ),
    'Atilde' => 
    array (
      'C' => '-40',
      'Cacute' => '-40',
      'Ccaron' => '-40',
      'Ccedilla' => '-40',
      'G' => '-40',
      'Gbreve' => '-40',
      'Gcommaaccent' => '-40',
      'O' => '-55',
      'Oacute' => '-55',
      'Ocircumflex' => '-55',
      'Odieresis' => '-55',
      'Ograve' => '-55',
      'Ohungarumlaut' => '-55',
      'Omacron' => '-55',
      'Oslash' => '-55',
      'Otilde' => '-55',
      'Q' => '-55',
      'T' => '-111',
      'Tcaron' => '-111',
      'Tcommaaccent' => '-111',
      'U' => '-55',
      'Uacute' => '-55',
      'Ucircumflex' => '-55',
      'Udieresis' => '-55',
      'Ugrave' => '-55',
      'Uhungarumlaut' => '-55',
      'Umacron' => '-55',
      'Uogonek' => '-55',
      'Uring' => '-55',
      'V' => '-135',
      'W' => '-90',
      'Y' => '-105',
      'Yacute' => '-105',
      'Ydieresis' => '-105',
      'quoteright' => '-111',
      'v' => '-74',
      'w' => '-92',
      'y' => '-92',
      'yacute' => '-92',
      'ydieresis' => '-92',
    ),
    'B' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'U' => '-10',
      'Uacute' => '-10',
      'Ucircumflex' => '-10',
      'Udieresis' => '-10',
      'Ugrave' => '-10',
      'Uhungarumlaut' => '-10',
      'Umacron' => '-10',
      'Uogonek' => '-10',
      'Uring' => '-10',
    ),
    'D' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'V' => '-40',
      'W' => '-30',
      'Y' => '-55',
      'Yacute' => '-55',
      'Ydieresis' => '-55',
    ),
    'Dcaron' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'V' => '-40',
      'W' => '-30',
      'Y' => '-55',
      'Yacute' => '-55',
      'Ydieresis' => '-55',
    ),
    'Dcroat' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'V' => '-40',
      'W' => '-30',
      'Y' => '-55',
      'Yacute' => '-55',
      'Ydieresis' => '-55',
    ),
    'F' => 
    array (
      'A' => '-74',
      'Aacute' => '-74',
      'Abreve' => '-74',
      'Acircumflex' => '-74',
      'Adieresis' => '-74',
      'Agrave' => '-74',
      'Amacron' => '-74',
      'Aogonek' => '-74',
      'Aring' => '-74',
      'Atilde' => '-74',
      'a' => '-15',
      'aacute' => '-15',
      'abreve' => '-15',
      'acircumflex' => '-15',
      'adieresis' => '-15',
      'agrave' => '-15',
      'amacron' => '-15',
      'aogonek' => '-15',
      'aring' => '-15',
      'atilde' => '-15',
      'comma' => '-80',
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
      'period' => '-80',
    ),
    'J' => 
    array (
      'A' => '-60',
      'Aacute' => '-60',
      'Abreve' => '-60',
      'Acircumflex' => '-60',
      'Adieresis' => '-60',
      'Agrave' => '-60',
      'Amacron' => '-60',
      'Aogonek' => '-60',
      'Aring' => '-60',
      'Atilde' => '-60',
    ),
    'K' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'e' => '-25',
      'eacute' => '-25',
      'ecaron' => '-25',
      'ecircumflex' => '-25',
      'edieresis' => '-25',
      'edotaccent' => '-25',
      'egrave' => '-25',
      'emacron' => '-25',
      'eogonek' => '-25',
      'o' => '-35',
      'oacute' => '-35',
      'ocircumflex' => '-35',
      'odieresis' => '-35',
      'ograve' => '-35',
      'ohungarumlaut' => '-35',
      'omacron' => '-35',
      'oslash' => '-35',
      'otilde' => '-35',
      'u' => '-15',
      'uacute' => '-15',
      'ucircumflex' => '-15',
      'udieresis' => '-15',
      'ugrave' => '-15',
      'uhungarumlaut' => '-15',
      'umacron' => '-15',
      'uogonek' => '-15',
      'uring' => '-15',
      'y' => '-25',
      'yacute' => '-25',
      'ydieresis' => '-25',
    ),
    'Kcommaaccent' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'e' => '-25',
      'eacute' => '-25',
      'ecaron' => '-25',
      'ecircumflex' => '-25',
      'edieresis' => '-25',
      'edotaccent' => '-25',
      'egrave' => '-25',
      'emacron' => '-25',
      'eogonek' => '-25',
      'o' => '-35',
      'oacute' => '-35',
      'ocircumflex' => '-35',
      'odieresis' => '-35',
      'ograve' => '-35',
      'ohungarumlaut' => '-35',
      'omacron' => '-35',
      'oslash' => '-35',
      'otilde' => '-35',
      'u' => '-15',
      'uacute' => '-15',
      'ucircumflex' => '-15',
      'udieresis' => '-15',
      'ugrave' => '-15',
      'uhungarumlaut' => '-15',
      'umacron' => '-15',
      'uogonek' => '-15',
      'uring' => '-15',
      'y' => '-25',
      'yacute' => '-25',
      'ydieresis' => '-25',
    ),
    'L' => 
    array (
      'T' => '-92',
      'Tcaron' => '-92',
      'Tcommaaccent' => '-92',
      'V' => '-100',
      'W' => '-74',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'quoteright' => '-92',
      'y' => '-55',
      'yacute' => '-55',
      'ydieresis' => '-55',
    ),
    'Lacute' => 
    array (
      'T' => '-92',
      'Tcaron' => '-92',
      'Tcommaaccent' => '-92',
      'V' => '-100',
      'W' => '-74',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'quoteright' => '-92',
      'y' => '-55',
      'yacute' => '-55',
      'ydieresis' => '-55',
    ),
    'Lcaron' => 
    array (
      'quoteright' => '-92',
      'y' => '-55',
      'yacute' => '-55',
      'ydieresis' => '-55',
    ),
    'Lcommaaccent' => 
    array (
      'T' => '-92',
      'Tcaron' => '-92',
      'Tcommaaccent' => '-92',
      'V' => '-100',
      'W' => '-74',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'quoteright' => '-92',
      'y' => '-55',
      'yacute' => '-55',
      'ydieresis' => '-55',
    ),
    'Lslash' => 
    array (
      'T' => '-92',
      'Tcaron' => '-92',
      'Tcommaaccent' => '-92',
      'V' => '-100',
      'W' => '-74',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'quoteright' => '-92',
      'y' => '-55',
      'yacute' => '-55',
      'ydieresis' => '-55',
    ),
    'N' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
    ),
    'Nacute' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
    ),
    'Ncaron' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
    ),
    'Ncommaaccent' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
    ),
    'Ntilde' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
    ),
    'O' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-35',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Oacute' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-35',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Ocircumflex' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-35',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Odieresis' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-35',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Ograve' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-35',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Ohungarumlaut' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-35',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Omacron' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-35',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Oslash' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-35',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Otilde' => 
    array (
      'A' => '-35',
      'Aacute' => '-35',
      'Abreve' => '-35',
      'Acircumflex' => '-35',
      'Adieresis' => '-35',
      'Agrave' => '-35',
      'Amacron' => '-35',
      'Aogonek' => '-35',
      'Aring' => '-35',
      'Atilde' => '-35',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-35',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'P' => 
    array (
      'A' => '-92',
      'Aacute' => '-92',
      'Abreve' => '-92',
      'Acircumflex' => '-92',
      'Adieresis' => '-92',
      'Agrave' => '-92',
      'Amacron' => '-92',
      'Aogonek' => '-92',
      'Aring' => '-92',
      'Atilde' => '-92',
      'a' => '-15',
      'aacute' => '-15',
      'abreve' => '-15',
      'acircumflex' => '-15',
      'adieresis' => '-15',
      'agrave' => '-15',
      'amacron' => '-15',
      'aogonek' => '-15',
      'aring' => '-15',
      'atilde' => '-15',
      'comma' => '-111',
      'period' => '-111',
    ),
    'Q' => 
    array (
      'U' => '-10',
      'Uacute' => '-10',
      'Ucircumflex' => '-10',
      'Udieresis' => '-10',
      'Ugrave' => '-10',
      'Uhungarumlaut' => '-10',
      'Umacron' => '-10',
      'Uogonek' => '-10',
      'Uring' => '-10',
    ),
    'R' => 
    array (
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'T' => '-60',
      'Tcaron' => '-60',
      'Tcommaaccent' => '-60',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-80',
      'W' => '-55',
      'Y' => '-65',
      'Yacute' => '-65',
      'Ydieresis' => '-65',
    ),
    'Racute' => 
    array (
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'T' => '-60',
      'Tcaron' => '-60',
      'Tcommaaccent' => '-60',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-80',
      'W' => '-55',
      'Y' => '-65',
      'Yacute' => '-65',
      'Ydieresis' => '-65',
    ),
    'Rcaron' => 
    array (
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'T' => '-60',
      'Tcaron' => '-60',
      'Tcommaaccent' => '-60',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-80',
      'W' => '-55',
      'Y' => '-65',
      'Yacute' => '-65',
      'Ydieresis' => '-65',
    ),
    'Rcommaaccent' => 
    array (
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'T' => '-60',
      'Tcaron' => '-60',
      'Tcommaaccent' => '-60',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-80',
      'W' => '-55',
      'Y' => '-65',
      'Yacute' => '-65',
      'Ydieresis' => '-65',
    ),
    'T' => 
    array (
      'A' => '-93',
      'Aacute' => '-93',
      'Abreve' => '-93',
      'Acircumflex' => '-93',
      'Adieresis' => '-93',
      'Agrave' => '-93',
      'Amacron' => '-93',
      'Aogonek' => '-93',
      'Aring' => '-93',
      'Atilde' => '-93',
      'O' => '-18',
      'Oacute' => '-18',
      'Ocircumflex' => '-18',
      'Odieresis' => '-18',
      'Ograve' => '-18',
      'Ohungarumlaut' => '-18',
      'Omacron' => '-18',
      'Oslash' => '-18',
      'Otilde' => '-18',
      'a' => '-80',
      'aacute' => '-80',
      'abreve' => '-80',
      'acircumflex' => '-80',
      'adieresis' => '-40',
      'agrave' => '-40',
      'amacron' => '-40',
      'aogonek' => '-80',
      'aring' => '-80',
      'atilde' => '-40',
      'colon' => '-50',
      'comma' => '-74',
      'e' => '-70',
      'eacute' => '-70',
      'ecaron' => '-70',
      'ecircumflex' => '-70',
      'edieresis' => '-30',
      'edotaccent' => '-70',
      'egrave' => '-70',
      'emacron' => '-30',
      'eogonek' => '-70',
      'hyphen' => '-92',
      'i' => '-35',
      'iacute' => '-35',
      'iogonek' => '-35',
      'o' => '-80',
      'oacute' => '-80',
      'ocircumflex' => '-80',
      'odieresis' => '-80',
      'ograve' => '-80',
      'ohungarumlaut' => '-80',
      'omacron' => '-80',
      'oslash' => '-80',
      'otilde' => '-80',
      'period' => '-74',
      'r' => '-35',
      'racute' => '-35',
      'rcaron' => '-35',
      'rcommaaccent' => '-35',
      'semicolon' => '-55',
      'u' => '-45',
      'uacute' => '-45',
      'ucircumflex' => '-45',
      'udieresis' => '-45',
      'ugrave' => '-45',
      'uhungarumlaut' => '-45',
      'umacron' => '-45',
      'uogonek' => '-45',
      'uring' => '-45',
      'w' => '-80',
      'y' => '-80',
      'yacute' => '-80',
      'ydieresis' => '-80',
    ),
    'Tcaron' => 
    array (
      'A' => '-93',
      'Aacute' => '-93',
      'Abreve' => '-93',
      'Acircumflex' => '-93',
      'Adieresis' => '-93',
      'Agrave' => '-93',
      'Amacron' => '-93',
      'Aogonek' => '-93',
      'Aring' => '-93',
      'Atilde' => '-93',
      'O' => '-18',
      'Oacute' => '-18',
      'Ocircumflex' => '-18',
      'Odieresis' => '-18',
      'Ograve' => '-18',
      'Ohungarumlaut' => '-18',
      'Omacron' => '-18',
      'Oslash' => '-18',
      'Otilde' => '-18',
      'a' => '-80',
      'aacute' => '-80',
      'abreve' => '-80',
      'acircumflex' => '-80',
      'adieresis' => '-40',
      'agrave' => '-40',
      'amacron' => '-40',
      'aogonek' => '-80',
      'aring' => '-80',
      'atilde' => '-40',
      'colon' => '-50',
      'comma' => '-74',
      'e' => '-70',
      'eacute' => '-70',
      'ecaron' => '-70',
      'ecircumflex' => '-30',
      'edieresis' => '-30',
      'edotaccent' => '-70',
      'egrave' => '-70',
      'emacron' => '-30',
      'eogonek' => '-70',
      'hyphen' => '-92',
      'i' => '-35',
      'iacute' => '-35',
      'iogonek' => '-35',
      'o' => '-80',
      'oacute' => '-80',
      'ocircumflex' => '-80',
      'odieresis' => '-80',
      'ograve' => '-80',
      'ohungarumlaut' => '-80',
      'omacron' => '-80',
      'oslash' => '-80',
      'otilde' => '-80',
      'period' => '-74',
      'r' => '-35',
      'racute' => '-35',
      'rcaron' => '-35',
      'rcommaaccent' => '-35',
      'semicolon' => '-55',
      'u' => '-45',
      'uacute' => '-45',
      'ucircumflex' => '-45',
      'udieresis' => '-45',
      'ugrave' => '-45',
      'uhungarumlaut' => '-45',
      'umacron' => '-45',
      'uogonek' => '-45',
      'uring' => '-45',
      'w' => '-80',
      'y' => '-80',
      'yacute' => '-80',
      'ydieresis' => '-80',
    ),
    'Tcommaaccent' => 
    array (
      'A' => '-93',
      'Aacute' => '-93',
      'Abreve' => '-93',
      'Acircumflex' => '-93',
      'Adieresis' => '-93',
      'Agrave' => '-93',
      'Amacron' => '-93',
      'Aogonek' => '-93',
      'Aring' => '-93',
      'Atilde' => '-93',
      'O' => '-18',
      'Oacute' => '-18',
      'Ocircumflex' => '-18',
      'Odieresis' => '-18',
      'Ograve' => '-18',
      'Ohungarumlaut' => '-18',
      'Omacron' => '-18',
      'Oslash' => '-18',
      'Otilde' => '-18',
      'a' => '-80',
      'aacute' => '-80',
      'abreve' => '-80',
      'acircumflex' => '-80',
      'adieresis' => '-40',
      'agrave' => '-40',
      'amacron' => '-40',
      'aogonek' => '-80',
      'aring' => '-80',
      'atilde' => '-40',
      'colon' => '-50',
      'comma' => '-74',
      'e' => '-70',
      'eacute' => '-70',
      'ecaron' => '-70',
      'ecircumflex' => '-30',
      'edieresis' => '-30',
      'edotaccent' => '-70',
      'egrave' => '-30',
      'emacron' => '-70',
      'eogonek' => '-70',
      'hyphen' => '-92',
      'i' => '-35',
      'iacute' => '-35',
      'iogonek' => '-35',
      'o' => '-80',
      'oacute' => '-80',
      'ocircumflex' => '-80',
      'odieresis' => '-80',
      'ograve' => '-80',
      'ohungarumlaut' => '-80',
      'omacron' => '-80',
      'oslash' => '-80',
      'otilde' => '-80',
      'period' => '-74',
      'r' => '-35',
      'racute' => '-35',
      'rcaron' => '-35',
      'rcommaaccent' => '-35',
      'semicolon' => '-55',
      'u' => '-45',
      'uacute' => '-45',
      'ucircumflex' => '-45',
      'udieresis' => '-45',
      'ugrave' => '-45',
      'uhungarumlaut' => '-45',
      'umacron' => '-45',
      'uogonek' => '-45',
      'uring' => '-45',
      'w' => '-80',
      'y' => '-80',
      'yacute' => '-80',
      'ydieresis' => '-80',
    ),
    'U' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
    ),
    'Uacute' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
    ),
    'Ucircumflex' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
    ),
    'Udieresis' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
    ),
    'Ugrave' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
    ),
    'Uhungarumlaut' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
    ),
    'Umacron' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
    ),
    'Uogonek' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
    ),
    'Uring' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
    ),
    'V' => 
    array (
      'A' => '-135',
      'Aacute' => '-135',
      'Abreve' => '-135',
      'Acircumflex' => '-135',
      'Adieresis' => '-135',
      'Agrave' => '-135',
      'Amacron' => '-135',
      'Aogonek' => '-135',
      'Aring' => '-135',
      'Atilde' => '-135',
      'G' => '-15',
      'Gbreve' => '-15',
      'Gcommaaccent' => '-15',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'a' => '-111',
      'aacute' => '-111',
      'abreve' => '-111',
      'acircumflex' => '-71',
      'adieresis' => '-71',
      'agrave' => '-71',
      'amacron' => '-71',
      'aogonek' => '-111',
      'aring' => '-111',
      'atilde' => '-71',
      'colon' => '-74',
      'comma' => '-129',
      'e' => '-111',
      'eacute' => '-111',
      'ecaron' => '-71',
      'ecircumflex' => '-71',
      'edieresis' => '-71',
      'edotaccent' => '-111',
      'egrave' => '-71',
      'emacron' => '-71',
      'eogonek' => '-111',
      'hyphen' => '-100',
      'i' => '-60',
      'iacute' => '-60',
      'icircumflex' => '-20',
      'idieresis' => '-20',
      'igrave' => '-20',
      'imacron' => '-20',
      'iogonek' => '-60',
      'o' => '-129',
      'oacute' => '-129',
      'ocircumflex' => '-129',
      'odieresis' => '-89',
      'ograve' => '-89',
      'ohungarumlaut' => '-129',
      'omacron' => '-89',
      'oslash' => '-129',
      'otilde' => '-89',
      'period' => '-129',
      'semicolon' => '-74',
      'u' => '-75',
      'uacute' => '-75',
      'ucircumflex' => '-75',
      'udieresis' => '-75',
      'ugrave' => '-75',
      'uhungarumlaut' => '-75',
      'umacron' => '-75',
      'uogonek' => '-75',
      'uring' => '-75',
    ),
    'W' => 
    array (
      'A' => '-120',
      'Aacute' => '-120',
      'Abreve' => '-120',
      'Acircumflex' => '-120',
      'Adieresis' => '-120',
      'Agrave' => '-120',
      'Amacron' => '-120',
      'Aogonek' => '-120',
      'Aring' => '-120',
      'Atilde' => '-120',
      'O' => '-10',
      'Oacute' => '-10',
      'Ocircumflex' => '-10',
      'Odieresis' => '-10',
      'Ograve' => '-10',
      'Ohungarumlaut' => '-10',
      'Omacron' => '-10',
      'Oslash' => '-10',
      'Otilde' => '-10',
      'a' => '-80',
      'aacute' => '-80',
      'abreve' => '-80',
      'acircumflex' => '-80',
      'adieresis' => '-80',
      'agrave' => '-80',
      'amacron' => '-80',
      'aogonek' => '-80',
      'aring' => '-80',
      'atilde' => '-80',
      'colon' => '-37',
      'comma' => '-92',
      'e' => '-80',
      'eacute' => '-80',
      'ecaron' => '-80',
      'ecircumflex' => '-80',
      'edieresis' => '-40',
      'edotaccent' => '-80',
      'egrave' => '-40',
      'emacron' => '-40',
      'eogonek' => '-80',
      'hyphen' => '-65',
      'i' => '-40',
      'iacute' => '-40',
      'iogonek' => '-40',
      'o' => '-80',
      'oacute' => '-80',
      'ocircumflex' => '-80',
      'odieresis' => '-80',
      'ograve' => '-80',
      'ohungarumlaut' => '-80',
      'omacron' => '-80',
      'oslash' => '-80',
      'otilde' => '-80',
      'period' => '-92',
      'semicolon' => '-37',
      'u' => '-50',
      'uacute' => '-50',
      'ucircumflex' => '-50',
      'udieresis' => '-50',
      'ugrave' => '-50',
      'uhungarumlaut' => '-50',
      'umacron' => '-50',
      'uogonek' => '-50',
      'uring' => '-50',
      'y' => '-73',
      'yacute' => '-73',
      'ydieresis' => '-73',
    ),
    'Y' => 
    array (
      'A' => '-120',
      'Aacute' => '-120',
      'Abreve' => '-120',
      'Acircumflex' => '-120',
      'Adieresis' => '-120',
      'Agrave' => '-120',
      'Amacron' => '-120',
      'Aogonek' => '-120',
      'Aring' => '-120',
      'Atilde' => '-120',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'a' => '-100',
      'aacute' => '-100',
      'abreve' => '-100',
      'acircumflex' => '-100',
      'adieresis' => '-60',
      'agrave' => '-60',
      'amacron' => '-60',
      'aogonek' => '-100',
      'aring' => '-100',
      'atilde' => '-60',
      'colon' => '-92',
      'comma' => '-129',
      'e' => '-100',
      'eacute' => '-100',
      'ecaron' => '-100',
      'ecircumflex' => '-100',
      'edieresis' => '-60',
      'edotaccent' => '-100',
      'egrave' => '-60',
      'emacron' => '-60',
      'eogonek' => '-100',
      'hyphen' => '-111',
      'i' => '-55',
      'iacute' => '-55',
      'iogonek' => '-55',
      'o' => '-110',
      'oacute' => '-110',
      'ocircumflex' => '-110',
      'odieresis' => '-70',
      'ograve' => '-70',
      'ohungarumlaut' => '-110',
      'omacron' => '-70',
      'oslash' => '-110',
      'otilde' => '-70',
      'period' => '-129',
      'semicolon' => '-92',
      'u' => '-111',
      'uacute' => '-111',
      'ucircumflex' => '-111',
      'udieresis' => '-71',
      'ugrave' => '-71',
      'uhungarumlaut' => '-111',
      'umacron' => '-71',
      'uogonek' => '-111',
      'uring' => '-111',
    ),
    'Yacute' => 
    array (
      'A' => '-120',
      'Aacute' => '-120',
      'Abreve' => '-120',
      'Acircumflex' => '-120',
      'Adieresis' => '-120',
      'Agrave' => '-120',
      'Amacron' => '-120',
      'Aogonek' => '-120',
      'Aring' => '-120',
      'Atilde' => '-120',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'a' => '-100',
      'aacute' => '-100',
      'abreve' => '-100',
      'acircumflex' => '-100',
      'adieresis' => '-60',
      'agrave' => '-60',
      'amacron' => '-60',
      'aogonek' => '-100',
      'aring' => '-100',
      'atilde' => '-60',
      'colon' => '-92',
      'comma' => '-129',
      'e' => '-100',
      'eacute' => '-100',
      'ecaron' => '-100',
      'ecircumflex' => '-100',
      'edieresis' => '-60',
      'edotaccent' => '-100',
      'egrave' => '-60',
      'emacron' => '-60',
      'eogonek' => '-100',
      'hyphen' => '-111',
      'i' => '-55',
      'iacute' => '-55',
      'iogonek' => '-55',
      'o' => '-110',
      'oacute' => '-110',
      'ocircumflex' => '-110',
      'odieresis' => '-70',
      'ograve' => '-70',
      'ohungarumlaut' => '-110',
      'omacron' => '-70',
      'oslash' => '-110',
      'otilde' => '-70',
      'period' => '-129',
      'semicolon' => '-92',
      'u' => '-111',
      'uacute' => '-111',
      'ucircumflex' => '-111',
      'udieresis' => '-71',
      'ugrave' => '-71',
      'uhungarumlaut' => '-111',
      'umacron' => '-71',
      'uogonek' => '-111',
      'uring' => '-111',
    ),
    'Ydieresis' => 
    array (
      'A' => '-120',
      'Aacute' => '-120',
      'Abreve' => '-120',
      'Acircumflex' => '-120',
      'Adieresis' => '-120',
      'Agrave' => '-120',
      'Amacron' => '-120',
      'Aogonek' => '-120',
      'Aring' => '-120',
      'Atilde' => '-120',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'a' => '-100',
      'aacute' => '-100',
      'abreve' => '-100',
      'acircumflex' => '-100',
      'adieresis' => '-60',
      'agrave' => '-60',
      'amacron' => '-60',
      'aogonek' => '-100',
      'aring' => '-100',
      'atilde' => '-100',
      'colon' => '-92',
      'comma' => '-129',
      'e' => '-100',
      'eacute' => '-100',
      'ecaron' => '-100',
      'ecircumflex' => '-100',
      'edieresis' => '-60',
      'edotaccent' => '-100',
      'egrave' => '-60',
      'emacron' => '-60',
      'eogonek' => '-100',
      'hyphen' => '-111',
      'i' => '-55',
      'iacute' => '-55',
      'iogonek' => '-55',
      'o' => '-110',
      'oacute' => '-110',
      'ocircumflex' => '-110',
      'odieresis' => '-70',
      'ograve' => '-70',
      'ohungarumlaut' => '-110',
      'omacron' => '-70',
      'oslash' => '-110',
      'otilde' => '-70',
      'period' => '-129',
      'semicolon' => '-92',
      'u' => '-111',
      'uacute' => '-111',
      'ucircumflex' => '-111',
      'udieresis' => '-71',
      'ugrave' => '-71',
      'uhungarumlaut' => '-111',
      'umacron' => '-71',
      'uogonek' => '-111',
      'uring' => '-111',
    ),
    'a' => 
    array (
      'v' => '-20',
      'w' => '-15',
    ),
    'aacute' => 
    array (
      'v' => '-20',
      'w' => '-15',
    ),
    'abreve' => 
    array (
      'v' => '-20',
      'w' => '-15',
    ),
    'acircumflex' => 
    array (
      'v' => '-20',
      'w' => '-15',
    ),
    'adieresis' => 
    array (
      'v' => '-20',
      'w' => '-15',
    ),
    'agrave' => 
    array (
      'v' => '-20',
      'w' => '-15',
    ),
    'amacron' => 
    array (
      'v' => '-20',
      'w' => '-15',
    ),
    'aogonek' => 
    array (
      'v' => '-20',
      'w' => '-15',
    ),
    'aring' => 
    array (
      'v' => '-20',
      'w' => '-15',
    ),
    'atilde' => 
    array (
      'v' => '-20',
      'w' => '-15',
    ),
    'b' => 
    array (
      'period' => '-40',
      'u' => '-20',
      'uacute' => '-20',
      'ucircumflex' => '-20',
      'udieresis' => '-20',
      'ugrave' => '-20',
      'uhungarumlaut' => '-20',
      'umacron' => '-20',
      'uogonek' => '-20',
      'uring' => '-20',
      'v' => '-15',
    ),
    'c' => 
    array (
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'cacute' => 
    array (
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ccaron' => 
    array (
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ccedilla' => 
    array (
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'comma' => 
    array (
      'quotedblright' => '-70',
      'quoteright' => '-70',
    ),
    'e' => 
    array (
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'v' => '-25',
      'w' => '-25',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'eacute' => 
    array (
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'v' => '-25',
      'w' => '-25',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ecaron' => 
    array (
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'v' => '-25',
      'w' => '-25',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ecircumflex' => 
    array (
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'v' => '-25',
      'w' => '-25',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'edieresis' => 
    array (
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'v' => '-25',
      'w' => '-25',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'edotaccent' => 
    array (
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'v' => '-25',
      'w' => '-25',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'egrave' => 
    array (
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'v' => '-25',
      'w' => '-25',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'emacron' => 
    array (
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'v' => '-25',
      'w' => '-25',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'eogonek' => 
    array (
      'g' => '-15',
      'gbreve' => '-15',
      'gcommaaccent' => '-15',
      'v' => '-25',
      'w' => '-25',
      'x' => '-15',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'f' => 
    array (
      'a' => '-10',
      'aacute' => '-10',
      'abreve' => '-10',
      'acircumflex' => '-10',
      'adieresis' => '-10',
      'agrave' => '-10',
      'amacron' => '-10',
      'aogonek' => '-10',
      'aring' => '-10',
      'atilde' => '-10',
      'dotlessi' => '-50',
      'f' => '-25',
      'i' => '-20',
      'iacute' => '-20',
      'quoteright' => '55',
    ),
    'g' => 
    array (
      'a' => '-5',
      'aacute' => '-5',
      'abreve' => '-5',
      'acircumflex' => '-5',
      'adieresis' => '-5',
      'agrave' => '-5',
      'amacron' => '-5',
      'aogonek' => '-5',
      'aring' => '-5',
      'atilde' => '-5',
    ),
    'gbreve' => 
    array (
      'a' => '-5',
      'aacute' => '-5',
      'abreve' => '-5',
      'acircumflex' => '-5',
      'adieresis' => '-5',
      'agrave' => '-5',
      'amacron' => '-5',
      'aogonek' => '-5',
      'aring' => '-5',
      'atilde' => '-5',
    ),
    'gcommaaccent' => 
    array (
      'a' => '-5',
      'aacute' => '-5',
      'abreve' => '-5',
      'acircumflex' => '-5',
      'adieresis' => '-5',
      'agrave' => '-5',
      'amacron' => '-5',
      'aogonek' => '-5',
      'aring' => '-5',
      'atilde' => '-5',
    ),
    'h' => 
    array (
      'y' => '-5',
      'yacute' => '-5',
      'ydieresis' => '-5',
    ),
    'i' => 
    array (
      'v' => '-25',
    ),
    'iacute' => 
    array (
      'v' => '-25',
    ),
    'icircumflex' => 
    array (
      'v' => '-25',
    ),
    'idieresis' => 
    array (
      'v' => '-25',
    ),
    'igrave' => 
    array (
      'v' => '-25',
    ),
    'imacron' => 
    array (
      'v' => '-25',
    ),
    'iogonek' => 
    array (
      'v' => '-25',
    ),
    'k' => 
    array (
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-10',
      'oacute' => '-10',
      'ocircumflex' => '-10',
      'odieresis' => '-10',
      'ograve' => '-10',
      'ohungarumlaut' => '-10',
      'omacron' => '-10',
      'oslash' => '-10',
      'otilde' => '-10',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'kcommaaccent' => 
    array (
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-10',
      'oacute' => '-10',
      'ocircumflex' => '-10',
      'odieresis' => '-10',
      'ograve' => '-10',
      'ohungarumlaut' => '-10',
      'omacron' => '-10',
      'oslash' => '-10',
      'otilde' => '-10',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'l' => 
    array (
      'w' => '-10',
    ),
    'lacute' => 
    array (
      'w' => '-10',
    ),
    'lcommaaccent' => 
    array (
      'w' => '-10',
    ),
    'lslash' => 
    array (
      'w' => '-10',
    ),
    'n' => 
    array (
      'v' => '-40',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'nacute' => 
    array (
      'v' => '-40',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ncaron' => 
    array (
      'v' => '-40',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ncommaaccent' => 
    array (
      'v' => '-40',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ntilde' => 
    array (
      'v' => '-40',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'o' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'oacute' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'ocircumflex' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'odieresis' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'ograve' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'ohungarumlaut' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'omacron' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'oslash' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'otilde' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'p' => 
    array (
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'period' => 
    array (
      'quotedblright' => '-70',
      'quoteright' => '-70',
    ),
    'quotedblleft' => 
    array (
      'A' => '-80',
      'Aacute' => '-80',
      'Abreve' => '-80',
      'Acircumflex' => '-80',
      'Adieresis' => '-80',
      'Agrave' => '-80',
      'Amacron' => '-80',
      'Aogonek' => '-80',
      'Aring' => '-80',
      'Atilde' => '-80',
    ),
    'quoteleft' => 
    array (
      'A' => '-80',
      'Aacute' => '-80',
      'Abreve' => '-80',
      'Acircumflex' => '-80',
      'Adieresis' => '-80',
      'Agrave' => '-80',
      'Amacron' => '-80',
      'Aogonek' => '-80',
      'Aring' => '-80',
      'Atilde' => '-80',
      'quoteleft' => '-74',
    ),
    'quoteright' => 
    array (
      'd' => '-50',
      'dcroat' => '-50',
      'l' => '-10',
      'lacute' => '-10',
      'lcommaaccent' => '-10',
      'lslash' => '-10',
      'quoteright' => '-74',
      'r' => '-50',
      'racute' => '-50',
      'rcaron' => '-50',
      'rcommaaccent' => '-50',
      's' => '-55',
      'sacute' => '-55',
      'scaron' => '-55',
      'scedilla' => '-55',
      'scommaaccent' => '-55',
      'space' => '-74',
      't' => '-18',
      'tcommaaccent' => '-18',
      'v' => '-50',
    ),
    'r' => 
    array (
      'comma' => '-40',
      'g' => '-18',
      'gbreve' => '-18',
      'gcommaaccent' => '-18',
      'hyphen' => '-20',
      'period' => '-55',
    ),
    'racute' => 
    array (
      'comma' => '-40',
      'g' => '-18',
      'gbreve' => '-18',
      'gcommaaccent' => '-18',
      'hyphen' => '-20',
      'period' => '-55',
    ),
    'rcaron' => 
    array (
      'comma' => '-40',
      'g' => '-18',
      'gbreve' => '-18',
      'gcommaaccent' => '-18',
      'hyphen' => '-20',
      'period' => '-55',
    ),
    'rcommaaccent' => 
    array (
      'comma' => '-40',
      'g' => '-18',
      'gbreve' => '-18',
      'gcommaaccent' => '-18',
      'hyphen' => '-20',
      'period' => '-55',
    ),
    'space' => 
    array (
      'A' => '-55',
      'Aacute' => '-55',
      'Abreve' => '-55',
      'Acircumflex' => '-55',
      'Adieresis' => '-55',
      'Agrave' => '-55',
      'Amacron' => '-55',
      'Aogonek' => '-55',
      'Aring' => '-55',
      'Atilde' => '-55',
      'T' => '-18',
      'Tcaron' => '-18',
      'Tcommaaccent' => '-18',
      'V' => '-50',
      'W' => '-30',
      'Y' => '-90',
      'Yacute' => '-90',
      'Ydieresis' => '-90',
    ),
    'v' => 
    array (
      'a' => '-25',
      'aacute' => '-25',
      'abreve' => '-25',
      'acircumflex' => '-25',
      'adieresis' => '-25',
      'agrave' => '-25',
      'amacron' => '-25',
      'aogonek' => '-25',
      'aring' => '-25',
      'atilde' => '-25',
      'comma' => '-65',
      'e' => '-15',
      'eacute' => '-15',
      'ecaron' => '-15',
      'ecircumflex' => '-15',
      'edieresis' => '-15',
      'edotaccent' => '-15',
      'egrave' => '-15',
      'emacron' => '-15',
      'eogonek' => '-15',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
      'period' => '-65',
    ),
    'w' => 
    array (
      'a' => '-10',
      'aacute' => '-10',
      'abreve' => '-10',
      'acircumflex' => '-10',
      'adieresis' => '-10',
      'agrave' => '-10',
      'amacron' => '-10',
      'aogonek' => '-10',
      'aring' => '-10',
      'atilde' => '-10',
      'comma' => '-65',
      'o' => '-10',
      'oacute' => '-10',
      'ocircumflex' => '-10',
      'odieresis' => '-10',
      'ograve' => '-10',
      'ohungarumlaut' => '-10',
      'omacron' => '-10',
      'oslash' => '-10',
      'otilde' => '-10',
      'period' => '-65',
    ),
    'x' => 
    array (
      'e' => '-15',
      'eacute' => '-15',
      'ecaron' => '-15',
      'ecircumflex' => '-15',
      'edieresis' => '-15',
      'edotaccent' => '-15',
      'egrave' => '-15',
      'emacron' => '-15',
      'eogonek' => '-15',
    ),
    'y' => 
    array (
      'comma' => '-65',
      'period' => '-65',
    ),
    'yacute' => 
    array (
      'comma' => '-65',
      'period' => '-65',
    ),
    'ydieresis' => 
    array (
      'comma' => '-65',
      'period' => '-65',
    ),
  ),
  '_version_' => 1,
);