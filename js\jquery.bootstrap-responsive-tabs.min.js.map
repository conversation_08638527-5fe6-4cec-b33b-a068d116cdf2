{"version": 3, "file": "jquery.bootstrap-responsive-tabs.min.js", "sources": ["../../src/jquery.bootstrap-responsive-tabs.js"], "names": ["$", "defaults", "accordionOn", "fn", "responsiveTabs", "options", "config", "extend", "accordion", "each", "index", "value", "this", "$self", "$navTabs", "find", "$tabContent", "first", "attr", "parent", "$tabs", "children", "add", "wrapAll", "$container", "addClass", "i", "$this", "id", "active", "last", "hasClass", "length", "clone", "insertBefore", "$accordionLinks", "on", "event", "preventDefault", "$li", "$siblings", "siblings", "$accordionLink", "removeClass", "$tabLink", "j<PERSON><PERSON><PERSON>"], "mappings": "CAQE,SAASA,GAET,YAED,IAAIC,IACHC,aAAc,MAGfF,GAAEG,GAAGC,eAAiB,SAASC,GAE9B,GAAIC,GAASN,EAAEO,UAAWN,EAAUI,GAC9BG,EAAY,EAMhB,OAJFR,GAAES,KAAKH,EAAOJ,YAAa,SAASQ,EAAOC,GAC1CH,GAAa,cAAgBG,IAGrBC,KAAKH,KAAK,WAEf,GAAII,GAAQb,EAAEY,MACVE,EAAWD,EAAME,KAAK,YACtBC,EAAchB,EAAEc,EAASG,QAAQC,KAAK,SAASC,OAAO,gBACtDC,EAAQJ,EAAYK,SAAS,YAGjCR,GAAMS,IAAIN,GAAaO,QAAQ,4CAE/B,IAAIC,GAAaX,EAAMM,OAAO,6BAE9BK,GAAWC,SAASjB,GAGpBM,EAASL,KAAK,SAASiB,GACrB,GAAIC,GAAQ3B,EAAEY,MACVgB,EAAKD,EAAMT,KAAK,QAChBW,EAAS,GACTZ,EAAQ,GACRa,EAAO,EAGPH,GAAMR,OAAO,MAAMY,SAAS,YAC9BF,EAAS,WAID,IAANH,IACFT,EAAQ,UAINS,IAAMZ,EAASkB,OAAS,IAC1BF,EAAO,SAGTH,EAAMM,OAAM,GAAOR,SAAS,iBAAmBI,EAASZ,EAAQa,GAAMI,aAAaN,IAGrF,IAAIO,GAAkBnB,EAAYK,SAAS,kBAG3CP,GAASsB,GAAG,QAAS,SAASC,GAC5BA,EAAMC,gBAEN,IAAIX,GAAQ3B,EAAEY,MACV2B,EAAMZ,EAAMR,OAAO,MACnBqB,EAAYD,EAAIE,SAAS,MACzBb,EAAKD,EAAMT,KAAK,QAChBwB,EAAiB1B,EAAYK,SAAS,WAAaO,EAAK,KAEvDW,GAAIR,SAAS,YAChBQ,EAAId,SAAS,UACbe,EAAUG,YAAY,UAEtBvB,EAAMuB,YAAY,UAClB3C,EAAE4B,GAAIH,SAAS,UAEfU,EAAgBQ,YAAY,UAC5BD,EAAejB,SAAS,aAK5BU,EAAgBC,GAAG,QAAS,SAASC,GACnCA,EAAMC,gBAEN,IAAIX,GAAQ3B,EAAEY,MACVgB,EAAKD,EAAMT,KAAK,QAChB0B,EAAW/B,EAAME,KAAK,gBAAkBa,EAAK,MAAMT,OAAO,KAEzDQ,GAAMI,SAAS,YAClBI,EAAgBQ,YAAY,UAC5BhB,EAAMF,SAAS,UAEfL,EAAMuB,YAAY,UAClB3C,EAAE4B,GAAIH,SAAS,UAEfX,EAASK,OAAO,MAAMwB,YAAY,UAClCC,EAASnB,SAAS,iBAQ1BoB"}