<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>You're Offline - Vasavi Matrimony</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .offline-container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .offline-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.8;
            animation: pulse 2s infinite;
        }
        
        .offline-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .offline-message {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .retry-btn {
            background: #549a11;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .retry-btn:hover {
            background: #4a8a0f;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .offline-features {
            margin-top: 30px;
            text-align: left;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .feature-icon {
            font-size: 20px;
            margin-right: 15px;
            width: 30px;
            text-align: center;
        }
        
        .feature-text {
            font-size: 14px;
        }
        
        .connection-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 10px;
            font-size: 14px;
        }
        
        .status-offline {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.3);
        }
        
        .status-online {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 20px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #549a11;
            font-weight: bold;
        }
        
        @media (max-width: 480px) {
            .offline-container {
                padding: 30px 20px;
            }
            
            .offline-icon {
                font-size: 60px;
            }
            
            .offline-title {
                font-size: 24px;
            }
            
            .offline-message {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container fade-in">
        <div class="logo">VM</div>
        
        <div class="offline-icon">📡</div>
        
        <h1 class="offline-title">You're Offline</h1>
        
        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry, you can still access some features while offline.
        </p>
        
        <div class="connection-status status-offline" id="connectionStatus">
            🔴 No internet connection
        </div>
        
        <button class="retry-btn" onclick="checkConnection()">
            🔄 Try Again
        </button>
        
        <button class="retry-btn" onclick="goHome()">
            🏠 Go to Homepage
        </button>
        
        <div class="offline-features">
            <h3 style="margin-bottom: 15px; text-align: center;">Available Offline:</h3>
            
            <div class="feature-item">
                <div class="feature-icon">👤</div>
                <div class="feature-text">View your cached profile information</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">📞</div>
                <div class="feature-text">Access your call history</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">💬</div>
                <div class="feature-text">Read previously loaded messages</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">🔍</div>
                <div class="feature-text">Browse cached search results</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">⚙️</div>
                <div class="feature-text">Modify app settings</div>
            </div>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.textContent = '🟢 Back online!';
                statusElement.className = 'connection-status status-online';
                
                // Auto-reload after a short delay when back online
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                statusElement.textContent = '🔴 No internet connection';
                statusElement.className = 'connection-status status-offline';
            }
        }
        
        // Check connection manually
        function checkConnection() {
            updateConnectionStatus();
            
            if (navigator.onLine) {
                // Try to fetch a small resource to verify actual connectivity
                fetch('/', { method: 'HEAD', cache: 'no-cache' })
                    .then(() => {
                        window.location.reload();
                    })
                    .catch(() => {
                        alert('Still having connection issues. Please check your internet connection.');
                    });
            } else {
                alert('Please check your internet connection and try again.');
            }
        }
        
        // Go to homepage
        function goHome() {
            if (navigator.onLine) {
                window.location.href = '/';
            } else {
                // Try to load cached homepage
                window.location.href = '/';
            }
        }
        
        // Listen for online/offline events
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Periodic connection check
        setInterval(() => {
            if (navigator.onLine) {
                // Ping server to check actual connectivity
                fetch('/', { method: 'HEAD', cache: 'no-cache' })
                    .then(() => {
                        updateConnectionStatus();
                    })
                    .catch(() => {
                        // Still offline despite navigator.onLine being true
                        const statusElement = document.getElementById('connectionStatus');
                        statusElement.textContent = '🟡 Limited connectivity';
                        statusElement.className = 'connection-status status-offline';
                    });
            }
        }, 10000); // Check every 10 seconds
        
        // Service Worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(registration => {
                console.log('Service Worker is ready');
            });
        }
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'r' || e.key === 'R') {
                e.preventDefault();
                checkConnection();
            } else if (e.key === 'h' || e.key === 'H') {
                e.preventDefault();
                goHome();
            }
        });
        
        // Add touch gestures for mobile
        let touchStartY = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', (e) => {
            const touchEndY = e.changedTouches[0].clientY;
            const diff = touchStartY - touchEndY;
            
            // Swipe up to retry
            if (diff > 50) {
                checkConnection();
            }
        });
    </script>
</body>
</html>
