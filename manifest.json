{"name": "<PERSON><PERSON><PERSON> Matrimony - Find Your Perfect Life Partner", "short_name": "Vasavi Matrimony", "description": "A comprehensive matrimony platform connecting individuals based on religion, caste, profession, and preferences. Find your perfect life partner with advanced search, video calling, and secure messaging.", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#549a11", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["lifestyle", "social", "dating"], "screenshots": [{"src": "img/pwa/screenshot-mobile-1.png", "sizes": "320x640", "type": "image/png", "form_factor": "narrow", "label": "Profile browsing on mobile"}, {"src": "img/pwa/screenshot-mobile-2.png", "sizes": "320x640", "type": "image/png", "form_factor": "narrow", "label": "Video calling interface"}, {"src": "img/pwa/screenshot-desktop-1.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Desktop dashboard view"}], "icons": [{"src": "img/pwa/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "img/pwa/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "img/pwa/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "img/pwa/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "img/pwa/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "img/pwa/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "img/pwa/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "img/pwa/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "Search Profiles", "short_name": "Search", "description": "Find potential matches with advanced search", "url": "/search", "icons": [{"src": "img/pwa/shortcut-search.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Messages", "short_name": "Messages", "description": "View and send messages", "url": "/inboxMessages", "icons": [{"src": "img/pwa/shortcut-messages.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Video Calls", "short_name": "Calls", "description": "Video call history and settings", "url": "/call-history", "icons": [{"src": "img/pwa/shortcut-video.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "My Profile", "short_name": "Profile", "description": "View and edit your profile", "url": "/view-profile", "icons": [{"src": "img/pwa/shortcut-profile.png", "sizes": "96x96", "type": "image/png"}]}], "related_applications": [{"platform": "play", "url": "https://play.google.com/store/apps/details?id=com.vasavi.matrimony", "id": "com.vasavi.matrimony"}, {"platform": "itunes", "url": "https://apps.apple.com/app/vasavi-matrimony/id123456789"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}