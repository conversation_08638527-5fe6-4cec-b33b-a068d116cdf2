							<option value="No Brother" <?php if($DatabaseCo->dbRow->no_of_brothers=='No Brother'){echo "selected";}?>>No Brother</option>
                            <option value="1 Brother" <?php if($DatabaseCo->dbRow->no_of_brothers=='1 Brother'){echo "selected";}?>>1 Brother</option>
                            <option value="2 Brothers" <?php if($DatabaseCo->dbRow->no_of_brothers=='2 Brother'){echo "selected";}?>>2 Brothers</option>
                            <option value="3 Brothers" <?php if($DatabaseCo->dbRow->no_of_brothers=='3 Brothers'){echo "selected";}?>>3 Brothers</option>
                            <option value="4 Brothers" <?php if($DatabaseCo->dbRow->no_of_brothers=='4 Brothers'){echo "selected";}?>>4 Brothers</option>
                            <option value="4 + Brothers" <?php if($DatabaseCo->dbRow->no_of_brothers=='4 + Brothers'){echo "selected";}?>>4 + Brothers</option>