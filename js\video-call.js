/**
 * Video Call JavaScript Module
 * Matrimony Platform - Video Call Integration
 */

class VideoCallManager {
    constructor() {
        this.currentCall = null;
        this.isCallActive = false;
        this.callWindow = null;
        this.notificationPermission = false;
        
        this.init();
    }
    
    /**
     * Initialize video call manager
     */
    init() {
        this.requestNotificationPermission();
        this.setupEventListeners();
        this.checkIncomingCalls();
    }
    
    /**
     * Request notification permission
     */
    async requestNotificationPermission() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            this.notificationPermission = permission === 'granted';
        }
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Video call buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('video-call-btn')) {
                const receiverId = e.target.dataset.userId;
                this.initiateCall(receiverId);
            }
        });
        
        // Handle incoming call notifications
        window.addEventListener('message', (event) => {
            if (event.data.type === 'incoming_call') {
                this.handleIncomingCall(event.data);
            }
        });
    }
    
    /**
     * Initiate video call
     */
    async initiateCall(receiverId) {
        try {
            // Check if user can make calls
            const permissionCheck = await this.checkCallPermissions(receiverId);
            if (!permissionCheck.allowed) {
                this.showError(permissionCheck.message);
                return;
            }
            
            // Show loading
            this.showCallInitiating(receiverId);
            
            // Call API to initiate call
            const response = await fetch('video-call/agora-token-server.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'initiate_call',
                    caller_id: this.getCurrentUserId(),
                    receiver_id: receiverId
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.currentCall = result.data;
                this.openCallWindow(result.data);
            } else {
                this.showError(result.message);
            }
            
        } catch (error) {
            console.error('Failed to initiate call:', error);
            this.showError('Failed to initiate call. Please try again.');
        } finally {
            this.hideCallInitiating();
        }
    }
    
    /**
     * Check call permissions
     */
    async checkCallPermissions(receiverId) {
        try {
            const response = await fetch('api/check_call_permissions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    caller_id: this.getCurrentUserId(),
                    receiver_id: receiverId
                })
            });
            
            return await response.json();
            
        } catch (error) {
            return {
                allowed: false,
                message: 'Unable to verify call permissions'
            };
        }
    }
    
    /**
     * Open call window
     */
    openCallWindow(callData) {
        const callUrl = `video-call/video-call-room.php?call_id=${callData.call_id}&channel=${callData.channel}`;
        
        this.callWindow = window.open(
            callUrl,
            'video_call',
            'width=1200,height=800,resizable=yes,scrollbars=no,toolbar=no,menubar=no,location=no,status=no'
        );
        
        if (this.callWindow) {
            this.isCallActive = true;
            this.monitorCallWindow();
        } else {
            this.showError('Please allow popups for video calling');
        }
    }
    
    /**
     * Monitor call window
     */
    monitorCallWindow() {
        const checkWindow = setInterval(() => {
            if (this.callWindow && this.callWindow.closed) {
                clearInterval(checkWindow);
                this.isCallActive = false;
                this.currentCall = null;
                this.callWindow = null;
            }
        }, 1000);
    }
    
    /**
     * Handle incoming call
     */
    handleIncomingCall(callData) {
        if (this.isCallActive) {
            // Reject call if already in a call
            this.rejectCall(callData.call_id, 'busy');
            return;
        }
        
        this.showIncomingCallNotification(callData);
    }
    
    /**
     * Show incoming call notification
     */
    showIncomingCallNotification(callData) {
        // Browser notification
        if (this.notificationPermission) {
            const notification = new Notification(`Incoming call from ${callData.caller_name}`, {
                icon: callData.caller_photo || 'img/default-avatar.png',
                body: 'Click to answer the call',
                requireInteraction: true,
                actions: [
                    { action: 'answer', title: 'Answer' },
                    { action: 'reject', title: 'Reject' }
                ]
            });
            
            notification.onclick = () => {
                this.acceptIncomingCall(callData);
                notification.close();
            };
            
            // Auto-close notification after 30 seconds
            setTimeout(() => {
                notification.close();
            }, 30000);
        }
        
        // In-page notification
        this.showInPageCallNotification(callData);
    }
    
    /**
     * Show in-page call notification
     */
    showInPageCallNotification(callData) {
        const notificationHtml = `
            <div id="incoming-call-notification" class="incoming-call-notification">
                <div class="call-notification-content">
                    <div class="caller-info">
                        <img src="my_photos/${callData.caller_photo || 'default-avatar.png'}" 
                             alt="${callData.caller_name}" class="caller-avatar">
                        <div class="caller-details">
                            <h4>${callData.caller_name}</h4>
                            <p>Incoming video call...</p>
                        </div>
                    </div>
                    <div class="call-actions">
                        <button class="btn btn-success" onclick="videoCallManager.acceptIncomingCall(${JSON.stringify(callData).replace(/"/g, '&quot;')})">
                            <i class="fas fa-phone"></i> Answer
                        </button>
                        <button class="btn btn-danger" onclick="videoCallManager.rejectIncomingCall('${callData.call_id}')">
                            <i class="fas fa-phone-slash"></i> Reject
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Remove existing notification
        const existing = document.getElementById('incoming-call-notification');
        if (existing) {
            existing.remove();
        }
        
        // Add new notification
        document.body.insertAdjacentHTML('beforeend', notificationHtml);
        
        // Auto-remove after 30 seconds
        setTimeout(() => {
            this.removeIncomingCallNotification();
        }, 30000);
    }
    
    /**
     * Accept incoming call
     */
    acceptIncomingCall(callData) {
        this.removeIncomingCallNotification();
        
        const callUrl = `video-call/video-call-room.php?call_id=${callData.call_id}&channel=${callData.channel}&incoming=true`;
        
        this.callWindow = window.open(
            callUrl,
            'video_call',
            'width=1200,height=800,resizable=yes,scrollbars=no,toolbar=no,menubar=no,location=no,status=no'
        );
        
        if (this.callWindow) {
            this.isCallActive = true;
            this.currentCall = callData;
            this.monitorCallWindow();
        }
    }
    
    /**
     * Reject incoming call
     */
    async rejectIncomingCall(callId, reason = 'rejected') {
        this.removeIncomingCallNotification();
        
        try {
            await fetch('video-call/agora-token-server.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'reject_call',
                    call_id: callId,
                    reason: reason
                })
            });
        } catch (error) {
            console.error('Failed to reject call:', error);
        }
    }
    
    /**
     * Remove incoming call notification
     */
    removeIncomingCallNotification() {
        const notification = document.getElementById('incoming-call-notification');
        if (notification) {
            notification.remove();
        }
    }
    
    /**
     * Check for incoming calls periodically
     */
    checkIncomingCalls() {
        setInterval(async () => {
            if (this.isCallActive) return;
            
            try {
                const response = await fetch('api/check_incoming_calls.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: this.getCurrentUserId()
                    })
                });
                
                const result = await response.json();
                
                if (result.success && result.data && result.data.length > 0) {
                    result.data.forEach(call => {
                        this.handleIncomingCall(call);
                    });
                }
                
            } catch (error) {
                console.error('Failed to check incoming calls:', error);
            }
        }, 5000); // Check every 5 seconds
    }
    
    /**
     * Show call initiating modal
     */
    showCallInitiating(receiverId) {
        const modal = `
            <div id="call-initiating-modal" class="modal fade" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-body text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            <h5>Initiating call...</h5>
                            <p>Please wait while we connect you.</p>
                            <button type="button" class="btn btn-secondary" onclick="videoCallManager.cancelCallInitiation()">
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modal);
        $('#call-initiating-modal').modal('show');
    }
    
    /**
     * Hide call initiating modal
     */
    hideCallInitiating() {
        const modal = document.getElementById('call-initiating-modal');
        if (modal) {
            $('#call-initiating-modal').modal('hide');
            setTimeout(() => modal.remove(), 500);
        }
    }
    
    /**
     * Cancel call initiation
     */
    cancelCallInitiation() {
        this.hideCallInitiating();
        // Additional cleanup if needed
    }
    
    /**
     * Show error message
     */
    showError(message) {
        // You can customize this to use your preferred notification system
        alert(message);
    }
    
    /**
     * Get current user ID
     */
    getCurrentUserId() {
        // This should return the current logged-in user's ID
        // You can get this from session, local storage, or a global variable
        return window.currentUserId || document.querySelector('meta[name="user-id"]')?.content;
    }
    
    /**
     * Get call history
     */
    async getCallHistory(limit = 20) {
        try {
            const response = await fetch('api/get_call_history.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: this.getCurrentUserId(),
                    limit: limit
                })
            });
            
            return await response.json();
            
        } catch (error) {
            console.error('Failed to get call history:', error);
            return { success: false, message: 'Failed to load call history' };
        }
    }
    
    /**
     * Check if user can make video calls
     */
    async canMakeVideoCalls() {
        try {
            const response = await fetch('api/check_video_call_permission.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: this.getCurrentUserId()
                })
            });
            
            return await response.json();
            
        } catch (error) {
            return { allowed: false, message: 'Unable to check permissions' };
        }
    }
}

// Initialize video call manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.videoCallManager = new VideoCallManager();
});

// Utility functions for adding video call buttons to profiles
function addVideoCallButton(userId, containerSelector) {
    const container = document.querySelector(containerSelector);
    if (!container) return;
    
    const button = document.createElement('button');
    button.className = 'btn btn-primary video-call-btn';
    button.dataset.userId = userId;
    button.innerHTML = '<i class="fas fa-video"></i> Video Call';
    
    container.appendChild(button);
}

// CSS for video call notifications (add to your CSS file)
const videoCallCSS = `
.incoming-call-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    padding: 20px;
    z-index: 9999;
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.call-notification-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.caller-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.caller-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.caller-details h4 {
    margin: 0;
    font-size: 16px;
}

.caller-details p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.call-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.call-actions .btn {
    flex: 1;
    padding: 8px 16px;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.video-call-btn {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.video-call-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.video-call-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = videoCallCSS;
document.head.appendChild(style);
