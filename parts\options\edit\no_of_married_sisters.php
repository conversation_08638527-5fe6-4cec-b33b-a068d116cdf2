							<option value="No married sister" <?php if($DatabaseCo->dbRow->no_marri_sister=='No married sister'){echo "selected";}?>>No married Sister</option>
							<option value="1 married sister" <?php if($DatabaseCo->dbRow->no_marri_sister=='1 married sister'){echo "selected";}?>>1 married sister</option>
							<option value="2 married sisters" <?php if($DatabaseCo->dbRow->no_marri_sister=='2 married sisters'){echo "selected";}?>>2 married sisters</option>
							<option value="3 married sisters" <?php if($DatabaseCo->dbRow->no_marri_sister=='3 married sisters'){echo "selected";}?>>3 married sisters</option>
							<option value="4 married sisters" <?php if($DatabaseCo->dbRow->no_marri_sister=='4 married sisters'){echo "selected";}?>>4 married sisters</option>
							<option value="4+ married sisters" <?php if($DatabaseCo->dbRow->no_marri_sister=='4+ married sisters'){echo "selected";}?>>4+ married sisters</option>