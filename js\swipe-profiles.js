/**
 * Swipe-based Profile Browsing
 * Modern Tinder-like interface for matrimony profiles
 */

class SwipeProfileManager {
    constructor() {
        this.currentProfileIndex = 0;
        this.profiles = [];
        this.isLoading = false;
        this.swipeContainer = null;
        this.profileCards = [];
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.isDragging = false;
        this.currentCard = null;
        this.swipeThreshold = 100;
        this.rotationFactor = 0.1;
        
        this.init();
    }
    
    /**
     * Initialize swipe profile system
     */
    init() {
        this.createSwipeInterface();
        this.loadProfiles();
        this.setupEventListeners();
    }
    
    /**
     * Create swipe interface
     */
    createSwipeInterface() {
        const swipeHTML = `
            <div class="swipe-container" id="swipe-container">
                <div class="swipe-header">
                    <div class="swipe-filters">
                        <button class="filter-btn active" data-filter="all">
                            <i class="fas fa-users"></i> All
                        </button>
                        <button class="filter-btn" data-filter="new">
                            <i class="fas fa-star"></i> New
                        </button>
                        <button class="filter-btn" data-filter="online">
                            <i class="fas fa-circle text-success"></i> Online
                        </button>
                        <button class="filter-btn" data-filter="premium">
                            <i class="fas fa-crown"></i> Premium
                        </button>
                    </div>
                    <div class="swipe-actions">
                        <button class="action-btn settings-btn" title="Settings">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="action-btn list-view-btn" title="List View">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
                
                <div class="swipe-cards-container" id="swipe-cards">
                    <!-- Profile cards will be inserted here -->
                </div>
                
                <div class="swipe-controls">
                    <button class="swipe-btn reject-btn" id="reject-btn" title="Pass">
                        <i class="fas fa-times"></i>
                    </button>
                    <button class="swipe-btn super-like-btn" id="super-like-btn" title="Super Like">
                        <i class="fas fa-star"></i>
                    </button>
                    <button class="swipe-btn like-btn" id="like-btn" title="Like">
                        <i class="fas fa-heart"></i>
                    </button>
                    <button class="swipe-btn video-call-btn" id="video-call-btn" title="Video Call">
                        <i class="fas fa-video"></i>
                    </button>
                </div>
                
                <div class="swipe-indicators">
                    <div class="indicator reject-indicator">
                        <i class="fas fa-times"></i>
                        <span>PASS</span>
                    </div>
                    <div class="indicator like-indicator">
                        <i class="fas fa-heart"></i>
                        <span>LIKE</span>
                    </div>
                    <div class="indicator super-like-indicator">
                        <i class="fas fa-star"></i>
                        <span>SUPER LIKE</span>
                    </div>
                </div>
                
                <div class="loading-spinner" id="loading-spinner" style="display: none;">
                    <div class="spinner"></div>
                    <p>Loading more profiles...</p>
                </div>
                
                <div class="no-more-profiles" id="no-more-profiles" style="display: none;">
                    <div class="no-profiles-content">
                        <i class="fas fa-heart-broken"></i>
                        <h3>No More Profiles</h3>
                        <p>You've seen all available profiles. Try adjusting your search criteria.</p>
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-refresh"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Find container or create one
        let container = document.getElementById('swipe-profile-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'swipe-profile-container';
            document.body.appendChild(container);
        }
        
        container.innerHTML = swipeHTML;
        this.swipeContainer = document.getElementById('swipe-container');
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.filterProfiles(e.target.dataset.filter);
            });
        });
        
        // Control buttons
        document.getElementById('reject-btn').addEventListener('click', () => this.rejectProfile());
        document.getElementById('like-btn').addEventListener('click', () => this.likeProfile());
        document.getElementById('super-like-btn').addEventListener('click', () => this.superLikeProfile());
        document.getElementById('video-call-btn').addEventListener('click', () => this.initiateVideoCall());
        
        // Settings and view toggle
        document.querySelector('.settings-btn').addEventListener('click', () => this.openSettings());
        document.querySelector('.list-view-btn').addEventListener('click', () => this.toggleListView());
        
        // Touch events for mobile
        this.setupTouchEvents();
        
        // Keyboard shortcuts
        this.setupKeyboardShortcuts();
    }
    
    /**
     * Setup touch events for swiping
     */
    setupTouchEvents() {
        const cardsContainer = document.getElementById('swipe-cards');
        
        cardsContainer.addEventListener('touchstart', (e) => {
            this.handleTouchStart(e);
        }, { passive: true });
        
        cardsContainer.addEventListener('touchmove', (e) => {
            this.handleTouchMove(e);
        }, { passive: false });
        
        cardsContainer.addEventListener('touchend', (e) => {
            this.handleTouchEnd(e);
        }, { passive: true });
        
        // Mouse events for desktop
        cardsContainer.addEventListener('mousedown', (e) => {
            this.handleMouseDown(e);
        });
        
        cardsContainer.addEventListener('mousemove', (e) => {
            this.handleMouseMove(e);
        });
        
        cardsContainer.addEventListener('mouseup', (e) => {
            this.handleMouseUp(e);
        });
        
        cardsContainer.addEventListener('mouseleave', (e) => {
            this.handleMouseUp(e);
        });
    }
    
    /**
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
            
            switch (e.key) {
                case 'ArrowLeft':
                case 'x':
                case 'X':
                    e.preventDefault();
                    this.rejectProfile();
                    break;
                case 'ArrowRight':
                case 'l':
                case 'L':
                    e.preventDefault();
                    this.likeProfile();
                    break;
                case 'ArrowUp':
                case 's':
                case 'S':
                    e.preventDefault();
                    this.superLikeProfile();
                    break;
                case 'v':
                case 'V':
                    e.preventDefault();
                    this.initiateVideoCall();
                    break;
                case 'Escape':
                    this.closeProfileDetails();
                    break;
            }
        });
    }
    
    /**
     * Load profiles from API
     */
    async loadProfiles() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);
        
        try {
            const response = await fetch('api/get_swipe_profiles.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    limit: 10,
                    offset: this.profiles.length,
                    filter: this.getCurrentFilter()
                })
            });
            
            const result = await response.json();
            
            if (result.success && result.data.profiles.length > 0) {
                this.profiles = [...this.profiles, ...result.data.profiles];
                this.renderProfileCards();
            } else if (this.profiles.length === 0) {
                this.showNoMoreProfiles();
            }
            
        } catch (error) {
            console.error('Error loading profiles:', error);
            this.showError('Failed to load profiles. Please try again.');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }
    
    /**
     * Render profile cards
     */
    renderProfileCards() {
        const cardsContainer = document.getElementById('swipe-cards');
        
        // Clear existing cards
        cardsContainer.innerHTML = '';
        this.profileCards = [];
        
        // Render up to 3 cards for smooth swiping
        const cardsToRender = Math.min(3, this.profiles.length - this.currentProfileIndex);
        
        for (let i = 0; i < cardsToRender; i++) {
            const profileIndex = this.currentProfileIndex + i;
            const profile = this.profiles[profileIndex];
            
            if (profile) {
                const card = this.createProfileCard(profile, i);
                cardsContainer.appendChild(card);
                this.profileCards.push(card);
            }
        }
        
        // Set current card
        this.currentCard = this.profileCards[0];
    }
    
    /**
     * Create profile card element
     */
    createProfileCard(profile, zIndex) {
        const card = document.createElement('div');
        card.className = 'profile-card';
        card.style.zIndex = 10 - zIndex;
        card.dataset.profileId = profile.id;
        
        const age = this.calculateAge(profile.dob);
        const isOnline = profile.last_seen && this.isRecentlyOnline(profile.last_seen);
        const isPremium = profile.status === 'Paid';
        
        card.innerHTML = `
            <div class="card-image-container">
                <img src="my_photos/${profile.photo1 || 'default-avatar.png'}" 
                     alt="${profile.username}" class="card-image"
                     onerror="this.src='img/default-avatar.png'">
                <div class="card-badges">
                    ${isOnline ? '<span class="badge online-badge"><i class="fas fa-circle"></i> Online</span>' : ''}
                    ${isPremium ? '<span class="badge premium-badge"><i class="fas fa-crown"></i> Premium</span>' : ''}
                    ${profile.is_verified ? '<span class="badge verified-badge"><i class="fas fa-check-circle"></i> Verified</span>' : ''}
                </div>
                <div class="card-gradient"></div>
            </div>
            
            <div class="card-content">
                <div class="card-header">
                    <h3 class="card-name">${profile.username}, ${age}</h3>
                    <div class="card-location">
                        <i class="fas fa-map-marker-alt"></i>
                        ${profile.city || 'Location not specified'}
                    </div>
                </div>
                
                <div class="card-details">
                    <div class="detail-item">
                        <i class="fas fa-graduation-cap"></i>
                        <span>${profile.education || 'Education not specified'}</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-briefcase"></i>
                        <span>${profile.occupation || 'Occupation not specified'}</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-pray"></i>
                        <span>${profile.religion || 'Religion not specified'}</span>
                    </div>
                    ${profile.height ? `
                    <div class="detail-item">
                        <i class="fas fa-ruler-vertical"></i>
                        <span>${profile.height}</span>
                    </div>
                    ` : ''}
                </div>
                
                <div class="card-actions">
                    <button class="card-action-btn info-btn" onclick="swipeProfileManager.showProfileDetails('${profile.id}')">
                        <i class="fas fa-info-circle"></i>
                    </button>
                    <button class="card-action-btn message-btn" onclick="swipeProfileManager.sendMessage('${profile.id}')">
                        <i class="fas fa-comment"></i>
                    </button>
                    <button class="card-action-btn gallery-btn" onclick="swipeProfileManager.showGallery('${profile.id}')">
                        <i class="fas fa-images"></i>
                    </button>
                </div>
            </div>
        `;
        
        return card;
    }
    
    /**
     * Handle touch start
     */
    handleTouchStart(e) {
        if (!this.currentCard) return;
        
        this.touchStartX = e.touches[0].clientX;
        this.touchStartY = e.touches[0].clientY;
        this.isDragging = true;
        
        this.currentCard.style.transition = 'none';
    }
    
    /**
     * Handle touch move
     */
    handleTouchMove(e) {
        if (!this.isDragging || !this.currentCard) return;
        
        e.preventDefault();
        
        const touchX = e.touches[0].clientX;
        const touchY = e.touches[0].clientY;
        
        const deltaX = touchX - this.touchStartX;
        const deltaY = touchY - this.touchStartY;
        
        const rotation = deltaX * this.rotationFactor;
        
        this.currentCard.style.transform = `translateX(${deltaX}px) translateY(${deltaY}px) rotate(${rotation}deg)`;
        
        // Show indicators
        this.updateSwipeIndicators(deltaX);
    }
    
    /**
     * Handle touch end
     */
    handleTouchEnd(e) {
        if (!this.isDragging || !this.currentCard) return;
        
        this.isDragging = false;
        
        const touchX = e.changedTouches[0].clientX;
        const deltaX = touchX - this.touchStartX;
        
        this.currentCard.style.transition = 'transform 0.3s ease';
        
        if (Math.abs(deltaX) > this.swipeThreshold) {
            if (deltaX > 0) {
                this.likeProfile();
            } else {
                this.rejectProfile();
            }
        } else {
            // Snap back to center
            this.currentCard.style.transform = 'translateX(0) translateY(0) rotate(0deg)';
            this.hideSwipeIndicators();
        }
    }
    
    /**
     * Handle mouse events (desktop)
     */
    handleMouseDown(e) {
        this.touchStartX = e.clientX;
        this.touchStartY = e.clientY;
        this.isDragging = true;
        
        if (this.currentCard) {
            this.currentCard.style.transition = 'none';
        }
    }
    
    handleMouseMove(e) {
        if (!this.isDragging || !this.currentCard) return;
        
        const deltaX = e.clientX - this.touchStartX;
        const deltaY = e.clientY - this.touchStartY;
        const rotation = deltaX * this.rotationFactor;
        
        this.currentCard.style.transform = `translateX(${deltaX}px) translateY(${deltaY}px) rotate(${rotation}deg)`;
        this.updateSwipeIndicators(deltaX);
    }
    
    handleMouseUp(e) {
        if (!this.isDragging || !this.currentCard) return;
        
        this.isDragging = false;
        
        const deltaX = e.clientX - this.touchStartX;
        
        this.currentCard.style.transition = 'transform 0.3s ease';
        
        if (Math.abs(deltaX) > this.swipeThreshold) {
            if (deltaX > 0) {
                this.likeProfile();
            } else {
                this.rejectProfile();
            }
        } else {
            this.currentCard.style.transform = 'translateX(0) translateY(0) rotate(0deg)';
            this.hideSwipeIndicators();
        }
    }
    
    /**
     * Update swipe indicators
     */
    updateSwipeIndicators(deltaX) {
        const indicators = document.querySelectorAll('.indicator');
        indicators.forEach(indicator => indicator.classList.remove('active'));
        
        if (Math.abs(deltaX) > this.swipeThreshold / 2) {
            if (deltaX > 0) {
                document.querySelector('.like-indicator').classList.add('active');
            } else {
                document.querySelector('.reject-indicator').classList.add('active');
            }
        }
    }
    
    /**
     * Hide swipe indicators
     */
    hideSwipeIndicators() {
        document.querySelectorAll('.indicator').forEach(indicator => {
            indicator.classList.remove('active');
        });
    }
    
    /**
     * Like profile
     */
    async likeProfile() {
        if (!this.currentCard) return;
        
        const profileId = this.currentCard.dataset.profileId;
        
        // Animate card out
        this.animateCardOut(this.currentCard, 'right');
        
        // Send like to server
        try {
            await fetch('api/send_intrest.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    to_id: profileId,
                    interest_type: 'like'
                })
            });
            
            this.showFeedback('Liked!', 'success');
        } catch (error) {
            console.error('Error sending like:', error);
        }
        
        this.nextProfile();
    }
    
    /**
     * Reject profile
     */
    async rejectProfile() {
        if (!this.currentCard) return;
        
        const profileId = this.currentCard.dataset.profileId;
        
        // Animate card out
        this.animateCardOut(this.currentCard, 'left');
        
        // Track rejection (optional)
        try {
            await fetch('api/track_profile_action.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    profile_id: profileId,
                    action: 'reject'
                })
            });
        } catch (error) {
            console.error('Error tracking rejection:', error);
        }
        
        this.nextProfile();
    }
    
    /**
     * Super like profile
     */
    async superLikeProfile() {
        if (!this.currentCard) return;
        
        const profileId = this.currentCard.dataset.profileId;
        
        // Animate card out (upward)
        this.animateCardOut(this.currentCard, 'up');
        
        // Send super like to server
        try {
            await fetch('api/send_intrest.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    to_id: profileId,
                    interest_type: 'super_like'
                })
            });
            
            this.showFeedback('Super Liked!', 'super');
        } catch (error) {
            console.error('Error sending super like:', error);
        }
        
        this.nextProfile();
    }
    
    /**
     * Initiate video call
     */
    initiateVideoCall() {
        if (!this.currentCard) return;
        
        const profileId = this.currentCard.dataset.profileId;
        
        if (window.videoCallManager) {
            window.videoCallManager.initiateCall(profileId);
        } else {
            this.showError('Video calling not available');
        }
    }
    
    /**
     * Animate card out
     */
    animateCardOut(card, direction) {
        let transform = '';
        
        switch (direction) {
            case 'left':
                transform = 'translateX(-100vw) rotate(-30deg)';
                break;
            case 'right':
                transform = 'translateX(100vw) rotate(30deg)';
                break;
            case 'up':
                transform = 'translateY(-100vh) scale(0.8)';
                break;
        }
        
        card.style.transform = transform;
        card.style.opacity = '0';
        
        setTimeout(() => {
            card.remove();
        }, 300);
    }
    
    /**
     * Move to next profile
     */
    nextProfile() {
        this.currentProfileIndex++;
        
        // Remove current card from array
        this.profileCards.shift();
        
        // Update current card
        this.currentCard = this.profileCards[0];
        
        // Load more profiles if needed
        if (this.currentProfileIndex >= this.profiles.length - 2) {
            this.loadProfiles();
        }
        
        // Add new card if available
        if (this.profiles.length > this.currentProfileIndex + 2) {
            const newProfile = this.profiles[this.currentProfileIndex + 2];
            const newCard = this.createProfileCard(newProfile, 2);
            document.getElementById('swipe-cards').appendChild(newCard);
            this.profileCards.push(newCard);
        }
        
        // Check if no more profiles
        if (!this.currentCard && this.profiles.length <= this.currentProfileIndex) {
            this.showNoMoreProfiles();
        }
    }
    
    /**
     * Utility functions
     */
    calculateAge(dob) {
        if (!dob) return 'N/A';
        const today = new Date();
        const birthDate = new Date(dob);
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        return age;
    }
    
    isRecentlyOnline(lastSeen) {
        const now = new Date();
        const lastSeenDate = new Date(lastSeen);
        const diffMinutes = (now - lastSeenDate) / (1000 * 60);
        return diffMinutes < 30; // Online if seen within 30 minutes
    }
    
    getCurrentFilter() {
        const activeFilter = document.querySelector('.filter-btn.active');
        return activeFilter ? activeFilter.dataset.filter : 'all';
    }
    
    showLoading(show) {
        const spinner = document.getElementById('loading-spinner');
        if (spinner) {
            spinner.style.display = show ? 'flex' : 'none';
        }
    }
    
    showNoMoreProfiles() {
        const noMoreDiv = document.getElementById('no-more-profiles');
        if (noMoreDiv) {
            noMoreDiv.style.display = 'flex';
        }
    }
    
    showFeedback(message, type) {
        const feedback = document.createElement('div');
        feedback.className = `swipe-feedback ${type}`;
        feedback.textContent = message;
        
        document.body.appendChild(feedback);
        
        setTimeout(() => {
            feedback.classList.add('show');
        }, 10);
        
        setTimeout(() => {
            feedback.remove();
        }, 2000);
    }
    
    showError(message) {
        console.error(message);
        alert(message); // Replace with your preferred notification system
    }
    
    // Additional methods for profile interactions
    showProfileDetails(profileId) {
        window.open(`profile.php?user_id=${profileId}`, '_blank');
    }
    
    sendMessage(profileId) {
        window.open(`chat.php?user_id=${profileId}`, '_blank');
    }
    
    showGallery(profileId) {
        window.open(`gallery.php?user_id=${profileId}`, '_blank');
    }
    
    filterProfiles(filter) {
        this.currentProfileIndex = 0;
        this.profiles = [];
        this.loadProfiles();
    }
    
    openSettings() {
        // Implementation for settings modal
        console.log('Opening settings...');
    }
    
    toggleListView() {
        // Switch to traditional list view
        window.location.href = 'search.php';
    }
    
    closeProfileDetails() {
        // Close any open modals or details
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            $(modal).modal('hide');
        });
    }
}

// Initialize swipe profile manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('swipe-profile-container') || 
        window.location.pathname.includes('swipe-profiles')) {
        window.swipeProfileManager = new SwipeProfileManager();
    }
});
