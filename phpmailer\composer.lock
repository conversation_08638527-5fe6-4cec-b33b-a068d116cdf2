{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "85e07ea1e3620c3da1942492052d7d21", "packages": [{"name": "phpmailer/phpmailer", "version": "v6.1.4", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "c5e61d0729507049cec9673aa1a679f9adefd683"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/c5e61d0729507049cec9673aa1a679f9adefd683", "reference": "c5e61d0729507049cec9673aa1a679f9adefd683", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "php": ">=5.5.0"}, "require-dev": {"doctrine/annotations": "^1.2", "friendsofphp/php-cs-fixer": "^2.2", "phpunit/phpunit": "^4.8 || ^5.7"}, "suggest": {"ext-mbstring": "Needed to send email in multibyte encoding charset", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "stevenmaguire/oauth2-microsoft": "Needed for Microsoft XOAUTH2 authentication", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)"}, "type": "library", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "time": "2019-12-10T11:17:38+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": []}