<?php
include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();
$getHeightQry = $DatabaseCo->dbLink->query("SELECT * FROM `height`");
if(mysqli_num_rows($getHeightQry) > 0){
	$count=0;
	while($contact_res = mysqli_fetch_object($getHeightQry)){
		$count++;
		$response = array('id' => $contact_res->id,'height' => $contact_res->height,'status'=>"1");
		$data["response"][] = $response; 
	}
	echo json_encode($data);
	exit;
}else{
	$data['status'] = "0";
	echo json_encode($data);
	exit;
}
?>
