$this->fonts[$font]=array (
  'FontName' => 'Times-BoldItalic',
  'FullName' => 'Times Bold Italic',
  'FamilyName' => 'Times',
  'Weight' => 'Bold',
  'ItalicAngle' => '-15',
  'IsFixedPitch' => 'false',
  'CharacterSet' => 'ExtendedRoman',
  'FontBBox' => 
  array (
    0 => '-200',
    1 => '-218',
    2 => '996',
    3 => '921',
  ),
  'UnderlinePosition' => '-100',
  'UnderlineThickness' => '50',
  'Version' => '002.000',
  'EncodingScheme' => 'AdobeStandardEncoding',
  'CapHeight' => '669',
  'XHeight' => '462',
  'Ascender' => '683',
  'Descender' => '-217',
  'StdHW' => '42',
  'StdVW' => '121',
  'StartCharMetrics' => '315',
  'C' => 
  array (
    32 => 
    array (
      'C' => '32',
      'WX' => '250',
      'N' => 'space',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
    'space' => 
    array (
      'C' => '32',
      'WX' => '250',
      'N' => 'space',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
    33 => 
    array (
      'C' => '33',
      'WX' => '389',
      'N' => 'exclam',
      'B' => 
      array (
        0 => '67',
        1 => '-13',
        2 => '370',
        3 => '684',
      ),
    ),
    'exclam' => 
    array (
      'C' => '33',
      'WX' => '389',
      'N' => 'exclam',
      'B' => 
      array (
        0 => '67',
        1 => '-13',
        2 => '370',
        3 => '684',
      ),
    ),
    34 => 
    array (
      'C' => '34',
      'WX' => '555',
      'N' => 'quotedbl',
      'B' => 
      array (
        0 => '136',
        1 => '398',
        2 => '536',
        3 => '685',
      ),
    ),
    'quotedbl' => 
    array (
      'C' => '34',
      'WX' => '555',
      'N' => 'quotedbl',
      'B' => 
      array (
        0 => '136',
        1 => '398',
        2 => '536',
        3 => '685',
      ),
    ),
    35 => 
    array (
      'C' => '35',
      'WX' => '500',
      'N' => 'numbersign',
      'B' => 
      array (
        0 => '-33',
        1 => '0',
        2 => '533',
        3 => '700',
      ),
    ),
    'numbersign' => 
    array (
      'C' => '35',
      'WX' => '500',
      'N' => 'numbersign',
      'B' => 
      array (
        0 => '-33',
        1 => '0',
        2 => '533',
        3 => '700',
      ),
    ),
    36 => 
    array (
      'C' => '36',
      'WX' => '500',
      'N' => 'dollar',
      'B' => 
      array (
        0 => '-20',
        1 => '-100',
        2 => '497',
        3 => '733',
      ),
    ),
    'dollar' => 
    array (
      'C' => '36',
      'WX' => '500',
      'N' => 'dollar',
      'B' => 
      array (
        0 => '-20',
        1 => '-100',
        2 => '497',
        3 => '733',
      ),
    ),
    37 => 
    array (
      'C' => '37',
      'WX' => '833',
      'N' => 'percent',
      'B' => 
      array (
        0 => '39',
        1 => '-10',
        2 => '793',
        3 => '692',
      ),
    ),
    'percent' => 
    array (
      'C' => '37',
      'WX' => '833',
      'N' => 'percent',
      'B' => 
      array (
        0 => '39',
        1 => '-10',
        2 => '793',
        3 => '692',
      ),
    ),
    38 => 
    array (
      'C' => '38',
      'WX' => '778',
      'N' => 'ampersand',
      'B' => 
      array (
        0 => '5',
        1 => '-19',
        2 => '699',
        3 => '682',
      ),
    ),
    'ampersand' => 
    array (
      'C' => '38',
      'WX' => '778',
      'N' => 'ampersand',
      'B' => 
      array (
        0 => '5',
        1 => '-19',
        2 => '699',
        3 => '682',
      ),
    ),
    39 => 
    array (
      'C' => '39',
      'WX' => '333',
      'N' => 'quoteright',
      'B' => 
      array (
        0 => '98',
        1 => '369',
        2 => '302',
        3 => '685',
      ),
    ),
    'quoteright' => 
    array (
      'C' => '39',
      'WX' => '333',
      'N' => 'quoteright',
      'B' => 
      array (
        0 => '98',
        1 => '369',
        2 => '302',
        3 => '685',
      ),
    ),
    40 => 
    array (
      'C' => '40',
      'WX' => '333',
      'N' => 'parenleft',
      'B' => 
      array (
        0 => '28',
        1 => '-179',
        2 => '344',
        3 => '685',
      ),
    ),
    'parenleft' => 
    array (
      'C' => '40',
      'WX' => '333',
      'N' => 'parenleft',
      'B' => 
      array (
        0 => '28',
        1 => '-179',
        2 => '344',
        3 => '685',
      ),
    ),
    41 => 
    array (
      'C' => '41',
      'WX' => '333',
      'N' => 'parenright',
      'B' => 
      array (
        0 => '-44',
        1 => '-179',
        2 => '271',
        3 => '685',
      ),
    ),
    'parenright' => 
    array (
      'C' => '41',
      'WX' => '333',
      'N' => 'parenright',
      'B' => 
      array (
        0 => '-44',
        1 => '-179',
        2 => '271',
        3 => '685',
      ),
    ),
    42 => 
    array (
      'C' => '42',
      'WX' => '500',
      'N' => 'asterisk',
      'B' => 
      array (
        0 => '65',
        1 => '249',
        2 => '456',
        3 => '685',
      ),
    ),
    'asterisk' => 
    array (
      'C' => '42',
      'WX' => '500',
      'N' => 'asterisk',
      'B' => 
      array (
        0 => '65',
        1 => '249',
        2 => '456',
        3 => '685',
      ),
    ),
    43 => 
    array (
      'C' => '43',
      'WX' => '570',
      'N' => 'plus',
      'B' => 
      array (
        0 => '33',
        1 => '0',
        2 => '537',
        3 => '506',
      ),
    ),
    'plus' => 
    array (
      'C' => '43',
      'WX' => '570',
      'N' => 'plus',
      'B' => 
      array (
        0 => '33',
        1 => '0',
        2 => '537',
        3 => '506',
      ),
    ),
    44 => 
    array (
      'C' => '44',
      'WX' => '250',
      'N' => 'comma',
      'B' => 
      array (
        0 => '-60',
        1 => '-182',
        2 => '144',
        3 => '134',
      ),
    ),
    'comma' => 
    array (
      'C' => '44',
      'WX' => '250',
      'N' => 'comma',
      'B' => 
      array (
        0 => '-60',
        1 => '-182',
        2 => '144',
        3 => '134',
      ),
    ),
    45 => 
    array (
      'C' => '45',
      'WX' => '333',
      'N' => 'hyphen',
      'B' => 
      array (
        0 => '2',
        1 => '166',
        2 => '271',
        3 => '282',
      ),
    ),
    'hyphen' => 
    array (
      'C' => '45',
      'WX' => '333',
      'N' => 'hyphen',
      'B' => 
      array (
        0 => '2',
        1 => '166',
        2 => '271',
        3 => '282',
      ),
    ),
    46 => 
    array (
      'C' => '46',
      'WX' => '250',
      'N' => 'period',
      'B' => 
      array (
        0 => '-9',
        1 => '-13',
        2 => '139',
        3 => '135',
      ),
    ),
    'period' => 
    array (
      'C' => '46',
      'WX' => '250',
      'N' => 'period',
      'B' => 
      array (
        0 => '-9',
        1 => '-13',
        2 => '139',
        3 => '135',
      ),
    ),
    47 => 
    array (
      'C' => '47',
      'WX' => '278',
      'N' => 'slash',
      'B' => 
      array (
        0 => '-64',
        1 => '-18',
        2 => '342',
        3 => '685',
      ),
    ),
    'slash' => 
    array (
      'C' => '47',
      'WX' => '278',
      'N' => 'slash',
      'B' => 
      array (
        0 => '-64',
        1 => '-18',
        2 => '342',
        3 => '685',
      ),
    ),
    48 => 
    array (
      'C' => '48',
      'WX' => '500',
      'N' => 'zero',
      'B' => 
      array (
        0 => '17',
        1 => '-14',
        2 => '477',
        3 => '683',
      ),
    ),
    'zero' => 
    array (
      'C' => '48',
      'WX' => '500',
      'N' => 'zero',
      'B' => 
      array (
        0 => '17',
        1 => '-14',
        2 => '477',
        3 => '683',
      ),
    ),
    49 => 
    array (
      'C' => '49',
      'WX' => '500',
      'N' => 'one',
      'B' => 
      array (
        0 => '5',
        1 => '0',
        2 => '419',
        3 => '683',
      ),
    ),
    'one' => 
    array (
      'C' => '49',
      'WX' => '500',
      'N' => 'one',
      'B' => 
      array (
        0 => '5',
        1 => '0',
        2 => '419',
        3 => '683',
      ),
    ),
    50 => 
    array (
      'C' => '50',
      'WX' => '500',
      'N' => 'two',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '446',
        3 => '683',
      ),
    ),
    'two' => 
    array (
      'C' => '50',
      'WX' => '500',
      'N' => 'two',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '446',
        3 => '683',
      ),
    ),
    51 => 
    array (
      'C' => '51',
      'WX' => '500',
      'N' => 'three',
      'B' => 
      array (
        0 => '-15',
        1 => '-13',
        2 => '450',
        3 => '683',
      ),
    ),
    'three' => 
    array (
      'C' => '51',
      'WX' => '500',
      'N' => 'three',
      'B' => 
      array (
        0 => '-15',
        1 => '-13',
        2 => '450',
        3 => '683',
      ),
    ),
    52 => 
    array (
      'C' => '52',
      'WX' => '500',
      'N' => 'four',
      'B' => 
      array (
        0 => '-15',
        1 => '0',
        2 => '503',
        3 => '683',
      ),
    ),
    'four' => 
    array (
      'C' => '52',
      'WX' => '500',
      'N' => 'four',
      'B' => 
      array (
        0 => '-15',
        1 => '0',
        2 => '503',
        3 => '683',
      ),
    ),
    53 => 
    array (
      'C' => '53',
      'WX' => '500',
      'N' => 'five',
      'B' => 
      array (
        0 => '-11',
        1 => '-13',
        2 => '487',
        3 => '669',
      ),
    ),
    'five' => 
    array (
      'C' => '53',
      'WX' => '500',
      'N' => 'five',
      'B' => 
      array (
        0 => '-11',
        1 => '-13',
        2 => '487',
        3 => '669',
      ),
    ),
    54 => 
    array (
      'C' => '54',
      'WX' => '500',
      'N' => 'six',
      'B' => 
      array (
        0 => '23',
        1 => '-15',
        2 => '509',
        3 => '679',
      ),
    ),
    'six' => 
    array (
      'C' => '54',
      'WX' => '500',
      'N' => 'six',
      'B' => 
      array (
        0 => '23',
        1 => '-15',
        2 => '509',
        3 => '679',
      ),
    ),
    55 => 
    array (
      'C' => '55',
      'WX' => '500',
      'N' => 'seven',
      'B' => 
      array (
        0 => '52',
        1 => '0',
        2 => '525',
        3 => '669',
      ),
    ),
    'seven' => 
    array (
      'C' => '55',
      'WX' => '500',
      'N' => 'seven',
      'B' => 
      array (
        0 => '52',
        1 => '0',
        2 => '525',
        3 => '669',
      ),
    ),
    56 => 
    array (
      'C' => '56',
      'WX' => '500',
      'N' => 'eight',
      'B' => 
      array (
        0 => '3',
        1 => '-13',
        2 => '476',
        3 => '683',
      ),
    ),
    'eight' => 
    array (
      'C' => '56',
      'WX' => '500',
      'N' => 'eight',
      'B' => 
      array (
        0 => '3',
        1 => '-13',
        2 => '476',
        3 => '683',
      ),
    ),
    57 => 
    array (
      'C' => '57',
      'WX' => '500',
      'N' => 'nine',
      'B' => 
      array (
        0 => '-12',
        1 => '-10',
        2 => '475',
        3 => '683',
      ),
    ),
    'nine' => 
    array (
      'C' => '57',
      'WX' => '500',
      'N' => 'nine',
      'B' => 
      array (
        0 => '-12',
        1 => '-10',
        2 => '475',
        3 => '683',
      ),
    ),
    58 => 
    array (
      'C' => '58',
      'WX' => '333',
      'N' => 'colon',
      'B' => 
      array (
        0 => '23',
        1 => '-13',
        2 => '264',
        3 => '459',
      ),
    ),
    'colon' => 
    array (
      'C' => '58',
      'WX' => '333',
      'N' => 'colon',
      'B' => 
      array (
        0 => '23',
        1 => '-13',
        2 => '264',
        3 => '459',
      ),
    ),
    59 => 
    array (
      'C' => '59',
      'WX' => '333',
      'N' => 'semicolon',
      'B' => 
      array (
        0 => '-25',
        1 => '-183',
        2 => '264',
        3 => '459',
      ),
    ),
    'semicolon' => 
    array (
      'C' => '59',
      'WX' => '333',
      'N' => 'semicolon',
      'B' => 
      array (
        0 => '-25',
        1 => '-183',
        2 => '264',
        3 => '459',
      ),
    ),
    60 => 
    array (
      'C' => '60',
      'WX' => '570',
      'N' => 'less',
      'B' => 
      array (
        0 => '31',
        1 => '-8',
        2 => '539',
        3 => '514',
      ),
    ),
    'less' => 
    array (
      'C' => '60',
      'WX' => '570',
      'N' => 'less',
      'B' => 
      array (
        0 => '31',
        1 => '-8',
        2 => '539',
        3 => '514',
      ),
    ),
    61 => 
    array (
      'C' => '61',
      'WX' => '570',
      'N' => 'equal',
      'B' => 
      array (
        0 => '33',
        1 => '107',
        2 => '537',
        3 => '399',
      ),
    ),
    'equal' => 
    array (
      'C' => '61',
      'WX' => '570',
      'N' => 'equal',
      'B' => 
      array (
        0 => '33',
        1 => '107',
        2 => '537',
        3 => '399',
      ),
    ),
    62 => 
    array (
      'C' => '62',
      'WX' => '570',
      'N' => 'greater',
      'B' => 
      array (
        0 => '31',
        1 => '-8',
        2 => '539',
        3 => '514',
      ),
    ),
    'greater' => 
    array (
      'C' => '62',
      'WX' => '570',
      'N' => 'greater',
      'B' => 
      array (
        0 => '31',
        1 => '-8',
        2 => '539',
        3 => '514',
      ),
    ),
    63 => 
    array (
      'C' => '63',
      'WX' => '500',
      'N' => 'question',
      'B' => 
      array (
        0 => '79',
        1 => '-13',
        2 => '470',
        3 => '684',
      ),
    ),
    'question' => 
    array (
      'C' => '63',
      'WX' => '500',
      'N' => 'question',
      'B' => 
      array (
        0 => '79',
        1 => '-13',
        2 => '470',
        3 => '684',
      ),
    ),
    64 => 
    array (
      'C' => '64',
      'WX' => '832',
      'N' => 'at',
      'B' => 
      array (
        0 => '63',
        1 => '-18',
        2 => '770',
        3 => '685',
      ),
    ),
    'at' => 
    array (
      'C' => '64',
      'WX' => '832',
      'N' => 'at',
      'B' => 
      array (
        0 => '63',
        1 => '-18',
        2 => '770',
        3 => '685',
      ),
    ),
    65 => 
    array (
      'C' => '65',
      'WX' => '667',
      'N' => 'A',
      'B' => 
      array (
        0 => '-67',
        1 => '0',
        2 => '593',
        3 => '683',
      ),
    ),
    'A' => 
    array (
      'C' => '65',
      'WX' => '667',
      'N' => 'A',
      'B' => 
      array (
        0 => '-67',
        1 => '0',
        2 => '593',
        3 => '683',
      ),
    ),
    66 => 
    array (
      'C' => '66',
      'WX' => '667',
      'N' => 'B',
      'B' => 
      array (
        0 => '-24',
        1 => '0',
        2 => '624',
        3 => '669',
      ),
    ),
    'B' => 
    array (
      'C' => '66',
      'WX' => '667',
      'N' => 'B',
      'B' => 
      array (
        0 => '-24',
        1 => '0',
        2 => '624',
        3 => '669',
      ),
    ),
    67 => 
    array (
      'C' => '67',
      'WX' => '667',
      'N' => 'C',
      'B' => 
      array (
        0 => '32',
        1 => '-18',
        2 => '677',
        3 => '685',
      ),
    ),
    'C' => 
    array (
      'C' => '67',
      'WX' => '667',
      'N' => 'C',
      'B' => 
      array (
        0 => '32',
        1 => '-18',
        2 => '677',
        3 => '685',
      ),
    ),
    68 => 
    array (
      'C' => '68',
      'WX' => '722',
      'N' => 'D',
      'B' => 
      array (
        0 => '-46',
        1 => '0',
        2 => '685',
        3 => '669',
      ),
    ),
    'D' => 
    array (
      'C' => '68',
      'WX' => '722',
      'N' => 'D',
      'B' => 
      array (
        0 => '-46',
        1 => '0',
        2 => '685',
        3 => '669',
      ),
    ),
    69 => 
    array (
      'C' => '69',
      'WX' => '667',
      'N' => 'E',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '653',
        3 => '669',
      ),
    ),
    'E' => 
    array (
      'C' => '69',
      'WX' => '667',
      'N' => 'E',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '653',
        3 => '669',
      ),
    ),
    70 => 
    array (
      'C' => '70',
      'WX' => '667',
      'N' => 'F',
      'B' => 
      array (
        0 => '-13',
        1 => '0',
        2 => '660',
        3 => '669',
      ),
    ),
    'F' => 
    array (
      'C' => '70',
      'WX' => '667',
      'N' => 'F',
      'B' => 
      array (
        0 => '-13',
        1 => '0',
        2 => '660',
        3 => '669',
      ),
    ),
    71 => 
    array (
      'C' => '71',
      'WX' => '722',
      'N' => 'G',
      'B' => 
      array (
        0 => '21',
        1 => '-18',
        2 => '706',
        3 => '685',
      ),
    ),
    'G' => 
    array (
      'C' => '71',
      'WX' => '722',
      'N' => 'G',
      'B' => 
      array (
        0 => '21',
        1 => '-18',
        2 => '706',
        3 => '685',
      ),
    ),
    72 => 
    array (
      'C' => '72',
      'WX' => '778',
      'N' => 'H',
      'B' => 
      array (
        0 => '-24',
        1 => '0',
        2 => '799',
        3 => '669',
      ),
    ),
    'H' => 
    array (
      'C' => '72',
      'WX' => '778',
      'N' => 'H',
      'B' => 
      array (
        0 => '-24',
        1 => '0',
        2 => '799',
        3 => '669',
      ),
    ),
    73 => 
    array (
      'C' => '73',
      'WX' => '389',
      'N' => 'I',
      'B' => 
      array (
        0 => '-32',
        1 => '0',
        2 => '406',
        3 => '669',
      ),
    ),
    'I' => 
    array (
      'C' => '73',
      'WX' => '389',
      'N' => 'I',
      'B' => 
      array (
        0 => '-32',
        1 => '0',
        2 => '406',
        3 => '669',
      ),
    ),
    74 => 
    array (
      'C' => '74',
      'WX' => '500',
      'N' => 'J',
      'B' => 
      array (
        0 => '-46',
        1 => '-99',
        2 => '524',
        3 => '669',
      ),
    ),
    'J' => 
    array (
      'C' => '74',
      'WX' => '500',
      'N' => 'J',
      'B' => 
      array (
        0 => '-46',
        1 => '-99',
        2 => '524',
        3 => '669',
      ),
    ),
    75 => 
    array (
      'C' => '75',
      'WX' => '667',
      'N' => 'K',
      'B' => 
      array (
        0 => '-21',
        1 => '0',
        2 => '702',
        3 => '669',
      ),
    ),
    'K' => 
    array (
      'C' => '75',
      'WX' => '667',
      'N' => 'K',
      'B' => 
      array (
        0 => '-21',
        1 => '0',
        2 => '702',
        3 => '669',
      ),
    ),
    76 => 
    array (
      'C' => '76',
      'WX' => '611',
      'N' => 'L',
      'B' => 
      array (
        0 => '-22',
        1 => '0',
        2 => '590',
        3 => '669',
      ),
    ),
    'L' => 
    array (
      'C' => '76',
      'WX' => '611',
      'N' => 'L',
      'B' => 
      array (
        0 => '-22',
        1 => '0',
        2 => '590',
        3 => '669',
      ),
    ),
    77 => 
    array (
      'C' => '77',
      'WX' => '889',
      'N' => 'M',
      'B' => 
      array (
        0 => '-29',
        1 => '-12',
        2 => '917',
        3 => '669',
      ),
    ),
    'M' => 
    array (
      'C' => '77',
      'WX' => '889',
      'N' => 'M',
      'B' => 
      array (
        0 => '-29',
        1 => '-12',
        2 => '917',
        3 => '669',
      ),
    ),
    78 => 
    array (
      'C' => '78',
      'WX' => '722',
      'N' => 'N',
      'B' => 
      array (
        0 => '-27',
        1 => '-15',
        2 => '748',
        3 => '669',
      ),
    ),
    'N' => 
    array (
      'C' => '78',
      'WX' => '722',
      'N' => 'N',
      'B' => 
      array (
        0 => '-27',
        1 => '-15',
        2 => '748',
        3 => '669',
      ),
    ),
    79 => 
    array (
      'C' => '79',
      'WX' => '722',
      'N' => 'O',
      'B' => 
      array (
        0 => '27',
        1 => '-18',
        2 => '691',
        3 => '685',
      ),
    ),
    'O' => 
    array (
      'C' => '79',
      'WX' => '722',
      'N' => 'O',
      'B' => 
      array (
        0 => '27',
        1 => '-18',
        2 => '691',
        3 => '685',
      ),
    ),
    80 => 
    array (
      'C' => '80',
      'WX' => '611',
      'N' => 'P',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '613',
        3 => '669',
      ),
    ),
    'P' => 
    array (
      'C' => '80',
      'WX' => '611',
      'N' => 'P',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '613',
        3 => '669',
      ),
    ),
    81 => 
    array (
      'C' => '81',
      'WX' => '722',
      'N' => 'Q',
      'B' => 
      array (
        0 => '27',
        1 => '-208',
        2 => '691',
        3 => '685',
      ),
    ),
    'Q' => 
    array (
      'C' => '81',
      'WX' => '722',
      'N' => 'Q',
      'B' => 
      array (
        0 => '27',
        1 => '-208',
        2 => '691',
        3 => '685',
      ),
    ),
    82 => 
    array (
      'C' => '82',
      'WX' => '667',
      'N' => 'R',
      'B' => 
      array (
        0 => '-29',
        1 => '0',
        2 => '623',
        3 => '669',
      ),
    ),
    'R' => 
    array (
      'C' => '82',
      'WX' => '667',
      'N' => 'R',
      'B' => 
      array (
        0 => '-29',
        1 => '0',
        2 => '623',
        3 => '669',
      ),
    ),
    83 => 
    array (
      'C' => '83',
      'WX' => '556',
      'N' => 'S',
      'B' => 
      array (
        0 => '2',
        1 => '-18',
        2 => '526',
        3 => '685',
      ),
    ),
    'S' => 
    array (
      'C' => '83',
      'WX' => '556',
      'N' => 'S',
      'B' => 
      array (
        0 => '2',
        1 => '-18',
        2 => '526',
        3 => '685',
      ),
    ),
    84 => 
    array (
      'C' => '84',
      'WX' => '611',
      'N' => 'T',
      'B' => 
      array (
        0 => '50',
        1 => '0',
        2 => '650',
        3 => '669',
      ),
    ),
    'T' => 
    array (
      'C' => '84',
      'WX' => '611',
      'N' => 'T',
      'B' => 
      array (
        0 => '50',
        1 => '0',
        2 => '650',
        3 => '669',
      ),
    ),
    85 => 
    array (
      'C' => '85',
      'WX' => '722',
      'N' => 'U',
      'B' => 
      array (
        0 => '67',
        1 => '-18',
        2 => '744',
        3 => '669',
      ),
    ),
    'U' => 
    array (
      'C' => '85',
      'WX' => '722',
      'N' => 'U',
      'B' => 
      array (
        0 => '67',
        1 => '-18',
        2 => '744',
        3 => '669',
      ),
    ),
    86 => 
    array (
      'C' => '86',
      'WX' => '667',
      'N' => 'V',
      'B' => 
      array (
        0 => '65',
        1 => '-18',
        2 => '715',
        3 => '669',
      ),
    ),
    'V' => 
    array (
      'C' => '86',
      'WX' => '667',
      'N' => 'V',
      'B' => 
      array (
        0 => '65',
        1 => '-18',
        2 => '715',
        3 => '669',
      ),
    ),
    87 => 
    array (
      'C' => '87',
      'WX' => '889',
      'N' => 'W',
      'B' => 
      array (
        0 => '65',
        1 => '-18',
        2 => '940',
        3 => '669',
      ),
    ),
    'W' => 
    array (
      'C' => '87',
      'WX' => '889',
      'N' => 'W',
      'B' => 
      array (
        0 => '65',
        1 => '-18',
        2 => '940',
        3 => '669',
      ),
    ),
    88 => 
    array (
      'C' => '88',
      'WX' => '667',
      'N' => 'X',
      'B' => 
      array (
        0 => '-24',
        1 => '0',
        2 => '694',
        3 => '669',
      ),
    ),
    'X' => 
    array (
      'C' => '88',
      'WX' => '667',
      'N' => 'X',
      'B' => 
      array (
        0 => '-24',
        1 => '0',
        2 => '694',
        3 => '669',
      ),
    ),
    89 => 
    array (
      'C' => '89',
      'WX' => '611',
      'N' => 'Y',
      'B' => 
      array (
        0 => '73',
        1 => '0',
        2 => '659',
        3 => '669',
      ),
    ),
    'Y' => 
    array (
      'C' => '89',
      'WX' => '611',
      'N' => 'Y',
      'B' => 
      array (
        0 => '73',
        1 => '0',
        2 => '659',
        3 => '669',
      ),
    ),
    90 => 
    array (
      'C' => '90',
      'WX' => '611',
      'N' => 'Z',
      'B' => 
      array (
        0 => '-11',
        1 => '0',
        2 => '590',
        3 => '669',
      ),
    ),
    'Z' => 
    array (
      'C' => '90',
      'WX' => '611',
      'N' => 'Z',
      'B' => 
      array (
        0 => '-11',
        1 => '0',
        2 => '590',
        3 => '669',
      ),
    ),
    91 => 
    array (
      'C' => '91',
      'WX' => '333',
      'N' => 'bracketleft',
      'B' => 
      array (
        0 => '-37',
        1 => '-159',
        2 => '362',
        3 => '674',
      ),
    ),
    'bracketleft' => 
    array (
      'C' => '91',
      'WX' => '333',
      'N' => 'bracketleft',
      'B' => 
      array (
        0 => '-37',
        1 => '-159',
        2 => '362',
        3 => '674',
      ),
    ),
    92 => 
    array (
      'C' => '92',
      'WX' => '278',
      'N' => 'backslash',
      'B' => 
      array (
        0 => '-1',
        1 => '-18',
        2 => '279',
        3 => '685',
      ),
    ),
    'backslash' => 
    array (
      'C' => '92',
      'WX' => '278',
      'N' => 'backslash',
      'B' => 
      array (
        0 => '-1',
        1 => '-18',
        2 => '279',
        3 => '685',
      ),
    ),
    93 => 
    array (
      'C' => '93',
      'WX' => '333',
      'N' => 'bracketright',
      'B' => 
      array (
        0 => '-56',
        1 => '-157',
        2 => '343',
        3 => '674',
      ),
    ),
    'bracketright' => 
    array (
      'C' => '93',
      'WX' => '333',
      'N' => 'bracketright',
      'B' => 
      array (
        0 => '-56',
        1 => '-157',
        2 => '343',
        3 => '674',
      ),
    ),
    94 => 
    array (
      'C' => '94',
      'WX' => '570',
      'N' => 'asciicircum',
      'B' => 
      array (
        0 => '67',
        1 => '304',
        2 => '503',
        3 => '669',
      ),
    ),
    'asciicircum' => 
    array (
      'C' => '94',
      'WX' => '570',
      'N' => 'asciicircum',
      'B' => 
      array (
        0 => '67',
        1 => '304',
        2 => '503',
        3 => '669',
      ),
    ),
    95 => 
    array (
      'C' => '95',
      'WX' => '500',
      'N' => 'underscore',
      'B' => 
      array (
        0 => '0',
        1 => '-125',
        2 => '500',
        3 => '-75',
      ),
    ),
    'underscore' => 
    array (
      'C' => '95',
      'WX' => '500',
      'N' => 'underscore',
      'B' => 
      array (
        0 => '0',
        1 => '-125',
        2 => '500',
        3 => '-75',
      ),
    ),
    96 => 
    array (
      'C' => '96',
      'WX' => '333',
      'N' => 'quoteleft',
      'B' => 
      array (
        0 => '128',
        1 => '369',
        2 => '332',
        3 => '685',
      ),
    ),
    'quoteleft' => 
    array (
      'C' => '96',
      'WX' => '333',
      'N' => 'quoteleft',
      'B' => 
      array (
        0 => '128',
        1 => '369',
        2 => '332',
        3 => '685',
      ),
    ),
    97 => 
    array (
      'C' => '97',
      'WX' => '500',
      'N' => 'a',
      'B' => 
      array (
        0 => '-21',
        1 => '-14',
        2 => '455',
        3 => '462',
      ),
    ),
    'a' => 
    array (
      'C' => '97',
      'WX' => '500',
      'N' => 'a',
      'B' => 
      array (
        0 => '-21',
        1 => '-14',
        2 => '455',
        3 => '462',
      ),
    ),
    98 => 
    array (
      'C' => '98',
      'WX' => '500',
      'N' => 'b',
      'B' => 
      array (
        0 => '-14',
        1 => '-13',
        2 => '444',
        3 => '699',
      ),
    ),
    'b' => 
    array (
      'C' => '98',
      'WX' => '500',
      'N' => 'b',
      'B' => 
      array (
        0 => '-14',
        1 => '-13',
        2 => '444',
        3 => '699',
      ),
    ),
    99 => 
    array (
      'C' => '99',
      'WX' => '444',
      'N' => 'c',
      'B' => 
      array (
        0 => '-5',
        1 => '-13',
        2 => '392',
        3 => '462',
      ),
    ),
    'c' => 
    array (
      'C' => '99',
      'WX' => '444',
      'N' => 'c',
      'B' => 
      array (
        0 => '-5',
        1 => '-13',
        2 => '392',
        3 => '462',
      ),
    ),
    100 => 
    array (
      'C' => '100',
      'WX' => '500',
      'N' => 'd',
      'B' => 
      array (
        0 => '-21',
        1 => '-13',
        2 => '517',
        3 => '699',
      ),
    ),
    'd' => 
    array (
      'C' => '100',
      'WX' => '500',
      'N' => 'd',
      'B' => 
      array (
        0 => '-21',
        1 => '-13',
        2 => '517',
        3 => '699',
      ),
    ),
    101 => 
    array (
      'C' => '101',
      'WX' => '444',
      'N' => 'e',
      'B' => 
      array (
        0 => '5',
        1 => '-13',
        2 => '398',
        3 => '462',
      ),
    ),
    'e' => 
    array (
      'C' => '101',
      'WX' => '444',
      'N' => 'e',
      'B' => 
      array (
        0 => '5',
        1 => '-13',
        2 => '398',
        3 => '462',
      ),
    ),
    102 => 
    array (
      'C' => '102',
      'WX' => '333',
      'N' => 'f',
      'B' => 
      array (
        0 => '-169',
        1 => '-205',
        2 => '446',
        3 => '698',
      ),
      'L' => 
      array (
        0 => 'l',
        1 => 'fl',
      ),
    ),
    'f' => 
    array (
      'C' => '102',
      'WX' => '333',
      'N' => 'f',
      'B' => 
      array (
        0 => '-169',
        1 => '-205',
        2 => '446',
        3 => '698',
      ),
      'L' => 
      array (
        0 => 'l',
        1 => 'fl',
      ),
    ),
    103 => 
    array (
      'C' => '103',
      'WX' => '500',
      'N' => 'g',
      'B' => 
      array (
        0 => '-52',
        1 => '-203',
        2 => '478',
        3 => '462',
      ),
    ),
    'g' => 
    array (
      'C' => '103',
      'WX' => '500',
      'N' => 'g',
      'B' => 
      array (
        0 => '-52',
        1 => '-203',
        2 => '478',
        3 => '462',
      ),
    ),
    104 => 
    array (
      'C' => '104',
      'WX' => '556',
      'N' => 'h',
      'B' => 
      array (
        0 => '-13',
        1 => '-9',
        2 => '498',
        3 => '699',
      ),
    ),
    'h' => 
    array (
      'C' => '104',
      'WX' => '556',
      'N' => 'h',
      'B' => 
      array (
        0 => '-13',
        1 => '-9',
        2 => '498',
        3 => '699',
      ),
    ),
    105 => 
    array (
      'C' => '105',
      'WX' => '278',
      'N' => 'i',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '263',
        3 => '684',
      ),
    ),
    'i' => 
    array (
      'C' => '105',
      'WX' => '278',
      'N' => 'i',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '263',
        3 => '684',
      ),
    ),
    106 => 
    array (
      'C' => '106',
      'WX' => '278',
      'N' => 'j',
      'B' => 
      array (
        0 => '-189',
        1 => '-207',
        2 => '279',
        3 => '684',
      ),
    ),
    'j' => 
    array (
      'C' => '106',
      'WX' => '278',
      'N' => 'j',
      'B' => 
      array (
        0 => '-189',
        1 => '-207',
        2 => '279',
        3 => '684',
      ),
    ),
    107 => 
    array (
      'C' => '107',
      'WX' => '500',
      'N' => 'k',
      'B' => 
      array (
        0 => '-23',
        1 => '-8',
        2 => '483',
        3 => '699',
      ),
    ),
    'k' => 
    array (
      'C' => '107',
      'WX' => '500',
      'N' => 'k',
      'B' => 
      array (
        0 => '-23',
        1 => '-8',
        2 => '483',
        3 => '699',
      ),
    ),
    108 => 
    array (
      'C' => '108',
      'WX' => '278',
      'N' => 'l',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '290',
        3 => '699',
      ),
    ),
    'l' => 
    array (
      'C' => '108',
      'WX' => '278',
      'N' => 'l',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '290',
        3 => '699',
      ),
    ),
    109 => 
    array (
      'C' => '109',
      'WX' => '778',
      'N' => 'm',
      'B' => 
      array (
        0 => '-14',
        1 => '-9',
        2 => '722',
        3 => '462',
      ),
    ),
    'm' => 
    array (
      'C' => '109',
      'WX' => '778',
      'N' => 'm',
      'B' => 
      array (
        0 => '-14',
        1 => '-9',
        2 => '722',
        3 => '462',
      ),
    ),
    110 => 
    array (
      'C' => '110',
      'WX' => '556',
      'N' => 'n',
      'B' => 
      array (
        0 => '-6',
        1 => '-9',
        2 => '493',
        3 => '462',
      ),
    ),
    'n' => 
    array (
      'C' => '110',
      'WX' => '556',
      'N' => 'n',
      'B' => 
      array (
        0 => '-6',
        1 => '-9',
        2 => '493',
        3 => '462',
      ),
    ),
    111 => 
    array (
      'C' => '111',
      'WX' => '500',
      'N' => 'o',
      'B' => 
      array (
        0 => '-3',
        1 => '-13',
        2 => '441',
        3 => '462',
      ),
    ),
    'o' => 
    array (
      'C' => '111',
      'WX' => '500',
      'N' => 'o',
      'B' => 
      array (
        0 => '-3',
        1 => '-13',
        2 => '441',
        3 => '462',
      ),
    ),
    112 => 
    array (
      'C' => '112',
      'WX' => '500',
      'N' => 'p',
      'B' => 
      array (
        0 => '-120',
        1 => '-205',
        2 => '446',
        3 => '462',
      ),
    ),
    'p' => 
    array (
      'C' => '112',
      'WX' => '500',
      'N' => 'p',
      'B' => 
      array (
        0 => '-120',
        1 => '-205',
        2 => '446',
        3 => '462',
      ),
    ),
    113 => 
    array (
      'C' => '113',
      'WX' => '500',
      'N' => 'q',
      'B' => 
      array (
        0 => '1',
        1 => '-205',
        2 => '471',
        3 => '462',
      ),
    ),
    'q' => 
    array (
      'C' => '113',
      'WX' => '500',
      'N' => 'q',
      'B' => 
      array (
        0 => '1',
        1 => '-205',
        2 => '471',
        3 => '462',
      ),
    ),
    114 => 
    array (
      'C' => '114',
      'WX' => '389',
      'N' => 'r',
      'B' => 
      array (
        0 => '-21',
        1 => '0',
        2 => '389',
        3 => '462',
      ),
    ),
    'r' => 
    array (
      'C' => '114',
      'WX' => '389',
      'N' => 'r',
      'B' => 
      array (
        0 => '-21',
        1 => '0',
        2 => '389',
        3 => '462',
      ),
    ),
    115 => 
    array (
      'C' => '115',
      'WX' => '389',
      'N' => 's',
      'B' => 
      array (
        0 => '-19',
        1 => '-13',
        2 => '333',
        3 => '462',
      ),
    ),
    's' => 
    array (
      'C' => '115',
      'WX' => '389',
      'N' => 's',
      'B' => 
      array (
        0 => '-19',
        1 => '-13',
        2 => '333',
        3 => '462',
      ),
    ),
    116 => 
    array (
      'C' => '116',
      'WX' => '278',
      'N' => 't',
      'B' => 
      array (
        0 => '-11',
        1 => '-9',
        2 => '281',
        3 => '594',
      ),
    ),
    't' => 
    array (
      'C' => '116',
      'WX' => '278',
      'N' => 't',
      'B' => 
      array (
        0 => '-11',
        1 => '-9',
        2 => '281',
        3 => '594',
      ),
    ),
    117 => 
    array (
      'C' => '117',
      'WX' => '556',
      'N' => 'u',
      'B' => 
      array (
        0 => '15',
        1 => '-9',
        2 => '492',
        3 => '462',
      ),
    ),
    'u' => 
    array (
      'C' => '117',
      'WX' => '556',
      'N' => 'u',
      'B' => 
      array (
        0 => '15',
        1 => '-9',
        2 => '492',
        3 => '462',
      ),
    ),
    118 => 
    array (
      'C' => '118',
      'WX' => '444',
      'N' => 'v',
      'B' => 
      array (
        0 => '16',
        1 => '-13',
        2 => '401',
        3 => '462',
      ),
    ),
    'v' => 
    array (
      'C' => '118',
      'WX' => '444',
      'N' => 'v',
      'B' => 
      array (
        0 => '16',
        1 => '-13',
        2 => '401',
        3 => '462',
      ),
    ),
    119 => 
    array (
      'C' => '119',
      'WX' => '667',
      'N' => 'w',
      'B' => 
      array (
        0 => '16',
        1 => '-13',
        2 => '614',
        3 => '462',
      ),
    ),
    'w' => 
    array (
      'C' => '119',
      'WX' => '667',
      'N' => 'w',
      'B' => 
      array (
        0 => '16',
        1 => '-13',
        2 => '614',
        3 => '462',
      ),
    ),
    120 => 
    array (
      'C' => '120',
      'WX' => '500',
      'N' => 'x',
      'B' => 
      array (
        0 => '-46',
        1 => '-13',
        2 => '469',
        3 => '462',
      ),
    ),
    'x' => 
    array (
      'C' => '120',
      'WX' => '500',
      'N' => 'x',
      'B' => 
      array (
        0 => '-46',
        1 => '-13',
        2 => '469',
        3 => '462',
      ),
    ),
    121 => 
    array (
      'C' => '121',
      'WX' => '444',
      'N' => 'y',
      'B' => 
      array (
        0 => '-94',
        1 => '-205',
        2 => '392',
        3 => '462',
      ),
    ),
    'y' => 
    array (
      'C' => '121',
      'WX' => '444',
      'N' => 'y',
      'B' => 
      array (
        0 => '-94',
        1 => '-205',
        2 => '392',
        3 => '462',
      ),
    ),
    122 => 
    array (
      'C' => '122',
      'WX' => '389',
      'N' => 'z',
      'B' => 
      array (
        0 => '-43',
        1 => '-78',
        2 => '368',
        3 => '449',
      ),
    ),
    'z' => 
    array (
      'C' => '122',
      'WX' => '389',
      'N' => 'z',
      'B' => 
      array (
        0 => '-43',
        1 => '-78',
        2 => '368',
        3 => '449',
      ),
    ),
    123 => 
    array (
      'C' => '123',
      'WX' => '348',
      'N' => 'braceleft',
      'B' => 
      array (
        0 => '5',
        1 => '-187',
        2 => '436',
        3 => '686',
      ),
    ),
    'braceleft' => 
    array (
      'C' => '123',
      'WX' => '348',
      'N' => 'braceleft',
      'B' => 
      array (
        0 => '5',
        1 => '-187',
        2 => '436',
        3 => '686',
      ),
    ),
    124 => 
    array (
      'C' => '124',
      'WX' => '220',
      'N' => 'bar',
      'B' => 
      array (
        0 => '66',
        1 => '-218',
        2 => '154',
        3 => '782',
      ),
    ),
    'bar' => 
    array (
      'C' => '124',
      'WX' => '220',
      'N' => 'bar',
      'B' => 
      array (
        0 => '66',
        1 => '-218',
        2 => '154',
        3 => '782',
      ),
    ),
    125 => 
    array (
      'C' => '125',
      'WX' => '348',
      'N' => 'braceright',
      'B' => 
      array (
        0 => '-129',
        1 => '-187',
        2 => '302',
        3 => '686',
      ),
    ),
    'braceright' => 
    array (
      'C' => '125',
      'WX' => '348',
      'N' => 'braceright',
      'B' => 
      array (
        0 => '-129',
        1 => '-187',
        2 => '302',
        3 => '686',
      ),
    ),
    126 => 
    array (
      'C' => '126',
      'WX' => '570',
      'N' => 'asciitilde',
      'B' => 
      array (
        0 => '54',
        1 => '173',
        2 => '516',
        3 => '333',
      ),
    ),
    'asciitilde' => 
    array (
      'C' => '126',
      'WX' => '570',
      'N' => 'asciitilde',
      'B' => 
      array (
        0 => '54',
        1 => '173',
        2 => '516',
        3 => '333',
      ),
    ),
    161 => 
    array (
      'C' => '161',
      'WX' => '389',
      'N' => 'exclamdown',
      'B' => 
      array (
        0 => '19',
        1 => '-205',
        2 => '322',
        3 => '492',
      ),
    ),
    'exclamdown' => 
    array (
      'C' => '161',
      'WX' => '389',
      'N' => 'exclamdown',
      'B' => 
      array (
        0 => '19',
        1 => '-205',
        2 => '322',
        3 => '492',
      ),
    ),
    162 => 
    array (
      'C' => '162',
      'WX' => '500',
      'N' => 'cent',
      'B' => 
      array (
        0 => '42',
        1 => '-143',
        2 => '439',
        3 => '576',
      ),
    ),
    'cent' => 
    array (
      'C' => '162',
      'WX' => '500',
      'N' => 'cent',
      'B' => 
      array (
        0 => '42',
        1 => '-143',
        2 => '439',
        3 => '576',
      ),
    ),
    163 => 
    array (
      'C' => '163',
      'WX' => '500',
      'N' => 'sterling',
      'B' => 
      array (
        0 => '-32',
        1 => '-12',
        2 => '510',
        3 => '683',
      ),
    ),
    'sterling' => 
    array (
      'C' => '163',
      'WX' => '500',
      'N' => 'sterling',
      'B' => 
      array (
        0 => '-32',
        1 => '-12',
        2 => '510',
        3 => '683',
      ),
    ),
    164 => 
    array (
      'C' => '164',
      'WX' => '167',
      'N' => 'fraction',
      'B' => 
      array (
        0 => '-169',
        1 => '-14',
        2 => '324',
        3 => '683',
      ),
    ),
    'fraction' => 
    array (
      'C' => '164',
      'WX' => '167',
      'N' => 'fraction',
      'B' => 
      array (
        0 => '-169',
        1 => '-14',
        2 => '324',
        3 => '683',
      ),
    ),
    165 => 
    array (
      'C' => '165',
      'WX' => '500',
      'N' => 'yen',
      'B' => 
      array (
        0 => '33',
        1 => '0',
        2 => '628',
        3 => '669',
      ),
    ),
    'yen' => 
    array (
      'C' => '165',
      'WX' => '500',
      'N' => 'yen',
      'B' => 
      array (
        0 => '33',
        1 => '0',
        2 => '628',
        3 => '669',
      ),
    ),
    166 => 
    array (
      'C' => '166',
      'WX' => '500',
      'N' => 'florin',
      'B' => 
      array (
        0 => '-87',
        1 => '-156',
        2 => '537',
        3 => '707',
      ),
    ),
    'florin' => 
    array (
      'C' => '166',
      'WX' => '500',
      'N' => 'florin',
      'B' => 
      array (
        0 => '-87',
        1 => '-156',
        2 => '537',
        3 => '707',
      ),
    ),
    167 => 
    array (
      'C' => '167',
      'WX' => '500',
      'N' => 'section',
      'B' => 
      array (
        0 => '36',
        1 => '-143',
        2 => '459',
        3 => '685',
      ),
    ),
    'section' => 
    array (
      'C' => '167',
      'WX' => '500',
      'N' => 'section',
      'B' => 
      array (
        0 => '36',
        1 => '-143',
        2 => '459',
        3 => '685',
      ),
    ),
    168 => 
    array (
      'C' => '168',
      'WX' => '500',
      'N' => 'currency',
      'B' => 
      array (
        0 => '-26',
        1 => '34',
        2 => '526',
        3 => '586',
      ),
    ),
    'currency' => 
    array (
      'C' => '168',
      'WX' => '500',
      'N' => 'currency',
      'B' => 
      array (
        0 => '-26',
        1 => '34',
        2 => '526',
        3 => '586',
      ),
    ),
    169 => 
    array (
      'C' => '169',
      'WX' => '278',
      'N' => 'quotesingle',
      'B' => 
      array (
        0 => '128',
        1 => '398',
        2 => '268',
        3 => '685',
      ),
    ),
    'quotesingle' => 
    array (
      'C' => '169',
      'WX' => '278',
      'N' => 'quotesingle',
      'B' => 
      array (
        0 => '128',
        1 => '398',
        2 => '268',
        3 => '685',
      ),
    ),
    170 => 
    array (
      'C' => '170',
      'WX' => '500',
      'N' => 'quotedblleft',
      'B' => 
      array (
        0 => '53',
        1 => '369',
        2 => '513',
        3 => '685',
      ),
    ),
    'quotedblleft' => 
    array (
      'C' => '170',
      'WX' => '500',
      'N' => 'quotedblleft',
      'B' => 
      array (
        0 => '53',
        1 => '369',
        2 => '513',
        3 => '685',
      ),
    ),
    171 => 
    array (
      'C' => '171',
      'WX' => '500',
      'N' => 'guillemotleft',
      'B' => 
      array (
        0 => '12',
        1 => '32',
        2 => '468',
        3 => '415',
      ),
    ),
    'guillemotleft' => 
    array (
      'C' => '171',
      'WX' => '500',
      'N' => 'guillemotleft',
      'B' => 
      array (
        0 => '12',
        1 => '32',
        2 => '468',
        3 => '415',
      ),
    ),
    172 => 
    array (
      'C' => '172',
      'WX' => '333',
      'N' => 'guilsinglleft',
      'B' => 
      array (
        0 => '32',
        1 => '32',
        2 => '303',
        3 => '415',
      ),
    ),
    'guilsinglleft' => 
    array (
      'C' => '172',
      'WX' => '333',
      'N' => 'guilsinglleft',
      'B' => 
      array (
        0 => '32',
        1 => '32',
        2 => '303',
        3 => '415',
      ),
    ),
    173 => 
    array (
      'C' => '173',
      'WX' => '333',
      'N' => 'guilsinglright',
      'B' => 
      array (
        0 => '10',
        1 => '32',
        2 => '281',
        3 => '415',
      ),
    ),
    'guilsinglright' => 
    array (
      'C' => '173',
      'WX' => '333',
      'N' => 'guilsinglright',
      'B' => 
      array (
        0 => '10',
        1 => '32',
        2 => '281',
        3 => '415',
      ),
    ),
    174 => 
    array (
      'C' => '174',
      'WX' => '556',
      'N' => 'fi',
      'B' => 
      array (
        0 => '-188',
        1 => '-205',
        2 => '514',
        3 => '703',
      ),
    ),
    'fi' => 
    array (
      'C' => '174',
      'WX' => '556',
      'N' => 'fi',
      'B' => 
      array (
        0 => '-188',
        1 => '-205',
        2 => '514',
        3 => '703',
      ),
    ),
    175 => 
    array (
      'C' => '175',
      'WX' => '556',
      'N' => 'fl',
      'B' => 
      array (
        0 => '-186',
        1 => '-205',
        2 => '553',
        3 => '704',
      ),
    ),
    'fl' => 
    array (
      'C' => '175',
      'WX' => '556',
      'N' => 'fl',
      'B' => 
      array (
        0 => '-186',
        1 => '-205',
        2 => '553',
        3 => '704',
      ),
    ),
    177 => 
    array (
      'C' => '177',
      'WX' => '500',
      'N' => 'endash',
      'B' => 
      array (
        0 => '-40',
        1 => '178',
        2 => '477',
        3 => '269',
      ),
    ),
    'endash' => 
    array (
      'C' => '177',
      'WX' => '500',
      'N' => 'endash',
      'B' => 
      array (
        0 => '-40',
        1 => '178',
        2 => '477',
        3 => '269',
      ),
    ),
    178 => 
    array (
      'C' => '178',
      'WX' => '500',
      'N' => 'dagger',
      'B' => 
      array (
        0 => '91',
        1 => '-145',
        2 => '494',
        3 => '685',
      ),
    ),
    'dagger' => 
    array (
      'C' => '178',
      'WX' => '500',
      'N' => 'dagger',
      'B' => 
      array (
        0 => '91',
        1 => '-145',
        2 => '494',
        3 => '685',
      ),
    ),
    179 => 
    array (
      'C' => '179',
      'WX' => '500',
      'N' => 'daggerdbl',
      'B' => 
      array (
        0 => '10',
        1 => '-139',
        2 => '493',
        3 => '685',
      ),
    ),
    'daggerdbl' => 
    array (
      'C' => '179',
      'WX' => '500',
      'N' => 'daggerdbl',
      'B' => 
      array (
        0 => '10',
        1 => '-139',
        2 => '493',
        3 => '685',
      ),
    ),
    180 => 
    array (
      'C' => '180',
      'WX' => '250',
      'N' => 'periodcentered',
      'B' => 
      array (
        0 => '51',
        1 => '257',
        2 => '199',
        3 => '405',
      ),
    ),
    'periodcentered' => 
    array (
      'C' => '180',
      'WX' => '250',
      'N' => 'periodcentered',
      'B' => 
      array (
        0 => '51',
        1 => '257',
        2 => '199',
        3 => '405',
      ),
    ),
    182 => 
    array (
      'C' => '182',
      'WX' => '500',
      'N' => 'paragraph',
      'B' => 
      array (
        0 => '-57',
        1 => '-193',
        2 => '562',
        3 => '669',
      ),
    ),
    'paragraph' => 
    array (
      'C' => '182',
      'WX' => '500',
      'N' => 'paragraph',
      'B' => 
      array (
        0 => '-57',
        1 => '-193',
        2 => '562',
        3 => '669',
      ),
    ),
    183 => 
    array (
      'C' => '183',
      'WX' => '350',
      'N' => 'bullet',
      'B' => 
      array (
        0 => '0',
        1 => '175',
        2 => '350',
        3 => '525',
      ),
    ),
    'bullet' => 
    array (
      'C' => '183',
      'WX' => '350',
      'N' => 'bullet',
      'B' => 
      array (
        0 => '0',
        1 => '175',
        2 => '350',
        3 => '525',
      ),
    ),
    184 => 
    array (
      'C' => '184',
      'WX' => '333',
      'N' => 'quotesinglbase',
      'B' => 
      array (
        0 => '-5',
        1 => '-182',
        2 => '199',
        3 => '134',
      ),
    ),
    'quotesinglbase' => 
    array (
      'C' => '184',
      'WX' => '333',
      'N' => 'quotesinglbase',
      'B' => 
      array (
        0 => '-5',
        1 => '-182',
        2 => '199',
        3 => '134',
      ),
    ),
    185 => 
    array (
      'C' => '185',
      'WX' => '500',
      'N' => 'quotedblbase',
      'B' => 
      array (
        0 => '-57',
        1 => '-182',
        2 => '403',
        3 => '134',
      ),
    ),
    'quotedblbase' => 
    array (
      'C' => '185',
      'WX' => '500',
      'N' => 'quotedblbase',
      'B' => 
      array (
        0 => '-57',
        1 => '-182',
        2 => '403',
        3 => '134',
      ),
    ),
    186 => 
    array (
      'C' => '186',
      'WX' => '500',
      'N' => 'quotedblright',
      'B' => 
      array (
        0 => '53',
        1 => '369',
        2 => '513',
        3 => '685',
      ),
    ),
    'quotedblright' => 
    array (
      'C' => '186',
      'WX' => '500',
      'N' => 'quotedblright',
      'B' => 
      array (
        0 => '53',
        1 => '369',
        2 => '513',
        3 => '685',
      ),
    ),
    187 => 
    array (
      'C' => '187',
      'WX' => '500',
      'N' => 'guillemotright',
      'B' => 
      array (
        0 => '12',
        1 => '32',
        2 => '468',
        3 => '415',
      ),
    ),
    'guillemotright' => 
    array (
      'C' => '187',
      'WX' => '500',
      'N' => 'guillemotright',
      'B' => 
      array (
        0 => '12',
        1 => '32',
        2 => '468',
        3 => '415',
      ),
    ),
    188 => 
    array (
      'C' => '188',
      'WX' => '1000',
      'N' => 'ellipsis',
      'B' => 
      array (
        0 => '40',
        1 => '-13',
        2 => '852',
        3 => '135',
      ),
    ),
    'ellipsis' => 
    array (
      'C' => '188',
      'WX' => '1000',
      'N' => 'ellipsis',
      'B' => 
      array (
        0 => '40',
        1 => '-13',
        2 => '852',
        3 => '135',
      ),
    ),
    189 => 
    array (
      'C' => '189',
      'WX' => '1000',
      'N' => 'perthousand',
      'B' => 
      array (
        0 => '7',
        1 => '-29',
        2 => '996',
        3 => '706',
      ),
    ),
    'perthousand' => 
    array (
      'C' => '189',
      'WX' => '1000',
      'N' => 'perthousand',
      'B' => 
      array (
        0 => '7',
        1 => '-29',
        2 => '996',
        3 => '706',
      ),
    ),
    191 => 
    array (
      'C' => '191',
      'WX' => '500',
      'N' => 'questiondown',
      'B' => 
      array (
        0 => '30',
        1 => '-205',
        2 => '421',
        3 => '492',
      ),
    ),
    'questiondown' => 
    array (
      'C' => '191',
      'WX' => '500',
      'N' => 'questiondown',
      'B' => 
      array (
        0 => '30',
        1 => '-205',
        2 => '421',
        3 => '492',
      ),
    ),
    193 => 
    array (
      'C' => '193',
      'WX' => '333',
      'N' => 'grave',
      'B' => 
      array (
        0 => '85',
        1 => '516',
        2 => '297',
        3 => '697',
      ),
    ),
    'grave' => 
    array (
      'C' => '193',
      'WX' => '333',
      'N' => 'grave',
      'B' => 
      array (
        0 => '85',
        1 => '516',
        2 => '297',
        3 => '697',
      ),
    ),
    194 => 
    array (
      'C' => '194',
      'WX' => '333',
      'N' => 'acute',
      'B' => 
      array (
        0 => '139',
        1 => '516',
        2 => '379',
        3 => '697',
      ),
    ),
    'acute' => 
    array (
      'C' => '194',
      'WX' => '333',
      'N' => 'acute',
      'B' => 
      array (
        0 => '139',
        1 => '516',
        2 => '379',
        3 => '697',
      ),
    ),
    195 => 
    array (
      'C' => '195',
      'WX' => '333',
      'N' => 'circumflex',
      'B' => 
      array (
        0 => '40',
        1 => '516',
        2 => '367',
        3 => '690',
      ),
    ),
    'circumflex' => 
    array (
      'C' => '195',
      'WX' => '333',
      'N' => 'circumflex',
      'B' => 
      array (
        0 => '40',
        1 => '516',
        2 => '367',
        3 => '690',
      ),
    ),
    196 => 
    array (
      'C' => '196',
      'WX' => '333',
      'N' => 'tilde',
      'B' => 
      array (
        0 => '48',
        1 => '536',
        2 => '407',
        3 => '655',
      ),
    ),
    'tilde' => 
    array (
      'C' => '196',
      'WX' => '333',
      'N' => 'tilde',
      'B' => 
      array (
        0 => '48',
        1 => '536',
        2 => '407',
        3 => '655',
      ),
    ),
    197 => 
    array (
      'C' => '197',
      'WX' => '333',
      'N' => 'macron',
      'B' => 
      array (
        0 => '51',
        1 => '553',
        2 => '393',
        3 => '623',
      ),
    ),
    'macron' => 
    array (
      'C' => '197',
      'WX' => '333',
      'N' => 'macron',
      'B' => 
      array (
        0 => '51',
        1 => '553',
        2 => '393',
        3 => '623',
      ),
    ),
    198 => 
    array (
      'C' => '198',
      'WX' => '333',
      'N' => 'breve',
      'B' => 
      array (
        0 => '71',
        1 => '516',
        2 => '387',
        3 => '678',
      ),
    ),
    'breve' => 
    array (
      'C' => '198',
      'WX' => '333',
      'N' => 'breve',
      'B' => 
      array (
        0 => '71',
        1 => '516',
        2 => '387',
        3 => '678',
      ),
    ),
    199 => 
    array (
      'C' => '199',
      'WX' => '333',
      'N' => 'dotaccent',
      'B' => 
      array (
        0 => '163',
        1 => '550',
        2 => '298',
        3 => '684',
      ),
    ),
    'dotaccent' => 
    array (
      'C' => '199',
      'WX' => '333',
      'N' => 'dotaccent',
      'B' => 
      array (
        0 => '163',
        1 => '550',
        2 => '298',
        3 => '684',
      ),
    ),
    200 => 
    array (
      'C' => '200',
      'WX' => '333',
      'N' => 'dieresis',
      'B' => 
      array (
        0 => '55',
        1 => '550',
        2 => '402',
        3 => '684',
      ),
    ),
    'dieresis' => 
    array (
      'C' => '200',
      'WX' => '333',
      'N' => 'dieresis',
      'B' => 
      array (
        0 => '55',
        1 => '550',
        2 => '402',
        3 => '684',
      ),
    ),
    202 => 
    array (
      'C' => '202',
      'WX' => '333',
      'N' => 'ring',
      'B' => 
      array (
        0 => '127',
        1 => '516',
        2 => '340',
        3 => '729',
      ),
    ),
    'ring' => 
    array (
      'C' => '202',
      'WX' => '333',
      'N' => 'ring',
      'B' => 
      array (
        0 => '127',
        1 => '516',
        2 => '340',
        3 => '729',
      ),
    ),
    203 => 
    array (
      'C' => '203',
      'WX' => '333',
      'N' => 'cedilla',
      'B' => 
      array (
        0 => '-80',
        1 => '-218',
        2 => '156',
        3 => '5',
      ),
    ),
    'cedilla' => 
    array (
      'C' => '203',
      'WX' => '333',
      'N' => 'cedilla',
      'B' => 
      array (
        0 => '-80',
        1 => '-218',
        2 => '156',
        3 => '5',
      ),
    ),
    205 => 
    array (
      'C' => '205',
      'WX' => '333',
      'N' => 'hungarumlaut',
      'B' => 
      array (
        0 => '69',
        1 => '516',
        2 => '498',
        3 => '697',
      ),
    ),
    'hungarumlaut' => 
    array (
      'C' => '205',
      'WX' => '333',
      'N' => 'hungarumlaut',
      'B' => 
      array (
        0 => '69',
        1 => '516',
        2 => '498',
        3 => '697',
      ),
    ),
    206 => 
    array (
      'C' => '206',
      'WX' => '333',
      'N' => 'ogonek',
      'B' => 
      array (
        0 => '15',
        1 => '-183',
        2 => '244',
        3 => '34',
      ),
    ),
    'ogonek' => 
    array (
      'C' => '206',
      'WX' => '333',
      'N' => 'ogonek',
      'B' => 
      array (
        0 => '15',
        1 => '-183',
        2 => '244',
        3 => '34',
      ),
    ),
    207 => 
    array (
      'C' => '207',
      'WX' => '333',
      'N' => 'caron',
      'B' => 
      array (
        0 => '79',
        1 => '516',
        2 => '411',
        3 => '690',
      ),
    ),
    'caron' => 
    array (
      'C' => '207',
      'WX' => '333',
      'N' => 'caron',
      'B' => 
      array (
        0 => '79',
        1 => '516',
        2 => '411',
        3 => '690',
      ),
    ),
    208 => 
    array (
      'C' => '208',
      'WX' => '1000',
      'N' => 'emdash',
      'B' => 
      array (
        0 => '-40',
        1 => '178',
        2 => '977',
        3 => '269',
      ),
    ),
    'emdash' => 
    array (
      'C' => '208',
      'WX' => '1000',
      'N' => 'emdash',
      'B' => 
      array (
        0 => '-40',
        1 => '178',
        2 => '977',
        3 => '269',
      ),
    ),
    225 => 
    array (
      'C' => '225',
      'WX' => '944',
      'N' => 'AE',
      'B' => 
      array (
        0 => '-64',
        1 => '0',
        2 => '918',
        3 => '669',
      ),
    ),
    'AE' => 
    array (
      'C' => '225',
      'WX' => '944',
      'N' => 'AE',
      'B' => 
      array (
        0 => '-64',
        1 => '0',
        2 => '918',
        3 => '669',
      ),
    ),
    227 => 
    array (
      'C' => '227',
      'WX' => '266',
      'N' => 'ordfeminine',
      'B' => 
      array (
        0 => '16',
        1 => '399',
        2 => '330',
        3 => '685',
      ),
    ),
    'ordfeminine' => 
    array (
      'C' => '227',
      'WX' => '266',
      'N' => 'ordfeminine',
      'B' => 
      array (
        0 => '16',
        1 => '399',
        2 => '330',
        3 => '685',
      ),
    ),
    232 => 
    array (
      'C' => '232',
      'WX' => '611',
      'N' => 'Lslash',
      'B' => 
      array (
        0 => '-22',
        1 => '0',
        2 => '590',
        3 => '669',
      ),
    ),
    'Lslash' => 
    array (
      'C' => '232',
      'WX' => '611',
      'N' => 'Lslash',
      'B' => 
      array (
        0 => '-22',
        1 => '0',
        2 => '590',
        3 => '669',
      ),
    ),
    233 => 
    array (
      'C' => '233',
      'WX' => '722',
      'N' => 'Oslash',
      'B' => 
      array (
        0 => '27',
        1 => '-125',
        2 => '691',
        3 => '764',
      ),
    ),
    'Oslash' => 
    array (
      'C' => '233',
      'WX' => '722',
      'N' => 'Oslash',
      'B' => 
      array (
        0 => '27',
        1 => '-125',
        2 => '691',
        3 => '764',
      ),
    ),
    234 => 
    array (
      'C' => '234',
      'WX' => '944',
      'N' => 'OE',
      'B' => 
      array (
        0 => '23',
        1 => '-8',
        2 => '946',
        3 => '677',
      ),
    ),
    'OE' => 
    array (
      'C' => '234',
      'WX' => '944',
      'N' => 'OE',
      'B' => 
      array (
        0 => '23',
        1 => '-8',
        2 => '946',
        3 => '677',
      ),
    ),
    235 => 
    array (
      'C' => '235',
      'WX' => '300',
      'N' => 'ordmasculine',
      'B' => 
      array (
        0 => '56',
        1 => '400',
        2 => '347',
        3 => '685',
      ),
    ),
    'ordmasculine' => 
    array (
      'C' => '235',
      'WX' => '300',
      'N' => 'ordmasculine',
      'B' => 
      array (
        0 => '56',
        1 => '400',
        2 => '347',
        3 => '685',
      ),
    ),
    241 => 
    array (
      'C' => '241',
      'WX' => '722',
      'N' => 'ae',
      'B' => 
      array (
        0 => '-5',
        1 => '-13',
        2 => '673',
        3 => '462',
      ),
    ),
    'ae' => 
    array (
      'C' => '241',
      'WX' => '722',
      'N' => 'ae',
      'B' => 
      array (
        0 => '-5',
        1 => '-13',
        2 => '673',
        3 => '462',
      ),
    ),
    245 => 
    array (
      'C' => '245',
      'WX' => '278',
      'N' => 'dotlessi',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '238',
        3 => '462',
      ),
    ),
    'dotlessi' => 
    array (
      'C' => '245',
      'WX' => '278',
      'N' => 'dotlessi',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '238',
        3 => '462',
      ),
    ),
    248 => 
    array (
      'C' => '248',
      'WX' => '278',
      'N' => 'lslash',
      'B' => 
      array (
        0 => '-7',
        1 => '-9',
        2 => '307',
        3 => '699',
      ),
    ),
    'lslash' => 
    array (
      'C' => '248',
      'WX' => '278',
      'N' => 'lslash',
      'B' => 
      array (
        0 => '-7',
        1 => '-9',
        2 => '307',
        3 => '699',
      ),
    ),
    249 => 
    array (
      'C' => '249',
      'WX' => '500',
      'N' => 'oslash',
      'B' => 
      array (
        0 => '-3',
        1 => '-119',
        2 => '441',
        3 => '560',
      ),
    ),
    'oslash' => 
    array (
      'C' => '249',
      'WX' => '500',
      'N' => 'oslash',
      'B' => 
      array (
        0 => '-3',
        1 => '-119',
        2 => '441',
        3 => '560',
      ),
    ),
    250 => 
    array (
      'C' => '250',
      'WX' => '722',
      'N' => 'oe',
      'B' => 
      array (
        0 => '6',
        1 => '-13',
        2 => '674',
        3 => '462',
      ),
    ),
    'oe' => 
    array (
      'C' => '250',
      'WX' => '722',
      'N' => 'oe',
      'B' => 
      array (
        0 => '6',
        1 => '-13',
        2 => '674',
        3 => '462',
      ),
    ),
    251 => 
    array (
      'C' => '251',
      'WX' => '500',
      'N' => 'germandbls',
      'B' => 
      array (
        0 => '-200',
        1 => '-200',
        2 => '473',
        3 => '705',
      ),
    ),
    'germandbls' => 
    array (
      'C' => '251',
      'WX' => '500',
      'N' => 'germandbls',
      'B' => 
      array (
        0 => '-200',
        1 => '-200',
        2 => '473',
        3 => '705',
      ),
    ),
    'Idieresis' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Idieresis',
      'B' => 
      array (
        0 => '-32',
        1 => '0',
        2 => '450',
        3 => '862',
      ),
    ),
    'eacute' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'eacute',
      'B' => 
      array (
        0 => '5',
        1 => '-13',
        2 => '435',
        3 => '697',
      ),
    ),
    'abreve' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'abreve',
      'B' => 
      array (
        0 => '-21',
        1 => '-14',
        2 => '471',
        3 => '678',
      ),
    ),
    'uhungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uhungarumlaut',
      'B' => 
      array (
        0 => '15',
        1 => '-9',
        2 => '610',
        3 => '697',
      ),
    ),
    'ecaron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ecaron',
      'B' => 
      array (
        0 => '5',
        1 => '-13',
        2 => '467',
        3 => '690',
      ),
    ),
    'Ydieresis' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Ydieresis',
      'B' => 
      array (
        0 => '73',
        1 => '0',
        2 => '659',
        3 => '862',
      ),
    ),
    'divide' => 
    array (
      'C' => '-1',
      'WX' => '570',
      'N' => 'divide',
      'B' => 
      array (
        0 => '33',
        1 => '-29',
        2 => '537',
        3 => '535',
      ),
    ),
    'Yacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Yacute',
      'B' => 
      array (
        0 => '73',
        1 => '0',
        2 => '659',
        3 => '904',
      ),
    ),
    'Acircumflex' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Acircumflex',
      'B' => 
      array (
        0 => '-67',
        1 => '0',
        2 => '593',
        3 => '897',
      ),
    ),
    'aacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'aacute',
      'B' => 
      array (
        0 => '-21',
        1 => '-14',
        2 => '463',
        3 => '697',
      ),
    ),
    'Ucircumflex' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ucircumflex',
      'B' => 
      array (
        0 => '67',
        1 => '-18',
        2 => '744',
        3 => '897',
      ),
    ),
    'yacute' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'yacute',
      'B' => 
      array (
        0 => '-94',
        1 => '-205',
        2 => '435',
        3 => '697',
      ),
    ),
    'scommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'scommaaccent',
      'B' => 
      array (
        0 => '-19',
        1 => '-218',
        2 => '333',
        3 => '462',
      ),
    ),
    'ecircumflex' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ecircumflex',
      'B' => 
      array (
        0 => '5',
        1 => '-13',
        2 => '423',
        3 => '690',
      ),
    ),
    'Uring' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uring',
      'B' => 
      array (
        0 => '67',
        1 => '-18',
        2 => '744',
        3 => '921',
      ),
    ),
    'Udieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Udieresis',
      'B' => 
      array (
        0 => '67',
        1 => '-18',
        2 => '744',
        3 => '862',
      ),
    ),
    'aogonek' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'aogonek',
      'B' => 
      array (
        0 => '-21',
        1 => '-183',
        2 => '455',
        3 => '462',
      ),
    ),
    'Uacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uacute',
      'B' => 
      array (
        0 => '67',
        1 => '-18',
        2 => '744',
        3 => '904',
      ),
    ),
    'uogonek' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uogonek',
      'B' => 
      array (
        0 => '15',
        1 => '-183',
        2 => '492',
        3 => '462',
      ),
    ),
    'Edieresis' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Edieresis',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '653',
        3 => '862',
      ),
    ),
    'Dcroat' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Dcroat',
      'B' => 
      array (
        0 => '-31',
        1 => '0',
        2 => '700',
        3 => '669',
      ),
    ),
    'commaaccent' => 
    array (
      'C' => '-1',
      'WX' => '250',
      'N' => 'commaaccent',
      'B' => 
      array (
        0 => '-36',
        1 => '-218',
        2 => '131',
        3 => '-50',
      ),
    ),
    'copyright' => 
    array (
      'C' => '-1',
      'WX' => '747',
      'N' => 'copyright',
      'B' => 
      array (
        0 => '30',
        1 => '-18',
        2 => '718',
        3 => '685',
      ),
    ),
    'Emacron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Emacron',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '653',
        3 => '830',
      ),
    ),
    'ccaron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ccaron',
      'B' => 
      array (
        0 => '-5',
        1 => '-13',
        2 => '467',
        3 => '690',
      ),
    ),
    'aring' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'aring',
      'B' => 
      array (
        0 => '-21',
        1 => '-14',
        2 => '455',
        3 => '729',
      ),
    ),
    'Ncommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ncommaaccent',
      'B' => 
      array (
        0 => '-27',
        1 => '-218',
        2 => '748',
        3 => '669',
      ),
    ),
    'lacute' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'lacute',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '392',
        3 => '904',
      ),
    ),
    'agrave' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'agrave',
      'B' => 
      array (
        0 => '-21',
        1 => '-14',
        2 => '455',
        3 => '697',
      ),
    ),
    'Tcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Tcommaaccent',
      'B' => 
      array (
        0 => '50',
        1 => '-218',
        2 => '650',
        3 => '669',
      ),
    ),
    'Cacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Cacute',
      'B' => 
      array (
        0 => '32',
        1 => '-18',
        2 => '677',
        3 => '904',
      ),
    ),
    'atilde' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'atilde',
      'B' => 
      array (
        0 => '-21',
        1 => '-14',
        2 => '491',
        3 => '655',
      ),
    ),
    'Edotaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Edotaccent',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '653',
        3 => '862',
      ),
    ),
    'scaron' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'scaron',
      'B' => 
      array (
        0 => '-19',
        1 => '-13',
        2 => '424',
        3 => '690',
      ),
    ),
    'scedilla' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'scedilla',
      'B' => 
      array (
        0 => '-19',
        1 => '-218',
        2 => '333',
        3 => '462',
      ),
    ),
    'iacute' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'iacute',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '352',
        3 => '697',
      ),
    ),
    'lozenge' => 
    array (
      'C' => '-1',
      'WX' => '494',
      'N' => 'lozenge',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '484',
        3 => '745',
      ),
    ),
    'Rcaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Rcaron',
      'B' => 
      array (
        0 => '-29',
        1 => '0',
        2 => '623',
        3 => '897',
      ),
    ),
    'Gcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Gcommaaccent',
      'B' => 
      array (
        0 => '21',
        1 => '-218',
        2 => '706',
        3 => '685',
      ),
    ),
    'ucircumflex' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ucircumflex',
      'B' => 
      array (
        0 => '15',
        1 => '-9',
        2 => '492',
        3 => '690',
      ),
    ),
    'acircumflex' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'acircumflex',
      'B' => 
      array (
        0 => '-21',
        1 => '-14',
        2 => '455',
        3 => '690',
      ),
    ),
    'Amacron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Amacron',
      'B' => 
      array (
        0 => '-67',
        1 => '0',
        2 => '593',
        3 => '830',
      ),
    ),
    'rcaron' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'rcaron',
      'B' => 
      array (
        0 => '-21',
        1 => '0',
        2 => '424',
        3 => '690',
      ),
    ),
    'ccedilla' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ccedilla',
      'B' => 
      array (
        0 => '-5',
        1 => '-218',
        2 => '392',
        3 => '462',
      ),
    ),
    'Zdotaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zdotaccent',
      'B' => 
      array (
        0 => '-11',
        1 => '0',
        2 => '590',
        3 => '862',
      ),
    ),
    'Thorn' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Thorn',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '573',
        3 => '669',
      ),
    ),
    'Omacron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Omacron',
      'B' => 
      array (
        0 => '27',
        1 => '-18',
        2 => '691',
        3 => '830',
      ),
    ),
    'Racute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Racute',
      'B' => 
      array (
        0 => '-29',
        1 => '0',
        2 => '623',
        3 => '904',
      ),
    ),
    'Sacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Sacute',
      'B' => 
      array (
        0 => '2',
        1 => '-18',
        2 => '531',
        3 => '904',
      ),
    ),
    'dcaron' => 
    array (
      'C' => '-1',
      'WX' => '608',
      'N' => 'dcaron',
      'B' => 
      array (
        0 => '-21',
        1 => '-13',
        2 => '675',
        3 => '708',
      ),
    ),
    'Umacron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Umacron',
      'B' => 
      array (
        0 => '67',
        1 => '-18',
        2 => '744',
        3 => '830',
      ),
    ),
    'uring' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uring',
      'B' => 
      array (
        0 => '15',
        1 => '-9',
        2 => '492',
        3 => '729',
      ),
    ),
    'threesuperior' => 
    array (
      'C' => '-1',
      'WX' => '300',
      'N' => 'threesuperior',
      'B' => 
      array (
        0 => '17',
        1 => '265',
        2 => '321',
        3 => '683',
      ),
    ),
    'Ograve' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ograve',
      'B' => 
      array (
        0 => '27',
        1 => '-18',
        2 => '691',
        3 => '904',
      ),
    ),
    'Agrave' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Agrave',
      'B' => 
      array (
        0 => '-67',
        1 => '0',
        2 => '593',
        3 => '904',
      ),
    ),
    'Abreve' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Abreve',
      'B' => 
      array (
        0 => '-67',
        1 => '0',
        2 => '593',
        3 => '885',
      ),
    ),
    'multiply' => 
    array (
      'C' => '-1',
      'WX' => '570',
      'N' => 'multiply',
      'B' => 
      array (
        0 => '48',
        1 => '16',
        2 => '522',
        3 => '490',
      ),
    ),
    'uacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uacute',
      'B' => 
      array (
        0 => '15',
        1 => '-9',
        2 => '492',
        3 => '697',
      ),
    ),
    'Tcaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Tcaron',
      'B' => 
      array (
        0 => '50',
        1 => '0',
        2 => '650',
        3 => '897',
      ),
    ),
    'partialdiff' => 
    array (
      'C' => '-1',
      'WX' => '494',
      'N' => 'partialdiff',
      'B' => 
      array (
        0 => '11',
        1 => '-21',
        2 => '494',
        3 => '750',
      ),
    ),
    'ydieresis' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'ydieresis',
      'B' => 
      array (
        0 => '-94',
        1 => '-205',
        2 => '443',
        3 => '655',
      ),
    ),
    'Nacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Nacute',
      'B' => 
      array (
        0 => '-27',
        1 => '-15',
        2 => '748',
        3 => '904',
      ),
    ),
    'icircumflex' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'icircumflex',
      'B' => 
      array (
        0 => '-3',
        1 => '-9',
        2 => '324',
        3 => '690',
      ),
    ),
    'Ecircumflex' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ecircumflex',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '653',
        3 => '897',
      ),
    ),
    'adieresis' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'adieresis',
      'B' => 
      array (
        0 => '-21',
        1 => '-14',
        2 => '476',
        3 => '655',
      ),
    ),
    'edieresis' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'edieresis',
      'B' => 
      array (
        0 => '5',
        1 => '-13',
        2 => '448',
        3 => '655',
      ),
    ),
    'cacute' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'cacute',
      'B' => 
      array (
        0 => '-5',
        1 => '-13',
        2 => '435',
        3 => '697',
      ),
    ),
    'nacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'nacute',
      'B' => 
      array (
        0 => '-6',
        1 => '-9',
        2 => '493',
        3 => '697',
      ),
    ),
    'umacron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'umacron',
      'B' => 
      array (
        0 => '15',
        1 => '-9',
        2 => '492',
        3 => '623',
      ),
    ),
    'Ncaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ncaron',
      'B' => 
      array (
        0 => '-27',
        1 => '-15',
        2 => '748',
        3 => '897',
      ),
    ),
    'Iacute' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Iacute',
      'B' => 
      array (
        0 => '-32',
        1 => '0',
        2 => '432',
        3 => '904',
      ),
    ),
    'plusminus' => 
    array (
      'C' => '-1',
      'WX' => '570',
      'N' => 'plusminus',
      'B' => 
      array (
        0 => '33',
        1 => '0',
        2 => '537',
        3 => '506',
      ),
    ),
    'brokenbar' => 
    array (
      'C' => '-1',
      'WX' => '220',
      'N' => 'brokenbar',
      'B' => 
      array (
        0 => '66',
        1 => '-143',
        2 => '154',
        3 => '707',
      ),
    ),
    'registered' => 
    array (
      'C' => '-1',
      'WX' => '747',
      'N' => 'registered',
      'B' => 
      array (
        0 => '30',
        1 => '-18',
        2 => '718',
        3 => '685',
      ),
    ),
    'Gbreve' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Gbreve',
      'B' => 
      array (
        0 => '21',
        1 => '-18',
        2 => '706',
        3 => '885',
      ),
    ),
    'Idotaccent' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Idotaccent',
      'B' => 
      array (
        0 => '-32',
        1 => '0',
        2 => '406',
        3 => '862',
      ),
    ),
    'summation' => 
    array (
      'C' => '-1',
      'WX' => '600',
      'N' => 'summation',
      'B' => 
      array (
        0 => '14',
        1 => '-10',
        2 => '585',
        3 => '706',
      ),
    ),
    'Egrave' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Egrave',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '653',
        3 => '904',
      ),
    ),
    'racute' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'racute',
      'B' => 
      array (
        0 => '-21',
        1 => '0',
        2 => '407',
        3 => '697',
      ),
    ),
    'omacron' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'omacron',
      'B' => 
      array (
        0 => '-3',
        1 => '-13',
        2 => '462',
        3 => '623',
      ),
    ),
    'Zacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zacute',
      'B' => 
      array (
        0 => '-11',
        1 => '0',
        2 => '590',
        3 => '904',
      ),
    ),
    'Zcaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zcaron',
      'B' => 
      array (
        0 => '-11',
        1 => '0',
        2 => '590',
        3 => '897',
      ),
    ),
    'greaterequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'greaterequal',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '523',
        3 => '704',
      ),
    ),
    'Eth' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Eth',
      'B' => 
      array (
        0 => '-31',
        1 => '0',
        2 => '700',
        3 => '669',
      ),
    ),
    'Ccedilla' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ccedilla',
      'B' => 
      array (
        0 => '32',
        1 => '-218',
        2 => '677',
        3 => '685',
      ),
    ),
    'lcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'lcommaaccent',
      'B' => 
      array (
        0 => '-42',
        1 => '-218',
        2 => '290',
        3 => '699',
      ),
    ),
    'tcaron' => 
    array (
      'C' => '-1',
      'WX' => '366',
      'N' => 'tcaron',
      'B' => 
      array (
        0 => '-11',
        1 => '-9',
        2 => '434',
        3 => '754',
      ),
    ),
    'eogonek' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'eogonek',
      'B' => 
      array (
        0 => '5',
        1 => '-183',
        2 => '398',
        3 => '462',
      ),
    ),
    'Uogonek' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uogonek',
      'B' => 
      array (
        0 => '67',
        1 => '-183',
        2 => '744',
        3 => '669',
      ),
    ),
    'Aacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Aacute',
      'B' => 
      array (
        0 => '-67',
        1 => '0',
        2 => '593',
        3 => '904',
      ),
    ),
    'Adieresis' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Adieresis',
      'B' => 
      array (
        0 => '-67',
        1 => '0',
        2 => '593',
        3 => '862',
      ),
    ),
    'egrave' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'egrave',
      'B' => 
      array (
        0 => '5',
        1 => '-13',
        2 => '398',
        3 => '697',
      ),
    ),
    'zacute' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'zacute',
      'B' => 
      array (
        0 => '-43',
        1 => '-78',
        2 => '407',
        3 => '697',
      ),
    ),
    'iogonek' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'iogonek',
      'B' => 
      array (
        0 => '-20',
        1 => '-183',
        2 => '263',
        3 => '684',
      ),
    ),
    'Oacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Oacute',
      'B' => 
      array (
        0 => '27',
        1 => '-18',
        2 => '691',
        3 => '904',
      ),
    ),
    'oacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'oacute',
      'B' => 
      array (
        0 => '-3',
        1 => '-13',
        2 => '463',
        3 => '697',
      ),
    ),
    'amacron' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'amacron',
      'B' => 
      array (
        0 => '-21',
        1 => '-14',
        2 => '467',
        3 => '623',
      ),
    ),
    'sacute' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'sacute',
      'B' => 
      array (
        0 => '-19',
        1 => '-13',
        2 => '407',
        3 => '697',
      ),
    ),
    'idieresis' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'idieresis',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '364',
        3 => '655',
      ),
    ),
    'Ocircumflex' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ocircumflex',
      'B' => 
      array (
        0 => '27',
        1 => '-18',
        2 => '691',
        3 => '897',
      ),
    ),
    'Ugrave' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ugrave',
      'B' => 
      array (
        0 => '67',
        1 => '-18',
        2 => '744',
        3 => '904',
      ),
    ),
    'Delta' => 
    array (
      'C' => '-1',
      'WX' => '612',
      'N' => 'Delta',
      'B' => 
      array (
        0 => '6',
        1 => '0',
        2 => '608',
        3 => '688',
      ),
    ),
    'thorn' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'thorn',
      'B' => 
      array (
        0 => '-120',
        1 => '-205',
        2 => '446',
        3 => '699',
      ),
    ),
    'twosuperior' => 
    array (
      'C' => '-1',
      'WX' => '300',
      'N' => 'twosuperior',
      'B' => 
      array (
        0 => '2',
        1 => '274',
        2 => '313',
        3 => '683',
      ),
    ),
    'Odieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Odieresis',
      'B' => 
      array (
        0 => '27',
        1 => '-18',
        2 => '691',
        3 => '862',
      ),
    ),
    'mu' => 
    array (
      'C' => '-1',
      'WX' => '576',
      'N' => 'mu',
      'B' => 
      array (
        0 => '-60',
        1 => '-207',
        2 => '516',
        3 => '449',
      ),
    ),
    'igrave' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'igrave',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '259',
        3 => '697',
      ),
    ),
    'ohungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ohungarumlaut',
      'B' => 
      array (
        0 => '-3',
        1 => '-13',
        2 => '582',
        3 => '697',
      ),
    ),
    'Eogonek' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Eogonek',
      'B' => 
      array (
        0 => '-27',
        1 => '-183',
        2 => '653',
        3 => '669',
      ),
    ),
    'dcroat' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'dcroat',
      'B' => 
      array (
        0 => '-21',
        1 => '-13',
        2 => '552',
        3 => '699',
      ),
    ),
    'threequarters' => 
    array (
      'C' => '-1',
      'WX' => '750',
      'N' => 'threequarters',
      'B' => 
      array (
        0 => '7',
        1 => '-14',
        2 => '726',
        3 => '683',
      ),
    ),
    'Scedilla' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Scedilla',
      'B' => 
      array (
        0 => '2',
        1 => '-218',
        2 => '526',
        3 => '685',
      ),
    ),
    'lcaron' => 
    array (
      'C' => '-1',
      'WX' => '382',
      'N' => 'lcaron',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '448',
        3 => '708',
      ),
    ),
    'Kcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Kcommaaccent',
      'B' => 
      array (
        0 => '-21',
        1 => '-218',
        2 => '702',
        3 => '669',
      ),
    ),
    'Lacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Lacute',
      'B' => 
      array (
        0 => '-22',
        1 => '0',
        2 => '590',
        3 => '904',
      ),
    ),
    'trademark' => 
    array (
      'C' => '-1',
      'WX' => '1000',
      'N' => 'trademark',
      'B' => 
      array (
        0 => '32',
        1 => '263',
        2 => '968',
        3 => '669',
      ),
    ),
    'edotaccent' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'edotaccent',
      'B' => 
      array (
        0 => '5',
        1 => '-13',
        2 => '398',
        3 => '655',
      ),
    ),
    'Igrave' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Igrave',
      'B' => 
      array (
        0 => '-32',
        1 => '0',
        2 => '406',
        3 => '904',
      ),
    ),
    'Imacron' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Imacron',
      'B' => 
      array (
        0 => '-32',
        1 => '0',
        2 => '461',
        3 => '830',
      ),
    ),
    'Lcaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Lcaron',
      'B' => 
      array (
        0 => '-22',
        1 => '0',
        2 => '671',
        3 => '718',
      ),
    ),
    'onehalf' => 
    array (
      'C' => '-1',
      'WX' => '750',
      'N' => 'onehalf',
      'B' => 
      array (
        0 => '-9',
        1 => '-14',
        2 => '723',
        3 => '683',
      ),
    ),
    'lessequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'lessequal',
      'B' => 
      array (
        0 => '29',
        1 => '0',
        2 => '526',
        3 => '704',
      ),
    ),
    'ocircumflex' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ocircumflex',
      'B' => 
      array (
        0 => '-3',
        1 => '-13',
        2 => '451',
        3 => '690',
      ),
    ),
    'ntilde' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ntilde',
      'B' => 
      array (
        0 => '-6',
        1 => '-9',
        2 => '504',
        3 => '655',
      ),
    ),
    'Uhungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uhungarumlaut',
      'B' => 
      array (
        0 => '67',
        1 => '-18',
        2 => '744',
        3 => '904',
      ),
    ),
    'Eacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Eacute',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '653',
        3 => '904',
      ),
    ),
    'emacron' => 
    array (
      'C' => '-1',
      'WX' => '444',
      'N' => 'emacron',
      'B' => 
      array (
        0 => '5',
        1 => '-13',
        2 => '439',
        3 => '623',
      ),
    ),
    'gbreve' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'gbreve',
      'B' => 
      array (
        0 => '-52',
        1 => '-203',
        2 => '478',
        3 => '678',
      ),
    ),
    'onequarter' => 
    array (
      'C' => '-1',
      'WX' => '750',
      'N' => 'onequarter',
      'B' => 
      array (
        0 => '7',
        1 => '-14',
        2 => '721',
        3 => '683',
      ),
    ),
    'Scaron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Scaron',
      'B' => 
      array (
        0 => '2',
        1 => '-18',
        2 => '553',
        3 => '897',
      ),
    ),
    'Scommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Scommaaccent',
      'B' => 
      array (
        0 => '2',
        1 => '-218',
        2 => '526',
        3 => '685',
      ),
    ),
    'Ohungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ohungarumlaut',
      'B' => 
      array (
        0 => '27',
        1 => '-18',
        2 => '723',
        3 => '904',
      ),
    ),
    'degree' => 
    array (
      'C' => '-1',
      'WX' => '400',
      'N' => 'degree',
      'B' => 
      array (
        0 => '83',
        1 => '397',
        2 => '369',
        3 => '683',
      ),
    ),
    'ograve' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ograve',
      'B' => 
      array (
        0 => '-3',
        1 => '-13',
        2 => '441',
        3 => '697',
      ),
    ),
    'Ccaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ccaron',
      'B' => 
      array (
        0 => '32',
        1 => '-18',
        2 => '677',
        3 => '897',
      ),
    ),
    'ugrave' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ugrave',
      'B' => 
      array (
        0 => '15',
        1 => '-9',
        2 => '492',
        3 => '697',
      ),
    ),
    'radical' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'radical',
      'B' => 
      array (
        0 => '10',
        1 => '-46',
        2 => '512',
        3 => '850',
      ),
    ),
    'Dcaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Dcaron',
      'B' => 
      array (
        0 => '-46',
        1 => '0',
        2 => '685',
        3 => '897',
      ),
    ),
    'rcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'rcommaaccent',
      'B' => 
      array (
        0 => '-67',
        1 => '-218',
        2 => '389',
        3 => '462',
      ),
    ),
    'Ntilde' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ntilde',
      'B' => 
      array (
        0 => '-27',
        1 => '-15',
        2 => '748',
        3 => '862',
      ),
    ),
    'otilde' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'otilde',
      'B' => 
      array (
        0 => '-3',
        1 => '-13',
        2 => '491',
        3 => '655',
      ),
    ),
    'Rcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Rcommaaccent',
      'B' => 
      array (
        0 => '-29',
        1 => '-218',
        2 => '623',
        3 => '669',
      ),
    ),
    'Lcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Lcommaaccent',
      'B' => 
      array (
        0 => '-22',
        1 => '-218',
        2 => '590',
        3 => '669',
      ),
    ),
    'Atilde' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Atilde',
      'B' => 
      array (
        0 => '-67',
        1 => '0',
        2 => '593',
        3 => '862',
      ),
    ),
    'Aogonek' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Aogonek',
      'B' => 
      array (
        0 => '-67',
        1 => '-183',
        2 => '604',
        3 => '683',
      ),
    ),
    'Aring' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Aring',
      'B' => 
      array (
        0 => '-67',
        1 => '0',
        2 => '593',
        3 => '921',
      ),
    ),
    'Otilde' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Otilde',
      'B' => 
      array (
        0 => '27',
        1 => '-18',
        2 => '691',
        3 => '862',
      ),
    ),
    'zdotaccent' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'zdotaccent',
      'B' => 
      array (
        0 => '-43',
        1 => '-78',
        2 => '368',
        3 => '655',
      ),
    ),
    'Ecaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ecaron',
      'B' => 
      array (
        0 => '-27',
        1 => '0',
        2 => '653',
        3 => '897',
      ),
    ),
    'Iogonek' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Iogonek',
      'B' => 
      array (
        0 => '-32',
        1 => '-183',
        2 => '406',
        3 => '669',
      ),
    ),
    'kcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'kcommaaccent',
      'B' => 
      array (
        0 => '-23',
        1 => '-218',
        2 => '483',
        3 => '699',
      ),
    ),
    'minus' => 
    array (
      'C' => '-1',
      'WX' => '606',
      'N' => 'minus',
      'B' => 
      array (
        0 => '51',
        1 => '209',
        2 => '555',
        3 => '297',
      ),
    ),
    'Icircumflex' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'Icircumflex',
      'B' => 
      array (
        0 => '-32',
        1 => '0',
        2 => '450',
        3 => '897',
      ),
    ),
    'ncaron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ncaron',
      'B' => 
      array (
        0 => '-6',
        1 => '-9',
        2 => '523',
        3 => '690',
      ),
    ),
    'tcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'tcommaaccent',
      'B' => 
      array (
        0 => '-62',
        1 => '-218',
        2 => '281',
        3 => '594',
      ),
    ),
    'logicalnot' => 
    array (
      'C' => '-1',
      'WX' => '606',
      'N' => 'logicalnot',
      'B' => 
      array (
        0 => '51',
        1 => '108',
        2 => '555',
        3 => '399',
      ),
    ),
    'odieresis' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'odieresis',
      'B' => 
      array (
        0 => '-3',
        1 => '-13',
        2 => '471',
        3 => '655',
      ),
    ),
    'udieresis' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'udieresis',
      'B' => 
      array (
        0 => '15',
        1 => '-9',
        2 => '499',
        3 => '655',
      ),
    ),
    'notequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'notequal',
      'B' => 
      array (
        0 => '15',
        1 => '-49',
        2 => '540',
        3 => '570',
      ),
    ),
    'gcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'gcommaaccent',
      'B' => 
      array (
        0 => '-52',
        1 => '-203',
        2 => '478',
        3 => '767',
      ),
    ),
    'eth' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'eth',
      'B' => 
      array (
        0 => '-3',
        1 => '-13',
        2 => '454',
        3 => '699',
      ),
    ),
    'zcaron' => 
    array (
      'C' => '-1',
      'WX' => '389',
      'N' => 'zcaron',
      'B' => 
      array (
        0 => '-43',
        1 => '-78',
        2 => '424',
        3 => '690',
      ),
    ),
    'ncommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ncommaaccent',
      'B' => 
      array (
        0 => '-6',
        1 => '-218',
        2 => '493',
        3 => '462',
      ),
    ),
    'onesuperior' => 
    array (
      'C' => '-1',
      'WX' => '300',
      'N' => 'onesuperior',
      'B' => 
      array (
        0 => '30',
        1 => '274',
        2 => '301',
        3 => '683',
      ),
    ),
    'imacron' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'imacron',
      'B' => 
      array (
        0 => '2',
        1 => '-9',
        2 => '294',
        3 => '623',
      ),
    ),
    'Euro' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'Euro',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
  ),
  'KPX' => 
  array (
    'A' => 
    array (
      'C' => '-65',
      'Cacute' => '-65',
      'Ccaron' => '-65',
      'Ccedilla' => '-65',
      'G' => '-60',
      'Gbreve' => '-60',
      'Gcommaaccent' => '-60',
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'Q' => '-55',
      'T' => '-55',
      'Tcaron' => '-55',
      'Tcommaaccent' => '-55',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-95',
      'W' => '-100',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'quoteright' => '-74',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-74',
      'w' => '-74',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Aacute' => 
    array (
      'C' => '-65',
      'Cacute' => '-65',
      'Ccaron' => '-65',
      'Ccedilla' => '-65',
      'G' => '-60',
      'Gbreve' => '-60',
      'Gcommaaccent' => '-60',
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'Q' => '-55',
      'T' => '-55',
      'Tcaron' => '-55',
      'Tcommaaccent' => '-55',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-95',
      'W' => '-100',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'quoteright' => '-74',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-74',
      'w' => '-74',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Abreve' => 
    array (
      'C' => '-65',
      'Cacute' => '-65',
      'Ccaron' => '-65',
      'Ccedilla' => '-65',
      'G' => '-60',
      'Gbreve' => '-60',
      'Gcommaaccent' => '-60',
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'Q' => '-55',
      'T' => '-55',
      'Tcaron' => '-55',
      'Tcommaaccent' => '-55',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-95',
      'W' => '-100',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'quoteright' => '-74',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-74',
      'w' => '-74',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Acircumflex' => 
    array (
      'C' => '-65',
      'Cacute' => '-65',
      'Ccaron' => '-65',
      'Ccedilla' => '-65',
      'G' => '-60',
      'Gbreve' => '-60',
      'Gcommaaccent' => '-60',
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'Q' => '-55',
      'T' => '-55',
      'Tcaron' => '-55',
      'Tcommaaccent' => '-55',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-95',
      'W' => '-100',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'quoteright' => '-74',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-74',
      'w' => '-74',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Adieresis' => 
    array (
      'C' => '-65',
      'Cacute' => '-65',
      'Ccaron' => '-65',
      'Ccedilla' => '-65',
      'G' => '-60',
      'Gbreve' => '-60',
      'Gcommaaccent' => '-60',
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'Q' => '-55',
      'T' => '-55',
      'Tcaron' => '-55',
      'Tcommaaccent' => '-55',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-95',
      'W' => '-100',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'quoteright' => '-74',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-74',
      'w' => '-74',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Agrave' => 
    array (
      'C' => '-65',
      'Cacute' => '-65',
      'Ccaron' => '-65',
      'Ccedilla' => '-65',
      'G' => '-60',
      'Gbreve' => '-60',
      'Gcommaaccent' => '-60',
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'Q' => '-55',
      'T' => '-55',
      'Tcaron' => '-55',
      'Tcommaaccent' => '-55',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-95',
      'W' => '-100',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'quoteright' => '-74',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-74',
      'w' => '-74',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Amacron' => 
    array (
      'C' => '-65',
      'Cacute' => '-65',
      'Ccaron' => '-65',
      'Ccedilla' => '-65',
      'G' => '-60',
      'Gbreve' => '-60',
      'Gcommaaccent' => '-60',
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'Q' => '-55',
      'T' => '-55',
      'Tcaron' => '-55',
      'Tcommaaccent' => '-55',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-95',
      'W' => '-100',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'quoteright' => '-74',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-74',
      'w' => '-74',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Aogonek' => 
    array (
      'C' => '-65',
      'Cacute' => '-65',
      'Ccaron' => '-65',
      'Ccedilla' => '-65',
      'G' => '-60',
      'Gbreve' => '-60',
      'Gcommaaccent' => '-60',
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'Q' => '-55',
      'T' => '-55',
      'Tcaron' => '-55',
      'Tcommaaccent' => '-55',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-95',
      'W' => '-100',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'quoteright' => '-74',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-74',
      'w' => '-74',
      'y' => '-34',
      'yacute' => '-34',
      'ydieresis' => '-34',
    ),
    'Aring' => 
    array (
      'C' => '-65',
      'Cacute' => '-65',
      'Ccaron' => '-65',
      'Ccedilla' => '-65',
      'G' => '-60',
      'Gbreve' => '-60',
      'Gcommaaccent' => '-60',
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'Q' => '-55',
      'T' => '-55',
      'Tcaron' => '-55',
      'Tcommaaccent' => '-55',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-95',
      'W' => '-100',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'quoteright' => '-74',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-74',
      'w' => '-74',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'Atilde' => 
    array (
      'C' => '-65',
      'Cacute' => '-65',
      'Ccaron' => '-65',
      'Ccedilla' => '-65',
      'G' => '-60',
      'Gbreve' => '-60',
      'Gcommaaccent' => '-60',
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'Q' => '-55',
      'T' => '-55',
      'Tcaron' => '-55',
      'Tcommaaccent' => '-55',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-95',
      'W' => '-100',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'quoteright' => '-74',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-74',
      'w' => '-74',
      'y' => '-74',
      'yacute' => '-74',
      'ydieresis' => '-74',
    ),
    'B' => 
    array (
      'A' => '-25',
      'Aacute' => '-25',
      'Abreve' => '-25',
      'Acircumflex' => '-25',
      'Adieresis' => '-25',
      'Agrave' => '-25',
      'Amacron' => '-25',
      'Aogonek' => '-25',
      'Aring' => '-25',
      'Atilde' => '-25',
      'U' => '-10',
      'Uacute' => '-10',
      'Ucircumflex' => '-10',
      'Udieresis' => '-10',
      'Ugrave' => '-10',
      'Uhungarumlaut' => '-10',
      'Umacron' => '-10',
      'Uogonek' => '-10',
      'Uring' => '-10',
    ),
    'D' => 
    array (
      'A' => '-25',
      'Aacute' => '-25',
      'Abreve' => '-25',
      'Acircumflex' => '-25',
      'Adieresis' => '-25',
      'Agrave' => '-25',
      'Amacron' => '-25',
      'Aogonek' => '-25',
      'Aring' => '-25',
      'Atilde' => '-25',
      'V' => '-50',
      'W' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Dcaron' => 
    array (
      'A' => '-25',
      'Aacute' => '-25',
      'Abreve' => '-25',
      'Acircumflex' => '-25',
      'Adieresis' => '-25',
      'Agrave' => '-25',
      'Amacron' => '-25',
      'Aogonek' => '-25',
      'Aring' => '-25',
      'Atilde' => '-25',
      'V' => '-50',
      'W' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Dcroat' => 
    array (
      'A' => '-25',
      'Aacute' => '-25',
      'Abreve' => '-25',
      'Acircumflex' => '-25',
      'Adieresis' => '-25',
      'Agrave' => '-25',
      'Amacron' => '-25',
      'Aogonek' => '-25',
      'Aring' => '-25',
      'Atilde' => '-25',
      'V' => '-50',
      'W' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'F' => 
    array (
      'A' => '-100',
      'Aacute' => '-100',
      'Abreve' => '-100',
      'Acircumflex' => '-100',
      'Adieresis' => '-100',
      'Agrave' => '-100',
      'Amacron' => '-100',
      'Aogonek' => '-100',
      'Aring' => '-100',
      'Atilde' => '-100',
      'a' => '-95',
      'aacute' => '-95',
      'abreve' => '-95',
      'acircumflex' => '-95',
      'adieresis' => '-95',
      'agrave' => '-95',
      'amacron' => '-95',
      'aogonek' => '-95',
      'aring' => '-95',
      'atilde' => '-95',
      'comma' => '-129',
      'e' => '-100',
      'eacute' => '-100',
      'ecaron' => '-100',
      'ecircumflex' => '-100',
      'edieresis' => '-100',
      'edotaccent' => '-100',
      'egrave' => '-100',
      'emacron' => '-100',
      'eogonek' => '-100',
      'i' => '-40',
      'iacute' => '-40',
      'icircumflex' => '-40',
      'idieresis' => '-40',
      'igrave' => '-40',
      'imacron' => '-40',
      'iogonek' => '-40',
      'o' => '-70',
      'oacute' => '-70',
      'ocircumflex' => '-70',
      'odieresis' => '-70',
      'ograve' => '-70',
      'ohungarumlaut' => '-70',
      'omacron' => '-70',
      'oslash' => '-70',
      'otilde' => '-70',
      'period' => '-129',
      'r' => '-50',
      'racute' => '-50',
      'rcaron' => '-50',
      'rcommaaccent' => '-50',
    ),
    'J' => 
    array (
      'A' => '-25',
      'Aacute' => '-25',
      'Abreve' => '-25',
      'Acircumflex' => '-25',
      'Adieresis' => '-25',
      'Agrave' => '-25',
      'Amacron' => '-25',
      'Aogonek' => '-25',
      'Aring' => '-25',
      'Atilde' => '-25',
      'a' => '-40',
      'aacute' => '-40',
      'abreve' => '-40',
      'acircumflex' => '-40',
      'adieresis' => '-40',
      'agrave' => '-40',
      'amacron' => '-40',
      'aogonek' => '-40',
      'aring' => '-40',
      'atilde' => '-40',
      'comma' => '-10',
      'e' => '-40',
      'eacute' => '-40',
      'ecaron' => '-40',
      'ecircumflex' => '-40',
      'edieresis' => '-40',
      'edotaccent' => '-40',
      'egrave' => '-40',
      'emacron' => '-40',
      'eogonek' => '-40',
      'o' => '-40',
      'oacute' => '-40',
      'ocircumflex' => '-40',
      'odieresis' => '-40',
      'ograve' => '-40',
      'ohungarumlaut' => '-40',
      'omacron' => '-40',
      'oslash' => '-40',
      'otilde' => '-40',
      'period' => '-10',
      'u' => '-40',
      'uacute' => '-40',
      'ucircumflex' => '-40',
      'udieresis' => '-40',
      'ugrave' => '-40',
      'uhungarumlaut' => '-40',
      'umacron' => '-40',
      'uogonek' => '-40',
      'uring' => '-40',
    ),
    'K' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'e' => '-25',
      'eacute' => '-25',
      'ecaron' => '-25',
      'ecircumflex' => '-25',
      'edieresis' => '-25',
      'edotaccent' => '-25',
      'egrave' => '-25',
      'emacron' => '-25',
      'eogonek' => '-25',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'u' => '-20',
      'uacute' => '-20',
      'ucircumflex' => '-20',
      'udieresis' => '-20',
      'ugrave' => '-20',
      'uhungarumlaut' => '-20',
      'umacron' => '-20',
      'uogonek' => '-20',
      'uring' => '-20',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'Kcommaaccent' => 
    array (
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'e' => '-25',
      'eacute' => '-25',
      'ecaron' => '-25',
      'ecircumflex' => '-25',
      'edieresis' => '-25',
      'edotaccent' => '-25',
      'egrave' => '-25',
      'emacron' => '-25',
      'eogonek' => '-25',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'u' => '-20',
      'uacute' => '-20',
      'ucircumflex' => '-20',
      'udieresis' => '-20',
      'ugrave' => '-20',
      'uhungarumlaut' => '-20',
      'umacron' => '-20',
      'uogonek' => '-20',
      'uring' => '-20',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'L' => 
    array (
      'T' => '-18',
      'Tcaron' => '-18',
      'Tcommaaccent' => '-18',
      'V' => '-37',
      'W' => '-37',
      'Y' => '-37',
      'Yacute' => '-37',
      'Ydieresis' => '-37',
      'quoteright' => '-55',
      'y' => '-37',
      'yacute' => '-37',
      'ydieresis' => '-37',
    ),
    'Lacute' => 
    array (
      'T' => '-18',
      'Tcaron' => '-18',
      'Tcommaaccent' => '-18',
      'V' => '-37',
      'W' => '-37',
      'Y' => '-37',
      'Yacute' => '-37',
      'Ydieresis' => '-37',
      'quoteright' => '-55',
      'y' => '-37',
      'yacute' => '-37',
      'ydieresis' => '-37',
    ),
    'Lcommaaccent' => 
    array (
      'T' => '-18',
      'Tcaron' => '-18',
      'Tcommaaccent' => '-18',
      'V' => '-37',
      'W' => '-37',
      'Y' => '-37',
      'Yacute' => '-37',
      'Ydieresis' => '-37',
      'quoteright' => '-55',
      'y' => '-37',
      'yacute' => '-37',
      'ydieresis' => '-37',
    ),
    'Lslash' => 
    array (
      'T' => '-18',
      'Tcaron' => '-18',
      'Tcommaaccent' => '-18',
      'V' => '-37',
      'W' => '-37',
      'Y' => '-37',
      'Yacute' => '-37',
      'Ydieresis' => '-37',
      'quoteright' => '-55',
      'y' => '-37',
      'yacute' => '-37',
      'ydieresis' => '-37',
    ),
    'N' => 
    array (
      'A' => '-30',
      'Aacute' => '-30',
      'Abreve' => '-30',
      'Acircumflex' => '-30',
      'Adieresis' => '-30',
      'Agrave' => '-30',
      'Amacron' => '-30',
      'Aogonek' => '-30',
      'Aring' => '-30',
      'Atilde' => '-30',
    ),
    'Nacute' => 
    array (
      'A' => '-30',
      'Aacute' => '-30',
      'Abreve' => '-30',
      'Acircumflex' => '-30',
      'Adieresis' => '-30',
      'Agrave' => '-30',
      'Amacron' => '-30',
      'Aogonek' => '-30',
      'Aring' => '-30',
      'Atilde' => '-30',
    ),
    'Ncaron' => 
    array (
      'A' => '-30',
      'Aacute' => '-30',
      'Abreve' => '-30',
      'Acircumflex' => '-30',
      'Adieresis' => '-30',
      'Agrave' => '-30',
      'Amacron' => '-30',
      'Aogonek' => '-30',
      'Aring' => '-30',
      'Atilde' => '-30',
    ),
    'Ncommaaccent' => 
    array (
      'A' => '-30',
      'Aacute' => '-30',
      'Abreve' => '-30',
      'Acircumflex' => '-30',
      'Adieresis' => '-30',
      'Agrave' => '-30',
      'Amacron' => '-30',
      'Aogonek' => '-30',
      'Aring' => '-30',
      'Atilde' => '-30',
    ),
    'Ntilde' => 
    array (
      'A' => '-30',
      'Aacute' => '-30',
      'Abreve' => '-30',
      'Acircumflex' => '-30',
      'Adieresis' => '-30',
      'Agrave' => '-30',
      'Amacron' => '-30',
      'Aogonek' => '-30',
      'Aring' => '-30',
      'Atilde' => '-30',
    ),
    'O' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Oacute' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Ocircumflex' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Odieresis' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Ograve' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Ohungarumlaut' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Omacron' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Oslash' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Otilde' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-50',
      'X' => '-40',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'P' => 
    array (
      'A' => '-85',
      'Aacute' => '-85',
      'Abreve' => '-85',
      'Acircumflex' => '-85',
      'Adieresis' => '-85',
      'Agrave' => '-85',
      'Amacron' => '-85',
      'Aogonek' => '-85',
      'Aring' => '-85',
      'Atilde' => '-85',
      'a' => '-40',
      'aacute' => '-40',
      'abreve' => '-40',
      'acircumflex' => '-40',
      'adieresis' => '-40',
      'agrave' => '-40',
      'amacron' => '-40',
      'aogonek' => '-40',
      'aring' => '-40',
      'atilde' => '-40',
      'comma' => '-129',
      'e' => '-50',
      'eacute' => '-50',
      'ecaron' => '-50',
      'ecircumflex' => '-50',
      'edieresis' => '-50',
      'edotaccent' => '-50',
      'egrave' => '-50',
      'emacron' => '-50',
      'eogonek' => '-50',
      'o' => '-55',
      'oacute' => '-55',
      'ocircumflex' => '-55',
      'odieresis' => '-55',
      'ograve' => '-55',
      'ohungarumlaut' => '-55',
      'omacron' => '-55',
      'oslash' => '-55',
      'otilde' => '-55',
      'period' => '-129',
    ),
    'Q' => 
    array (
      'U' => '-10',
      'Uacute' => '-10',
      'Ucircumflex' => '-10',
      'Udieresis' => '-10',
      'Ugrave' => '-10',
      'Uhungarumlaut' => '-10',
      'Umacron' => '-10',
      'Uogonek' => '-10',
      'Uring' => '-10',
    ),
    'R' => 
    array (
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'T' => '-30',
      'Tcaron' => '-30',
      'Tcommaaccent' => '-30',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-18',
      'W' => '-18',
      'Y' => '-18',
      'Yacute' => '-18',
      'Ydieresis' => '-18',
    ),
    'Racute' => 
    array (
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'T' => '-30',
      'Tcaron' => '-30',
      'Tcommaaccent' => '-30',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-18',
      'W' => '-18',
      'Y' => '-18',
      'Yacute' => '-18',
      'Ydieresis' => '-18',
    ),
    'Rcaron' => 
    array (
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'T' => '-30',
      'Tcaron' => '-30',
      'Tcommaaccent' => '-30',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-18',
      'W' => '-18',
      'Y' => '-18',
      'Yacute' => '-18',
      'Ydieresis' => '-18',
    ),
    'Rcommaaccent' => 
    array (
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'T' => '-30',
      'Tcaron' => '-30',
      'Tcommaaccent' => '-30',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-18',
      'W' => '-18',
      'Y' => '-18',
      'Yacute' => '-18',
      'Ydieresis' => '-18',
    ),
    'T' => 
    array (
      'A' => '-55',
      'Aacute' => '-55',
      'Abreve' => '-55',
      'Acircumflex' => '-55',
      'Adieresis' => '-55',
      'Agrave' => '-55',
      'Amacron' => '-55',
      'Aogonek' => '-55',
      'Aring' => '-55',
      'Atilde' => '-55',
      'O' => '-18',
      'Oacute' => '-18',
      'Ocircumflex' => '-18',
      'Odieresis' => '-18',
      'Ograve' => '-18',
      'Ohungarumlaut' => '-18',
      'Omacron' => '-18',
      'Oslash' => '-18',
      'Otilde' => '-18',
      'a' => '-92',
      'aacute' => '-92',
      'abreve' => '-92',
      'acircumflex' => '-92',
      'adieresis' => '-92',
      'agrave' => '-92',
      'amacron' => '-92',
      'aogonek' => '-92',
      'aring' => '-92',
      'atilde' => '-92',
      'colon' => '-74',
      'comma' => '-92',
      'e' => '-92',
      'eacute' => '-92',
      'ecaron' => '-92',
      'ecircumflex' => '-92',
      'edieresis' => '-52',
      'edotaccent' => '-92',
      'egrave' => '-52',
      'emacron' => '-52',
      'eogonek' => '-92',
      'hyphen' => '-92',
      'i' => '-37',
      'iacute' => '-37',
      'iogonek' => '-37',
      'o' => '-95',
      'oacute' => '-95',
      'ocircumflex' => '-95',
      'odieresis' => '-95',
      'ograve' => '-95',
      'ohungarumlaut' => '-95',
      'omacron' => '-95',
      'oslash' => '-95',
      'otilde' => '-95',
      'period' => '-92',
      'r' => '-37',
      'racute' => '-37',
      'rcaron' => '-37',
      'rcommaaccent' => '-37',
      'semicolon' => '-74',
      'u' => '-37',
      'uacute' => '-37',
      'ucircumflex' => '-37',
      'udieresis' => '-37',
      'ugrave' => '-37',
      'uhungarumlaut' => '-37',
      'umacron' => '-37',
      'uogonek' => '-37',
      'uring' => '-37',
      'w' => '-37',
      'y' => '-37',
      'yacute' => '-37',
      'ydieresis' => '-37',
    ),
    'Tcaron' => 
    array (
      'A' => '-55',
      'Aacute' => '-55',
      'Abreve' => '-55',
      'Acircumflex' => '-55',
      'Adieresis' => '-55',
      'Agrave' => '-55',
      'Amacron' => '-55',
      'Aogonek' => '-55',
      'Aring' => '-55',
      'Atilde' => '-55',
      'O' => '-18',
      'Oacute' => '-18',
      'Ocircumflex' => '-18',
      'Odieresis' => '-18',
      'Ograve' => '-18',
      'Ohungarumlaut' => '-18',
      'Omacron' => '-18',
      'Oslash' => '-18',
      'Otilde' => '-18',
      'a' => '-92',
      'aacute' => '-92',
      'abreve' => '-92',
      'acircumflex' => '-92',
      'adieresis' => '-92',
      'agrave' => '-92',
      'amacron' => '-92',
      'aogonek' => '-92',
      'aring' => '-92',
      'atilde' => '-92',
      'colon' => '-74',
      'comma' => '-92',
      'e' => '-92',
      'eacute' => '-92',
      'ecaron' => '-92',
      'ecircumflex' => '-92',
      'edieresis' => '-52',
      'edotaccent' => '-92',
      'egrave' => '-52',
      'emacron' => '-52',
      'eogonek' => '-92',
      'hyphen' => '-92',
      'i' => '-37',
      'iacute' => '-37',
      'iogonek' => '-37',
      'o' => '-95',
      'oacute' => '-95',
      'ocircumflex' => '-95',
      'odieresis' => '-95',
      'ograve' => '-95',
      'ohungarumlaut' => '-95',
      'omacron' => '-95',
      'oslash' => '-95',
      'otilde' => '-95',
      'period' => '-92',
      'r' => '-37',
      'racute' => '-37',
      'rcaron' => '-37',
      'rcommaaccent' => '-37',
      'semicolon' => '-74',
      'u' => '-37',
      'uacute' => '-37',
      'ucircumflex' => '-37',
      'udieresis' => '-37',
      'ugrave' => '-37',
      'uhungarumlaut' => '-37',
      'umacron' => '-37',
      'uogonek' => '-37',
      'uring' => '-37',
      'w' => '-37',
      'y' => '-37',
      'yacute' => '-37',
      'ydieresis' => '-37',
    ),
    'Tcommaaccent' => 
    array (
      'A' => '-55',
      'Aacute' => '-55',
      'Abreve' => '-55',
      'Acircumflex' => '-55',
      'Adieresis' => '-55',
      'Agrave' => '-55',
      'Amacron' => '-55',
      'Aogonek' => '-55',
      'Aring' => '-55',
      'Atilde' => '-55',
      'O' => '-18',
      'Oacute' => '-18',
      'Ocircumflex' => '-18',
      'Odieresis' => '-18',
      'Ograve' => '-18',
      'Ohungarumlaut' => '-18',
      'Omacron' => '-18',
      'Oslash' => '-18',
      'Otilde' => '-18',
      'a' => '-92',
      'aacute' => '-92',
      'abreve' => '-92',
      'acircumflex' => '-92',
      'adieresis' => '-92',
      'agrave' => '-92',
      'amacron' => '-92',
      'aogonek' => '-92',
      'aring' => '-92',
      'atilde' => '-92',
      'colon' => '-74',
      'comma' => '-92',
      'e' => '-92',
      'eacute' => '-92',
      'ecaron' => '-92',
      'ecircumflex' => '-92',
      'edieresis' => '-52',
      'edotaccent' => '-92',
      'egrave' => '-52',
      'emacron' => '-52',
      'eogonek' => '-92',
      'hyphen' => '-92',
      'i' => '-37',
      'iacute' => '-37',
      'iogonek' => '-37',
      'o' => '-95',
      'oacute' => '-95',
      'ocircumflex' => '-95',
      'odieresis' => '-95',
      'ograve' => '-95',
      'ohungarumlaut' => '-95',
      'omacron' => '-95',
      'oslash' => '-95',
      'otilde' => '-95',
      'period' => '-92',
      'r' => '-37',
      'racute' => '-37',
      'rcaron' => '-37',
      'rcommaaccent' => '-37',
      'semicolon' => '-74',
      'u' => '-37',
      'uacute' => '-37',
      'ucircumflex' => '-37',
      'udieresis' => '-37',
      'ugrave' => '-37',
      'uhungarumlaut' => '-37',
      'umacron' => '-37',
      'uogonek' => '-37',
      'uring' => '-37',
      'w' => '-37',
      'y' => '-37',
      'yacute' => '-37',
      'ydieresis' => '-37',
    ),
    'U' => 
    array (
      'A' => '-45',
      'Aacute' => '-45',
      'Abreve' => '-45',
      'Acircumflex' => '-45',
      'Adieresis' => '-45',
      'Agrave' => '-45',
      'Amacron' => '-45',
      'Aogonek' => '-45',
      'Aring' => '-45',
      'Atilde' => '-45',
    ),
    'Uacute' => 
    array (
      'A' => '-45',
      'Aacute' => '-45',
      'Abreve' => '-45',
      'Acircumflex' => '-45',
      'Adieresis' => '-45',
      'Agrave' => '-45',
      'Amacron' => '-45',
      'Aogonek' => '-45',
      'Aring' => '-45',
      'Atilde' => '-45',
    ),
    'Ucircumflex' => 
    array (
      'A' => '-45',
      'Aacute' => '-45',
      'Abreve' => '-45',
      'Acircumflex' => '-45',
      'Adieresis' => '-45',
      'Agrave' => '-45',
      'Amacron' => '-45',
      'Aogonek' => '-45',
      'Aring' => '-45',
      'Atilde' => '-45',
    ),
    'Udieresis' => 
    array (
      'A' => '-45',
      'Aacute' => '-45',
      'Abreve' => '-45',
      'Acircumflex' => '-45',
      'Adieresis' => '-45',
      'Agrave' => '-45',
      'Amacron' => '-45',
      'Aogonek' => '-45',
      'Aring' => '-45',
      'Atilde' => '-45',
    ),
    'Ugrave' => 
    array (
      'A' => '-45',
      'Aacute' => '-45',
      'Abreve' => '-45',
      'Acircumflex' => '-45',
      'Adieresis' => '-45',
      'Agrave' => '-45',
      'Amacron' => '-45',
      'Aogonek' => '-45',
      'Aring' => '-45',
      'Atilde' => '-45',
    ),
    'Uhungarumlaut' => 
    array (
      'A' => '-45',
      'Aacute' => '-45',
      'Abreve' => '-45',
      'Acircumflex' => '-45',
      'Adieresis' => '-45',
      'Agrave' => '-45',
      'Amacron' => '-45',
      'Aogonek' => '-45',
      'Aring' => '-45',
      'Atilde' => '-45',
    ),
    'Umacron' => 
    array (
      'A' => '-45',
      'Aacute' => '-45',
      'Abreve' => '-45',
      'Acircumflex' => '-45',
      'Adieresis' => '-45',
      'Agrave' => '-45',
      'Amacron' => '-45',
      'Aogonek' => '-45',
      'Aring' => '-45',
      'Atilde' => '-45',
    ),
    'Uogonek' => 
    array (
      'A' => '-45',
      'Aacute' => '-45',
      'Abreve' => '-45',
      'Acircumflex' => '-45',
      'Adieresis' => '-45',
      'Agrave' => '-45',
      'Amacron' => '-45',
      'Aogonek' => '-45',
      'Aring' => '-45',
      'Atilde' => '-45',
    ),
    'Uring' => 
    array (
      'A' => '-45',
      'Aacute' => '-45',
      'Abreve' => '-45',
      'Acircumflex' => '-45',
      'Adieresis' => '-45',
      'Agrave' => '-45',
      'Amacron' => '-45',
      'Aogonek' => '-45',
      'Aring' => '-45',
      'Atilde' => '-45',
    ),
    'V' => 
    array (
      'A' => '-85',
      'Aacute' => '-85',
      'Abreve' => '-85',
      'Acircumflex' => '-85',
      'Adieresis' => '-85',
      'Agrave' => '-85',
      'Amacron' => '-85',
      'Aogonek' => '-85',
      'Aring' => '-85',
      'Atilde' => '-85',
      'G' => '-10',
      'Gbreve' => '-10',
      'Gcommaaccent' => '-10',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'a' => '-111',
      'aacute' => '-111',
      'abreve' => '-111',
      'acircumflex' => '-111',
      'adieresis' => '-111',
      'agrave' => '-111',
      'amacron' => '-111',
      'aogonek' => '-111',
      'aring' => '-111',
      'atilde' => '-111',
      'colon' => '-74',
      'comma' => '-129',
      'e' => '-111',
      'eacute' => '-111',
      'ecaron' => '-111',
      'ecircumflex' => '-111',
      'edieresis' => '-71',
      'edotaccent' => '-111',
      'egrave' => '-71',
      'emacron' => '-71',
      'eogonek' => '-111',
      'hyphen' => '-70',
      'i' => '-55',
      'iacute' => '-55',
      'iogonek' => '-55',
      'o' => '-111',
      'oacute' => '-111',
      'ocircumflex' => '-111',
      'odieresis' => '-111',
      'ograve' => '-111',
      'ohungarumlaut' => '-111',
      'omacron' => '-111',
      'oslash' => '-111',
      'otilde' => '-111',
      'period' => '-129',
      'semicolon' => '-74',
      'u' => '-55',
      'uacute' => '-55',
      'ucircumflex' => '-55',
      'udieresis' => '-55',
      'ugrave' => '-55',
      'uhungarumlaut' => '-55',
      'umacron' => '-55',
      'uogonek' => '-55',
      'uring' => '-55',
    ),
    'W' => 
    array (
      'A' => '-74',
      'Aacute' => '-74',
      'Abreve' => '-74',
      'Acircumflex' => '-74',
      'Adieresis' => '-74',
      'Agrave' => '-74',
      'Amacron' => '-74',
      'Aogonek' => '-74',
      'Aring' => '-74',
      'Atilde' => '-74',
      'O' => '-15',
      'Oacute' => '-15',
      'Ocircumflex' => '-15',
      'Odieresis' => '-15',
      'Ograve' => '-15',
      'Ohungarumlaut' => '-15',
      'Omacron' => '-15',
      'Oslash' => '-15',
      'Otilde' => '-15',
      'a' => '-85',
      'aacute' => '-85',
      'abreve' => '-85',
      'acircumflex' => '-85',
      'adieresis' => '-85',
      'agrave' => '-85',
      'amacron' => '-85',
      'aogonek' => '-85',
      'aring' => '-85',
      'atilde' => '-85',
      'colon' => '-55',
      'comma' => '-74',
      'e' => '-90',
      'eacute' => '-90',
      'ecaron' => '-90',
      'ecircumflex' => '-90',
      'edieresis' => '-50',
      'edotaccent' => '-90',
      'egrave' => '-50',
      'emacron' => '-50',
      'eogonek' => '-90',
      'hyphen' => '-50',
      'i' => '-37',
      'iacute' => '-37',
      'iogonek' => '-37',
      'o' => '-80',
      'oacute' => '-80',
      'ocircumflex' => '-80',
      'odieresis' => '-80',
      'ograve' => '-80',
      'ohungarumlaut' => '-80',
      'omacron' => '-80',
      'oslash' => '-80',
      'otilde' => '-80',
      'period' => '-74',
      'semicolon' => '-55',
      'u' => '-55',
      'uacute' => '-55',
      'ucircumflex' => '-55',
      'udieresis' => '-55',
      'ugrave' => '-55',
      'uhungarumlaut' => '-55',
      'umacron' => '-55',
      'uogonek' => '-55',
      'uring' => '-55',
      'y' => '-55',
      'yacute' => '-55',
      'ydieresis' => '-55',
    ),
    'Y' => 
    array (
      'A' => '-74',
      'Aacute' => '-74',
      'Abreve' => '-74',
      'Acircumflex' => '-74',
      'Adieresis' => '-74',
      'Agrave' => '-74',
      'Amacron' => '-74',
      'Aogonek' => '-74',
      'Aring' => '-74',
      'Atilde' => '-74',
      'O' => '-25',
      'Oacute' => '-25',
      'Ocircumflex' => '-25',
      'Odieresis' => '-25',
      'Ograve' => '-25',
      'Ohungarumlaut' => '-25',
      'Omacron' => '-25',
      'Oslash' => '-25',
      'Otilde' => '-25',
      'a' => '-92',
      'aacute' => '-92',
      'abreve' => '-92',
      'acircumflex' => '-92',
      'adieresis' => '-92',
      'agrave' => '-92',
      'amacron' => '-92',
      'aogonek' => '-92',
      'aring' => '-92',
      'atilde' => '-92',
      'colon' => '-92',
      'comma' => '-92',
      'e' => '-111',
      'eacute' => '-111',
      'ecaron' => '-111',
      'ecircumflex' => '-71',
      'edieresis' => '-71',
      'edotaccent' => '-111',
      'egrave' => '-71',
      'emacron' => '-71',
      'eogonek' => '-111',
      'hyphen' => '-92',
      'i' => '-55',
      'iacute' => '-55',
      'iogonek' => '-55',
      'o' => '-111',
      'oacute' => '-111',
      'ocircumflex' => '-111',
      'odieresis' => '-111',
      'ograve' => '-111',
      'ohungarumlaut' => '-111',
      'omacron' => '-111',
      'oslash' => '-111',
      'otilde' => '-111',
      'period' => '-74',
      'semicolon' => '-92',
      'u' => '-92',
      'uacute' => '-92',
      'ucircumflex' => '-92',
      'udieresis' => '-92',
      'ugrave' => '-92',
      'uhungarumlaut' => '-92',
      'umacron' => '-92',
      'uogonek' => '-92',
      'uring' => '-92',
    ),
    'Yacute' => 
    array (
      'A' => '-74',
      'Aacute' => '-74',
      'Abreve' => '-74',
      'Acircumflex' => '-74',
      'Adieresis' => '-74',
      'Agrave' => '-74',
      'Amacron' => '-74',
      'Aogonek' => '-74',
      'Aring' => '-74',
      'Atilde' => '-74',
      'O' => '-25',
      'Oacute' => '-25',
      'Ocircumflex' => '-25',
      'Odieresis' => '-25',
      'Ograve' => '-25',
      'Ohungarumlaut' => '-25',
      'Omacron' => '-25',
      'Oslash' => '-25',
      'Otilde' => '-25',
      'a' => '-92',
      'aacute' => '-92',
      'abreve' => '-92',
      'acircumflex' => '-92',
      'adieresis' => '-92',
      'agrave' => '-92',
      'amacron' => '-92',
      'aogonek' => '-92',
      'aring' => '-92',
      'atilde' => '-92',
      'colon' => '-92',
      'comma' => '-92',
      'e' => '-111',
      'eacute' => '-111',
      'ecaron' => '-111',
      'ecircumflex' => '-71',
      'edieresis' => '-71',
      'edotaccent' => '-111',
      'egrave' => '-71',
      'emacron' => '-71',
      'eogonek' => '-111',
      'hyphen' => '-92',
      'i' => '-55',
      'iacute' => '-55',
      'iogonek' => '-55',
      'o' => '-111',
      'oacute' => '-111',
      'ocircumflex' => '-111',
      'odieresis' => '-111',
      'ograve' => '-111',
      'ohungarumlaut' => '-111',
      'omacron' => '-111',
      'oslash' => '-111',
      'otilde' => '-111',
      'period' => '-74',
      'semicolon' => '-92',
      'u' => '-92',
      'uacute' => '-92',
      'ucircumflex' => '-92',
      'udieresis' => '-92',
      'ugrave' => '-92',
      'uhungarumlaut' => '-92',
      'umacron' => '-92',
      'uogonek' => '-92',
      'uring' => '-92',
    ),
    'Ydieresis' => 
    array (
      'A' => '-74',
      'Aacute' => '-74',
      'Abreve' => '-74',
      'Acircumflex' => '-74',
      'Adieresis' => '-74',
      'Agrave' => '-74',
      'Amacron' => '-74',
      'Aogonek' => '-74',
      'Aring' => '-74',
      'Atilde' => '-74',
      'O' => '-25',
      'Oacute' => '-25',
      'Ocircumflex' => '-25',
      'Odieresis' => '-25',
      'Ograve' => '-25',
      'Ohungarumlaut' => '-25',
      'Omacron' => '-25',
      'Oslash' => '-25',
      'Otilde' => '-25',
      'a' => '-92',
      'aacute' => '-92',
      'abreve' => '-92',
      'acircumflex' => '-92',
      'adieresis' => '-92',
      'agrave' => '-92',
      'amacron' => '-92',
      'aogonek' => '-92',
      'aring' => '-92',
      'atilde' => '-92',
      'colon' => '-92',
      'comma' => '-92',
      'e' => '-111',
      'eacute' => '-111',
      'ecaron' => '-111',
      'ecircumflex' => '-71',
      'edieresis' => '-71',
      'edotaccent' => '-111',
      'egrave' => '-71',
      'emacron' => '-71',
      'eogonek' => '-111',
      'hyphen' => '-92',
      'i' => '-55',
      'iacute' => '-55',
      'iogonek' => '-55',
      'o' => '-111',
      'oacute' => '-111',
      'ocircumflex' => '-111',
      'odieresis' => '-111',
      'ograve' => '-111',
      'ohungarumlaut' => '-111',
      'omacron' => '-111',
      'oslash' => '-111',
      'otilde' => '-111',
      'period' => '-74',
      'semicolon' => '-92',
      'u' => '-92',
      'uacute' => '-92',
      'ucircumflex' => '-92',
      'udieresis' => '-92',
      'ugrave' => '-92',
      'uhungarumlaut' => '-92',
      'umacron' => '-92',
      'uogonek' => '-92',
      'uring' => '-92',
    ),
    'b' => 
    array (
      'b' => '-10',
      'period' => '-40',
      'u' => '-20',
      'uacute' => '-20',
      'ucircumflex' => '-20',
      'udieresis' => '-20',
      'ugrave' => '-20',
      'uhungarumlaut' => '-20',
      'umacron' => '-20',
      'uogonek' => '-20',
      'uring' => '-20',
    ),
    'c' => 
    array (
      'h' => '-10',
      'k' => '-10',
      'kcommaaccent' => '-10',
    ),
    'cacute' => 
    array (
      'h' => '-10',
      'k' => '-10',
      'kcommaaccent' => '-10',
    ),
    'ccaron' => 
    array (
      'h' => '-10',
      'k' => '-10',
      'kcommaaccent' => '-10',
    ),
    'ccedilla' => 
    array (
      'h' => '-10',
      'k' => '-10',
      'kcommaaccent' => '-10',
    ),
    'comma' => 
    array (
      'quotedblright' => '-95',
      'quoteright' => '-95',
    ),
    'e' => 
    array (
      'b' => '-10',
    ),
    'eacute' => 
    array (
      'b' => '-10',
    ),
    'ecaron' => 
    array (
      'b' => '-10',
    ),
    'ecircumflex' => 
    array (
      'b' => '-10',
    ),
    'edieresis' => 
    array (
      'b' => '-10',
    ),
    'edotaccent' => 
    array (
      'b' => '-10',
    ),
    'egrave' => 
    array (
      'b' => '-10',
    ),
    'emacron' => 
    array (
      'b' => '-10',
    ),
    'eogonek' => 
    array (
      'b' => '-10',
    ),
    'f' => 
    array (
      'comma' => '-10',
      'dotlessi' => '-30',
      'e' => '-10',
      'eacute' => '-10',
      'edotaccent' => '-10',
      'eogonek' => '-10',
      'f' => '-18',
      'o' => '-10',
      'oacute' => '-10',
      'ocircumflex' => '-10',
      'ograve' => '-10',
      'ohungarumlaut' => '-10',
      'oslash' => '-10',
      'otilde' => '-10',
      'period' => '-10',
      'quoteright' => '55',
    ),
    'k' => 
    array (
      'e' => '-30',
      'eacute' => '-30',
      'ecaron' => '-30',
      'ecircumflex' => '-30',
      'edieresis' => '-30',
      'edotaccent' => '-30',
      'egrave' => '-30',
      'emacron' => '-30',
      'eogonek' => '-30',
      'o' => '-10',
      'oacute' => '-10',
      'ocircumflex' => '-10',
      'odieresis' => '-10',
      'ograve' => '-10',
      'ohungarumlaut' => '-10',
      'omacron' => '-10',
      'oslash' => '-10',
      'otilde' => '-10',
    ),
    'kcommaaccent' => 
    array (
      'e' => '-30',
      'eacute' => '-30',
      'ecaron' => '-30',
      'ecircumflex' => '-30',
      'edieresis' => '-30',
      'edotaccent' => '-30',
      'egrave' => '-30',
      'emacron' => '-30',
      'eogonek' => '-30',
      'o' => '-10',
      'oacute' => '-10',
      'ocircumflex' => '-10',
      'odieresis' => '-10',
      'ograve' => '-10',
      'ohungarumlaut' => '-10',
      'omacron' => '-10',
      'oslash' => '-10',
      'otilde' => '-10',
    ),
    'n' => 
    array (
      'v' => '-40',
    ),
    'nacute' => 
    array (
      'v' => '-40',
    ),
    'ncaron' => 
    array (
      'v' => '-40',
    ),
    'ncommaaccent' => 
    array (
      'v' => '-40',
    ),
    'ntilde' => 
    array (
      'v' => '-40',
    ),
    'o' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'x' => '-10',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'oacute' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'x' => '-10',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'ocircumflex' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'x' => '-10',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'odieresis' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'x' => '-10',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'ograve' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'x' => '-10',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'ohungarumlaut' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'x' => '-10',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'omacron' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'x' => '-10',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'oslash' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'x' => '-10',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'otilde' => 
    array (
      'v' => '-15',
      'w' => '-25',
      'x' => '-10',
      'y' => '-10',
      'yacute' => '-10',
      'ydieresis' => '-10',
    ),
    'period' => 
    array (
      'quotedblright' => '-95',
      'quoteright' => '-95',
    ),
    'quoteleft' => 
    array (
      'quoteleft' => '-74',
    ),
    'quoteright' => 
    array (
      'd' => '-15',
      'dcroat' => '-15',
      'quoteright' => '-74',
      'r' => '-15',
      'racute' => '-15',
      'rcaron' => '-15',
      'rcommaaccent' => '-15',
      's' => '-74',
      'sacute' => '-74',
      'scaron' => '-74',
      'scedilla' => '-74',
      'scommaaccent' => '-74',
      'space' => '-74',
      't' => '-37',
      'tcommaaccent' => '-37',
      'v' => '-15',
    ),
    'r' => 
    array (
      'comma' => '-65',
      'period' => '-65',
    ),
    'racute' => 
    array (
      'comma' => '-65',
      'period' => '-65',
    ),
    'rcaron' => 
    array (
      'comma' => '-65',
      'period' => '-65',
    ),
    'rcommaaccent' => 
    array (
      'comma' => '-65',
      'period' => '-65',
    ),
    'space' => 
    array (
      'A' => '-37',
      'Aacute' => '-37',
      'Abreve' => '-37',
      'Acircumflex' => '-37',
      'Adieresis' => '-37',
      'Agrave' => '-37',
      'Amacron' => '-37',
      'Aogonek' => '-37',
      'Aring' => '-37',
      'Atilde' => '-37',
      'V' => '-70',
      'W' => '-70',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
    ),
    'v' => 
    array (
      'comma' => '-37',
      'e' => '-15',
      'eacute' => '-15',
      'ecaron' => '-15',
      'ecircumflex' => '-15',
      'edieresis' => '-15',
      'edotaccent' => '-15',
      'egrave' => '-15',
      'emacron' => '-15',
      'eogonek' => '-15',
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
      'period' => '-37',
    ),
    'w' => 
    array (
      'a' => '-10',
      'aacute' => '-10',
      'abreve' => '-10',
      'acircumflex' => '-10',
      'adieresis' => '-10',
      'agrave' => '-10',
      'amacron' => '-10',
      'aogonek' => '-10',
      'aring' => '-10',
      'atilde' => '-10',
      'comma' => '-37',
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
      'period' => '-37',
    ),
    'x' => 
    array (
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
    ),
    'y' => 
    array (
      'comma' => '-37',
      'period' => '-37',
    ),
    'yacute' => 
    array (
      'comma' => '-37',
      'period' => '-37',
    ),
    'ydieresis' => 
    array (
      'comma' => '-37',
      'period' => '-37',
    ),
  ),
  '_version_' => 1,
);