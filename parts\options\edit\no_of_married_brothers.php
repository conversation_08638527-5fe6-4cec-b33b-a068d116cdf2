							<option value="No married brother" <?php if($DatabaseCo->dbRow->no_marri_brother=='No married brother'){echo "selected";}?>>No married brother</option>
							<option value="1 married brother" <?php if($DatabaseCo->dbRow->no_marri_brother=='1 married brother'){echo "selected";}?>>1 married brother</option>
							<option value="2 married brothers" <?php if($DatabaseCo->dbRow->no_marri_brother=='2 married brothers'){echo "selected";}?>>2 married brothers</option>
							<option value="3 married brothers" <?php if($DatabaseCo->dbRow->no_marri_brother=='3 married brothers'){echo "selected";}?>>3 married brothers</option>
							<option value="4 married brothers" <?php if($DatabaseCo->dbRow->no_marri_brother=='4 married brothers'){echo "selected";}?>>4 married brothers</option>
 							<option value="4+ married brothers" <?php if($DatabaseCo->dbRow->no_marri_brother=='4+ married brothers'){echo "selected";}?>>4+ married brothers</option>