<?php
include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();

$site_data = mysqli_fetch_object($DatabaseCo->dbLink->query("select * from site_config"));
$count=0;
$tokan = $_POST['tokan'];
$user_id = $_POST['user_id'];

if($user_id==""){
	$err_email="Enter Valid Email";
	$count++;
}else{
	$err_email="";
}

/*$otp = $_POST['otp'];
if($otp==""){
	 $err_password="Enter Correct OTP";
	 $count++;
}else{
	$err_password="";
}*/

if($count==0){
	$selqry = "SELECT * FROM register WHERE (matri_id='$user_id' OR mobile='$user_id' OR email='$user_id') AND (status!='Inactive' OR status!='Suspended')";
	$qryres = $DatabaseCo->dbLink->query($selqry);
	if(mysqli_num_rows($qryres) > 0){
		$updateTokan="UPDATE register set tokan='$tokan' WHERE (matri_id='$user_id' OR mobile='$user_id' OR email='$user_id')";
		$tokanQry = $DatabaseCo->dbLink->query($updateTokan);
		$count=0;
		while($contact_res = mysqli_fetch_object($qryres)){
            $email=$contact_res->email;
			$matri_id=$contact_res->matri_id;
			$user_id=$contact_res->index_id;
			$username=$contact_res->username;
			$firstname=$contact_res->firstname;
			$reg_date=$contact_res->reg_date;
			$gender=$contact_res->gender;
			$mem_status=$contact_res->status;
			$mobile=$contact_res->mobile;
			 
			if(isset($contact_res->photo1) !=''){
				$photo=$site_data->web_name."/my_photos/".$contact_res->photo1;
			}else{
				if($contact_res->gender=='Male'){
				 	$photo=$site_data->web_name."/img/app_img/male-upload-photo.jpg";
				 }else{
					$photo=$site_data->web_name."/img/app_img/female-upload-photo.jpg";
				 }
			}
			$response['responseData'] = array('email' => "$email",'matri_id' =>"$matri_id" ,'user_id'=>"$user_id",'username'=>"$username",'gender'=>"$gender",'profile_path'=>"$photo",'message'=>"Login Successfully",'membership_status'=>"$mem_status",'reg_date'=>"$reg_date",'mobile' => "$mobile");
		}
    		$response['status'] = "1";
    		$response['message'] = "Login Successfully";
    		echo json_encode($response);
    		exit;
		}else{
    		$response['status'] = "0";
    		$response['message'] = "Your profile is not active or suspended";
    		echo json_encode($response);
    		exit;
		}

}else{
	$response['status'] = "0";
	$response['message'] = "Enter Valid OTP";
	echo json_encode($response);
	exit;
}
?>
