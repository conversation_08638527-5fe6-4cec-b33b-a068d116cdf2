<?php
/**
 * Get Typing Status API - Enhanced Chat System
 * Retrieves typing status for real-time chat indicators
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
include_once '../databaseConn.php';

$DatabaseCo = new DatabaseConn();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Only POST method allowed');
}

$input = json_decode(file_get_contents('php://input'), true);
$currentUserId = $_SESSION['user_id'] ?? '';
$userId = $input['user_id'] ?? '';

// Validate inputs
if (empty($currentUserId)) {
    sendResponse(false, 'User not logged in');
}

if (empty($userId)) {
    sendResponse(false, 'User ID is required');
}

try {
    // Get typing status for the specified user
    $typingQuery = $DatabaseCo->dbLink->query("
        SELECT ts.is_typing, ts.last_updated, r.username 
        FROM typing_status ts
        LEFT JOIN register r ON ts.user_id = r.matri_id
        WHERE ts.user_id = '$userId' 
        AND ts.receiver_id = '$currentUserId'
        AND ts.last_updated > DATE_SUB(NOW(), INTERVAL 10 SECOND)
    ");
    
    $isTyping = false;
    $userName = '';
    $lastUpdated = null;
    
    if ($typingQuery && ($typingData = mysqli_fetch_object($typingQuery))) {
        $isTyping = $typingData->is_typing == 1;
        $userName = $typingData->username;
        $lastUpdated = $typingData->last_updated;
    }
    
    sendResponse(true, 'Typing status retrieved successfully', [
        'user_id' => $userId,
        'user_name' => $userName,
        'is_typing' => $isTyping,
        'last_updated' => $lastUpdated,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    error_log("Get typing status error: " . $e->getMessage());
    sendResponse(false, 'Failed to retrieve typing status');
}

/**
 * Send JSON response
 */
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => time()
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
    exit();
}
?>
