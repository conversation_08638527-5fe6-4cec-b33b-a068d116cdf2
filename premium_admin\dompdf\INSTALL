DOMPDF - PHP5 HTML to PDF converter

===================================



http://www.digitaljunkies.ca/dompdf

Copyright (c) 2004 Ben<PERSON> <PERSON>



Send bug reports, patches, feature requests, complaints & hate mail (no spam

thanks though) to <<EMAIL>>



##### See README for usage information. #####





Installation

------------



1. Untar/unzip the source package in a directory accessible by your

   webserver.



2. Edit dompdf_config.inc.php to fit your installation.



3. Give your webserver write permission on the path specified in

   DOMPDF_FONT_DIR (lib/fonts by default).  Under *nix, ideally you can make

   the webserver group the owner of this directory and give the directory

   group write permissions.  For example, on Debian systems, apache runs as

   the www-data user:



   $ chgrp www-data lib/fonts

   $ chmod g+w lib/fonts



   If your user is not a member of the www-data group or you do not have

   root priviledges, you can make the directory world writable and set the

   sticky bit:

   

   $ chmod 1777 lib/fonts





Font Installation

-----------------



Linux/Unix:



The load_font.php utility installs and converts TrueType fonts for use with

DOMPDF.  Since CSS uses the concept of font families (i.e. the same face can

be rendered in differnt styles & weights) DOMPDF needs to know which actual

font files belong to which font family and which style.  For example, the

Microsoft core font pack includes the files Verdana.ttf, Verdana_Italic.ttf,

Verdana_Bold.ttf and Verdana_Bold_Italic.ttf.  All four of these files need

to be present in the DOMPDF font directory (DOMPDF_FONT_DIR), and entries

need to be made in the dompdf_font_family_cache file.



Given the font family name and the path to the 'normal' font face file

(Verdana.ttf, in our example), load_font.php will search for the bold,

italic and bold italic font face files in the same directory as the

specified file.  It searches for files with the same base name followed by

'_Bold', 'B', or 'b' (similarly for italic and bold italic).  If it can not

find the correct files, you can specify them on the command line.



In addition to copying the files to the DOMPDF font directory, it also

generates .afm files.  The R&OS PDF class requires both the ttf file and an

afm file, which describes glyph metrics.  The afm file is generated using

the ttf2pt1 utlity (available at http://ttf2pt1.sourceforge.net).





usage:



./load_font.php font-family n_file [b_file] [i_file] [bi_file]



font_family:      the name of the font, e.g. Verdana, 'Times New Roman', 

                  monospace, sans-serif.



n_file:           the .pfb or .ttf file for the normal, non-bold, non-italic

                  face of the font.



{b|i|bi}_file:    the files for each of the respective (bold, italic, 

                  bold-italic) faces.



Examples:



$ ./load_font.php silkscreen /usr/share/fonts/truetype/slkscr.ttf



$ ./load_font.php 'Times New Roman' /mnt/c_drive/WINDOWS/Fonts/times.ttf



$ php -f load_font.php -- sans-serif /home/<USER>/myfonts/Verdana.ttf \

                                     /home/<USER>/myfonts/V_Bold.ttf



Windows:



(Note I don't have a windows test box at the moment, so these instructions

may not work...  If someone has tried this and has any suggestions for me,

please send me an email!)



Read the Linux/Unix section above first, as most of it applies.  The main

difference is the ttf2pt1 utility.  Fortunately, there is a windows version,

available at http://gnuwin32.sourceforge.net/packages/ttf2pt1.htm.  You will

have to edit your dompdf_config.inc.php file to point to the path where you

installed ttf2pt1.



You will also need the cli version of PHP in order to execute

load_font.php, however it's usage is the same (see the last example above).





Note for all platforms:



PDFs include support by default for Helvetica, Times-Roman, Courier and

ZapfDingbats.  You do not need to install any font files if you wish to use

these fonts.  This has the advantage of reducing the size of the resulting

PDF, because additional fonts must be embedded in the PDF.



Also, if you have problems installing the font files, you can try and use

the distributed dompdf_font_family_cache.dist file in lib/fonts.  Copy this

file to lib/fonts/dompdf_font_family_cache and edit it directly to match the

files present in your lib/fonts directory. You can uncomment the appropriate

entries in dompdf_font_family_cache once you have copied the appropriate ttf

files into lib/fonts and generated afm files for each one.



