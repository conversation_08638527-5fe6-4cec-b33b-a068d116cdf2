$this->fonts[$font]=array (
  'FontName' => 'Helvetica',
  'FullName' => 'Helvetica',
  'FamilyName' => 'Helvetica',
  'Weight' => 'Medium',
  'ItalicAngle' => '0',
  'IsFixedPitch' => 'false',
  'CharacterSet' => 'ExtendedRoman',
  'FontBBox' => 
  array (
    0 => '-166',
    1 => '-225',
    2 => '1000',
    3 => '931',
  ),
  'UnderlinePosition' => '-100',
  'UnderlineThickness' => '50',
  'Version' => '002.000',
  'EncodingScheme' => 'AdobeStandardEncoding',
  'CapHeight' => '718',
  'XHeight' => '523',
  'Ascender' => '718',
  'Descender' => '-207',
  'StdHW' => '76',
  'StdVW' => '88',
  'StartCharMetrics' => '315',
  'C' => 
  array (
    32 => 
    array (
      'C' => '32',
      'WX' => '278',
      'N' => 'space',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
    'space' => 
    array (
      'C' => '32',
      'WX' => '278',
      'N' => 'space',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
    33 => 
    array (
      'C' => '33',
      'WX' => '278',
      'N' => 'exclam',
      'B' => 
      array (
        0 => '90',
        1 => '0',
        2 => '187',
        3 => '718',
      ),
    ),
    'exclam' => 
    array (
      'C' => '33',
      'WX' => '278',
      'N' => 'exclam',
      'B' => 
      array (
        0 => '90',
        1 => '0',
        2 => '187',
        3 => '718',
      ),
    ),
    34 => 
    array (
      'C' => '34',
      'WX' => '355',
      'N' => 'quotedbl',
      'B' => 
      array (
        0 => '70',
        1 => '463',
        2 => '285',
        3 => '718',
      ),
    ),
    'quotedbl' => 
    array (
      'C' => '34',
      'WX' => '355',
      'N' => 'quotedbl',
      'B' => 
      array (
        0 => '70',
        1 => '463',
        2 => '285',
        3 => '718',
      ),
    ),
    35 => 
    array (
      'C' => '35',
      'WX' => '556',
      'N' => 'numbersign',
      'B' => 
      array (
        0 => '28',
        1 => '0',
        2 => '529',
        3 => '688',
      ),
    ),
    'numbersign' => 
    array (
      'C' => '35',
      'WX' => '556',
      'N' => 'numbersign',
      'B' => 
      array (
        0 => '28',
        1 => '0',
        2 => '529',
        3 => '688',
      ),
    ),
    36 => 
    array (
      'C' => '36',
      'WX' => '556',
      'N' => 'dollar',
      'B' => 
      array (
        0 => '32',
        1 => '-115',
        2 => '520',
        3 => '775',
      ),
    ),
    'dollar' => 
    array (
      'C' => '36',
      'WX' => '556',
      'N' => 'dollar',
      'B' => 
      array (
        0 => '32',
        1 => '-115',
        2 => '520',
        3 => '775',
      ),
    ),
    37 => 
    array (
      'C' => '37',
      'WX' => '889',
      'N' => 'percent',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '850',
        3 => '703',
      ),
    ),
    'percent' => 
    array (
      'C' => '37',
      'WX' => '889',
      'N' => 'percent',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '850',
        3 => '703',
      ),
    ),
    38 => 
    array (
      'C' => '38',
      'WX' => '667',
      'N' => 'ampersand',
      'B' => 
      array (
        0 => '44',
        1 => '-15',
        2 => '645',
        3 => '718',
      ),
    ),
    'ampersand' => 
    array (
      'C' => '38',
      'WX' => '667',
      'N' => 'ampersand',
      'B' => 
      array (
        0 => '44',
        1 => '-15',
        2 => '645',
        3 => '718',
      ),
    ),
    39 => 
    array (
      'C' => '39',
      'WX' => '222',
      'N' => 'quoteright',
      'B' => 
      array (
        0 => '53',
        1 => '463',
        2 => '157',
        3 => '718',
      ),
    ),
    'quoteright' => 
    array (
      'C' => '39',
      'WX' => '222',
      'N' => 'quoteright',
      'B' => 
      array (
        0 => '53',
        1 => '463',
        2 => '157',
        3 => '718',
      ),
    ),
    40 => 
    array (
      'C' => '40',
      'WX' => '333',
      'N' => 'parenleft',
      'B' => 
      array (
        0 => '68',
        1 => '-207',
        2 => '299',
        3 => '733',
      ),
    ),
    'parenleft' => 
    array (
      'C' => '40',
      'WX' => '333',
      'N' => 'parenleft',
      'B' => 
      array (
        0 => '68',
        1 => '-207',
        2 => '299',
        3 => '733',
      ),
    ),
    41 => 
    array (
      'C' => '41',
      'WX' => '333',
      'N' => 'parenright',
      'B' => 
      array (
        0 => '34',
        1 => '-207',
        2 => '265',
        3 => '733',
      ),
    ),
    'parenright' => 
    array (
      'C' => '41',
      'WX' => '333',
      'N' => 'parenright',
      'B' => 
      array (
        0 => '34',
        1 => '-207',
        2 => '265',
        3 => '733',
      ),
    ),
    42 => 
    array (
      'C' => '42',
      'WX' => '389',
      'N' => 'asterisk',
      'B' => 
      array (
        0 => '39',
        1 => '431',
        2 => '349',
        3 => '718',
      ),
    ),
    'asterisk' => 
    array (
      'C' => '42',
      'WX' => '389',
      'N' => 'asterisk',
      'B' => 
      array (
        0 => '39',
        1 => '431',
        2 => '349',
        3 => '718',
      ),
    ),
    43 => 
    array (
      'C' => '43',
      'WX' => '584',
      'N' => 'plus',
      'B' => 
      array (
        0 => '39',
        1 => '0',
        2 => '545',
        3 => '505',
      ),
    ),
    'plus' => 
    array (
      'C' => '43',
      'WX' => '584',
      'N' => 'plus',
      'B' => 
      array (
        0 => '39',
        1 => '0',
        2 => '545',
        3 => '505',
      ),
    ),
    44 => 
    array (
      'C' => '44',
      'WX' => '278',
      'N' => 'comma',
      'B' => 
      array (
        0 => '87',
        1 => '-147',
        2 => '191',
        3 => '106',
      ),
    ),
    'comma' => 
    array (
      'C' => '44',
      'WX' => '278',
      'N' => 'comma',
      'B' => 
      array (
        0 => '87',
        1 => '-147',
        2 => '191',
        3 => '106',
      ),
    ),
    45 => 
    array (
      'C' => '45',
      'WX' => '333',
      'N' => 'hyphen',
      'B' => 
      array (
        0 => '44',
        1 => '232',
        2 => '289',
        3 => '322',
      ),
    ),
    'hyphen' => 
    array (
      'C' => '45',
      'WX' => '333',
      'N' => 'hyphen',
      'B' => 
      array (
        0 => '44',
        1 => '232',
        2 => '289',
        3 => '322',
      ),
    ),
    46 => 
    array (
      'C' => '46',
      'WX' => '278',
      'N' => 'period',
      'B' => 
      array (
        0 => '87',
        1 => '0',
        2 => '191',
        3 => '106',
      ),
    ),
    'period' => 
    array (
      'C' => '46',
      'WX' => '278',
      'N' => 'period',
      'B' => 
      array (
        0 => '87',
        1 => '0',
        2 => '191',
        3 => '106',
      ),
    ),
    47 => 
    array (
      'C' => '47',
      'WX' => '278',
      'N' => 'slash',
      'B' => 
      array (
        0 => '-17',
        1 => '-19',
        2 => '295',
        3 => '737',
      ),
    ),
    'slash' => 
    array (
      'C' => '47',
      'WX' => '278',
      'N' => 'slash',
      'B' => 
      array (
        0 => '-17',
        1 => '-19',
        2 => '295',
        3 => '737',
      ),
    ),
    48 => 
    array (
      'C' => '48',
      'WX' => '556',
      'N' => 'zero',
      'B' => 
      array (
        0 => '37',
        1 => '-19',
        2 => '519',
        3 => '703',
      ),
    ),
    'zero' => 
    array (
      'C' => '48',
      'WX' => '556',
      'N' => 'zero',
      'B' => 
      array (
        0 => '37',
        1 => '-19',
        2 => '519',
        3 => '703',
      ),
    ),
    49 => 
    array (
      'C' => '49',
      'WX' => '556',
      'N' => 'one',
      'B' => 
      array (
        0 => '101',
        1 => '0',
        2 => '359',
        3 => '703',
      ),
    ),
    'one' => 
    array (
      'C' => '49',
      'WX' => '556',
      'N' => 'one',
      'B' => 
      array (
        0 => '101',
        1 => '0',
        2 => '359',
        3 => '703',
      ),
    ),
    50 => 
    array (
      'C' => '50',
      'WX' => '556',
      'N' => 'two',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '507',
        3 => '703',
      ),
    ),
    'two' => 
    array (
      'C' => '50',
      'WX' => '556',
      'N' => 'two',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '507',
        3 => '703',
      ),
    ),
    51 => 
    array (
      'C' => '51',
      'WX' => '556',
      'N' => 'three',
      'B' => 
      array (
        0 => '34',
        1 => '-19',
        2 => '522',
        3 => '703',
      ),
    ),
    'three' => 
    array (
      'C' => '51',
      'WX' => '556',
      'N' => 'three',
      'B' => 
      array (
        0 => '34',
        1 => '-19',
        2 => '522',
        3 => '703',
      ),
    ),
    52 => 
    array (
      'C' => '52',
      'WX' => '556',
      'N' => 'four',
      'B' => 
      array (
        0 => '25',
        1 => '0',
        2 => '523',
        3 => '703',
      ),
    ),
    'four' => 
    array (
      'C' => '52',
      'WX' => '556',
      'N' => 'four',
      'B' => 
      array (
        0 => '25',
        1 => '0',
        2 => '523',
        3 => '703',
      ),
    ),
    53 => 
    array (
      'C' => '53',
      'WX' => '556',
      'N' => 'five',
      'B' => 
      array (
        0 => '32',
        1 => '-19',
        2 => '514',
        3 => '688',
      ),
    ),
    'five' => 
    array (
      'C' => '53',
      'WX' => '556',
      'N' => 'five',
      'B' => 
      array (
        0 => '32',
        1 => '-19',
        2 => '514',
        3 => '688',
      ),
    ),
    54 => 
    array (
      'C' => '54',
      'WX' => '556',
      'N' => 'six',
      'B' => 
      array (
        0 => '38',
        1 => '-19',
        2 => '518',
        3 => '703',
      ),
    ),
    'six' => 
    array (
      'C' => '54',
      'WX' => '556',
      'N' => 'six',
      'B' => 
      array (
        0 => '38',
        1 => '-19',
        2 => '518',
        3 => '703',
      ),
    ),
    55 => 
    array (
      'C' => '55',
      'WX' => '556',
      'N' => 'seven',
      'B' => 
      array (
        0 => '37',
        1 => '0',
        2 => '523',
        3 => '688',
      ),
    ),
    'seven' => 
    array (
      'C' => '55',
      'WX' => '556',
      'N' => 'seven',
      'B' => 
      array (
        0 => '37',
        1 => '0',
        2 => '523',
        3 => '688',
      ),
    ),
    56 => 
    array (
      'C' => '56',
      'WX' => '556',
      'N' => 'eight',
      'B' => 
      array (
        0 => '38',
        1 => '-19',
        2 => '517',
        3 => '703',
      ),
    ),
    'eight' => 
    array (
      'C' => '56',
      'WX' => '556',
      'N' => 'eight',
      'B' => 
      array (
        0 => '38',
        1 => '-19',
        2 => '517',
        3 => '703',
      ),
    ),
    57 => 
    array (
      'C' => '57',
      'WX' => '556',
      'N' => 'nine',
      'B' => 
      array (
        0 => '42',
        1 => '-19',
        2 => '514',
        3 => '703',
      ),
    ),
    'nine' => 
    array (
      'C' => '57',
      'WX' => '556',
      'N' => 'nine',
      'B' => 
      array (
        0 => '42',
        1 => '-19',
        2 => '514',
        3 => '703',
      ),
    ),
    58 => 
    array (
      'C' => '58',
      'WX' => '278',
      'N' => 'colon',
      'B' => 
      array (
        0 => '87',
        1 => '0',
        2 => '191',
        3 => '516',
      ),
    ),
    'colon' => 
    array (
      'C' => '58',
      'WX' => '278',
      'N' => 'colon',
      'B' => 
      array (
        0 => '87',
        1 => '0',
        2 => '191',
        3 => '516',
      ),
    ),
    59 => 
    array (
      'C' => '59',
      'WX' => '278',
      'N' => 'semicolon',
      'B' => 
      array (
        0 => '87',
        1 => '-147',
        2 => '191',
        3 => '516',
      ),
    ),
    'semicolon' => 
    array (
      'C' => '59',
      'WX' => '278',
      'N' => 'semicolon',
      'B' => 
      array (
        0 => '87',
        1 => '-147',
        2 => '191',
        3 => '516',
      ),
    ),
    60 => 
    array (
      'C' => '60',
      'WX' => '584',
      'N' => 'less',
      'B' => 
      array (
        0 => '48',
        1 => '11',
        2 => '536',
        3 => '495',
      ),
    ),
    'less' => 
    array (
      'C' => '60',
      'WX' => '584',
      'N' => 'less',
      'B' => 
      array (
        0 => '48',
        1 => '11',
        2 => '536',
        3 => '495',
      ),
    ),
    61 => 
    array (
      'C' => '61',
      'WX' => '584',
      'N' => 'equal',
      'B' => 
      array (
        0 => '39',
        1 => '115',
        2 => '545',
        3 => '390',
      ),
    ),
    'equal' => 
    array (
      'C' => '61',
      'WX' => '584',
      'N' => 'equal',
      'B' => 
      array (
        0 => '39',
        1 => '115',
        2 => '545',
        3 => '390',
      ),
    ),
    62 => 
    array (
      'C' => '62',
      'WX' => '584',
      'N' => 'greater',
      'B' => 
      array (
        0 => '48',
        1 => '11',
        2 => '536',
        3 => '495',
      ),
    ),
    'greater' => 
    array (
      'C' => '62',
      'WX' => '584',
      'N' => 'greater',
      'B' => 
      array (
        0 => '48',
        1 => '11',
        2 => '536',
        3 => '495',
      ),
    ),
    63 => 
    array (
      'C' => '63',
      'WX' => '556',
      'N' => 'question',
      'B' => 
      array (
        0 => '56',
        1 => '0',
        2 => '492',
        3 => '727',
      ),
    ),
    'question' => 
    array (
      'C' => '63',
      'WX' => '556',
      'N' => 'question',
      'B' => 
      array (
        0 => '56',
        1 => '0',
        2 => '492',
        3 => '727',
      ),
    ),
    64 => 
    array (
      'C' => '64',
      'WX' => '1015',
      'N' => 'at',
      'B' => 
      array (
        0 => '147',
        1 => '-19',
        2 => '868',
        3 => '737',
      ),
    ),
    'at' => 
    array (
      'C' => '64',
      'WX' => '1015',
      'N' => 'at',
      'B' => 
      array (
        0 => '147',
        1 => '-19',
        2 => '868',
        3 => '737',
      ),
    ),
    65 => 
    array (
      'C' => '65',
      'WX' => '667',
      'N' => 'A',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '654',
        3 => '718',
      ),
    ),
    'A' => 
    array (
      'C' => '65',
      'WX' => '667',
      'N' => 'A',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '654',
        3 => '718',
      ),
    ),
    66 => 
    array (
      'C' => '66',
      'WX' => '667',
      'N' => 'B',
      'B' => 
      array (
        0 => '74',
        1 => '0',
        2 => '627',
        3 => '718',
      ),
    ),
    'B' => 
    array (
      'C' => '66',
      'WX' => '667',
      'N' => 'B',
      'B' => 
      array (
        0 => '74',
        1 => '0',
        2 => '627',
        3 => '718',
      ),
    ),
    67 => 
    array (
      'C' => '67',
      'WX' => '722',
      'N' => 'C',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '681',
        3 => '737',
      ),
    ),
    'C' => 
    array (
      'C' => '67',
      'WX' => '722',
      'N' => 'C',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '681',
        3 => '737',
      ),
    ),
    68 => 
    array (
      'C' => '68',
      'WX' => '722',
      'N' => 'D',
      'B' => 
      array (
        0 => '81',
        1 => '0',
        2 => '674',
        3 => '718',
      ),
    ),
    'D' => 
    array (
      'C' => '68',
      'WX' => '722',
      'N' => 'D',
      'B' => 
      array (
        0 => '81',
        1 => '0',
        2 => '674',
        3 => '718',
      ),
    ),
    69 => 
    array (
      'C' => '69',
      'WX' => '667',
      'N' => 'E',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '616',
        3 => '718',
      ),
    ),
    'E' => 
    array (
      'C' => '69',
      'WX' => '667',
      'N' => 'E',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '616',
        3 => '718',
      ),
    ),
    70 => 
    array (
      'C' => '70',
      'WX' => '611',
      'N' => 'F',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '583',
        3 => '718',
      ),
    ),
    'F' => 
    array (
      'C' => '70',
      'WX' => '611',
      'N' => 'F',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '583',
        3 => '718',
      ),
    ),
    71 => 
    array (
      'C' => '71',
      'WX' => '778',
      'N' => 'G',
      'B' => 
      array (
        0 => '48',
        1 => '-19',
        2 => '704',
        3 => '737',
      ),
    ),
    'G' => 
    array (
      'C' => '71',
      'WX' => '778',
      'N' => 'G',
      'B' => 
      array (
        0 => '48',
        1 => '-19',
        2 => '704',
        3 => '737',
      ),
    ),
    72 => 
    array (
      'C' => '72',
      'WX' => '722',
      'N' => 'H',
      'B' => 
      array (
        0 => '77',
        1 => '0',
        2 => '646',
        3 => '718',
      ),
    ),
    'H' => 
    array (
      'C' => '72',
      'WX' => '722',
      'N' => 'H',
      'B' => 
      array (
        0 => '77',
        1 => '0',
        2 => '646',
        3 => '718',
      ),
    ),
    73 => 
    array (
      'C' => '73',
      'WX' => '278',
      'N' => 'I',
      'B' => 
      array (
        0 => '91',
        1 => '0',
        2 => '188',
        3 => '718',
      ),
    ),
    'I' => 
    array (
      'C' => '73',
      'WX' => '278',
      'N' => 'I',
      'B' => 
      array (
        0 => '91',
        1 => '0',
        2 => '188',
        3 => '718',
      ),
    ),
    74 => 
    array (
      'C' => '74',
      'WX' => '500',
      'N' => 'J',
      'B' => 
      array (
        0 => '17',
        1 => '-19',
        2 => '428',
        3 => '718',
      ),
    ),
    'J' => 
    array (
      'C' => '74',
      'WX' => '500',
      'N' => 'J',
      'B' => 
      array (
        0 => '17',
        1 => '-19',
        2 => '428',
        3 => '718',
      ),
    ),
    75 => 
    array (
      'C' => '75',
      'WX' => '667',
      'N' => 'K',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '663',
        3 => '718',
      ),
    ),
    'K' => 
    array (
      'C' => '75',
      'WX' => '667',
      'N' => 'K',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '663',
        3 => '718',
      ),
    ),
    76 => 
    array (
      'C' => '76',
      'WX' => '556',
      'N' => 'L',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '537',
        3 => '718',
      ),
    ),
    'L' => 
    array (
      'C' => '76',
      'WX' => '556',
      'N' => 'L',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '537',
        3 => '718',
      ),
    ),
    77 => 
    array (
      'C' => '77',
      'WX' => '833',
      'N' => 'M',
      'B' => 
      array (
        0 => '73',
        1 => '0',
        2 => '761',
        3 => '718',
      ),
    ),
    'M' => 
    array (
      'C' => '77',
      'WX' => '833',
      'N' => 'M',
      'B' => 
      array (
        0 => '73',
        1 => '0',
        2 => '761',
        3 => '718',
      ),
    ),
    78 => 
    array (
      'C' => '78',
      'WX' => '722',
      'N' => 'N',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '646',
        3 => '718',
      ),
    ),
    'N' => 
    array (
      'C' => '78',
      'WX' => '722',
      'N' => 'N',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '646',
        3 => '718',
      ),
    ),
    79 => 
    array (
      'C' => '79',
      'WX' => '778',
      'N' => 'O',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '739',
        3 => '737',
      ),
    ),
    'O' => 
    array (
      'C' => '79',
      'WX' => '778',
      'N' => 'O',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '739',
        3 => '737',
      ),
    ),
    80 => 
    array (
      'C' => '80',
      'WX' => '667',
      'N' => 'P',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '622',
        3 => '718',
      ),
    ),
    'P' => 
    array (
      'C' => '80',
      'WX' => '667',
      'N' => 'P',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '622',
        3 => '718',
      ),
    ),
    81 => 
    array (
      'C' => '81',
      'WX' => '778',
      'N' => 'Q',
      'B' => 
      array (
        0 => '39',
        1 => '-56',
        2 => '739',
        3 => '737',
      ),
    ),
    'Q' => 
    array (
      'C' => '81',
      'WX' => '778',
      'N' => 'Q',
      'B' => 
      array (
        0 => '39',
        1 => '-56',
        2 => '739',
        3 => '737',
      ),
    ),
    82 => 
    array (
      'C' => '82',
      'WX' => '722',
      'N' => 'R',
      'B' => 
      array (
        0 => '88',
        1 => '0',
        2 => '684',
        3 => '718',
      ),
    ),
    'R' => 
    array (
      'C' => '82',
      'WX' => '722',
      'N' => 'R',
      'B' => 
      array (
        0 => '88',
        1 => '0',
        2 => '684',
        3 => '718',
      ),
    ),
    83 => 
    array (
      'C' => '83',
      'WX' => '667',
      'N' => 'S',
      'B' => 
      array (
        0 => '49',
        1 => '-19',
        2 => '620',
        3 => '737',
      ),
    ),
    'S' => 
    array (
      'C' => '83',
      'WX' => '667',
      'N' => 'S',
      'B' => 
      array (
        0 => '49',
        1 => '-19',
        2 => '620',
        3 => '737',
      ),
    ),
    84 => 
    array (
      'C' => '84',
      'WX' => '611',
      'N' => 'T',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '597',
        3 => '718',
      ),
    ),
    'T' => 
    array (
      'C' => '84',
      'WX' => '611',
      'N' => 'T',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '597',
        3 => '718',
      ),
    ),
    85 => 
    array (
      'C' => '85',
      'WX' => '722',
      'N' => 'U',
      'B' => 
      array (
        0 => '79',
        1 => '-19',
        2 => '644',
        3 => '718',
      ),
    ),
    'U' => 
    array (
      'C' => '85',
      'WX' => '722',
      'N' => 'U',
      'B' => 
      array (
        0 => '79',
        1 => '-19',
        2 => '644',
        3 => '718',
      ),
    ),
    86 => 
    array (
      'C' => '86',
      'WX' => '667',
      'N' => 'V',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '647',
        3 => '718',
      ),
    ),
    'V' => 
    array (
      'C' => '86',
      'WX' => '667',
      'N' => 'V',
      'B' => 
      array (
        0 => '20',
        1 => '0',
        2 => '647',
        3 => '718',
      ),
    ),
    87 => 
    array (
      'C' => '87',
      'WX' => '944',
      'N' => 'W',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '928',
        3 => '718',
      ),
    ),
    'W' => 
    array (
      'C' => '87',
      'WX' => '944',
      'N' => 'W',
      'B' => 
      array (
        0 => '16',
        1 => '0',
        2 => '928',
        3 => '718',
      ),
    ),
    88 => 
    array (
      'C' => '88',
      'WX' => '667',
      'N' => 'X',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '648',
        3 => '718',
      ),
    ),
    'X' => 
    array (
      'C' => '88',
      'WX' => '667',
      'N' => 'X',
      'B' => 
      array (
        0 => '19',
        1 => '0',
        2 => '648',
        3 => '718',
      ),
    ),
    89 => 
    array (
      'C' => '89',
      'WX' => '667',
      'N' => 'Y',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '653',
        3 => '718',
      ),
    ),
    'Y' => 
    array (
      'C' => '89',
      'WX' => '667',
      'N' => 'Y',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '653',
        3 => '718',
      ),
    ),
    90 => 
    array (
      'C' => '90',
      'WX' => '611',
      'N' => 'Z',
      'B' => 
      array (
        0 => '23',
        1 => '0',
        2 => '588',
        3 => '718',
      ),
    ),
    'Z' => 
    array (
      'C' => '90',
      'WX' => '611',
      'N' => 'Z',
      'B' => 
      array (
        0 => '23',
        1 => '0',
        2 => '588',
        3 => '718',
      ),
    ),
    91 => 
    array (
      'C' => '91',
      'WX' => '278',
      'N' => 'bracketleft',
      'B' => 
      array (
        0 => '63',
        1 => '-196',
        2 => '250',
        3 => '722',
      ),
    ),
    'bracketleft' => 
    array (
      'C' => '91',
      'WX' => '278',
      'N' => 'bracketleft',
      'B' => 
      array (
        0 => '63',
        1 => '-196',
        2 => '250',
        3 => '722',
      ),
    ),
    92 => 
    array (
      'C' => '92',
      'WX' => '278',
      'N' => 'backslash',
      'B' => 
      array (
        0 => '-17',
        1 => '-19',
        2 => '295',
        3 => '737',
      ),
    ),
    'backslash' => 
    array (
      'C' => '92',
      'WX' => '278',
      'N' => 'backslash',
      'B' => 
      array (
        0 => '-17',
        1 => '-19',
        2 => '295',
        3 => '737',
      ),
    ),
    93 => 
    array (
      'C' => '93',
      'WX' => '278',
      'N' => 'bracketright',
      'B' => 
      array (
        0 => '28',
        1 => '-196',
        2 => '215',
        3 => '722',
      ),
    ),
    'bracketright' => 
    array (
      'C' => '93',
      'WX' => '278',
      'N' => 'bracketright',
      'B' => 
      array (
        0 => '28',
        1 => '-196',
        2 => '215',
        3 => '722',
      ),
    ),
    94 => 
    array (
      'C' => '94',
      'WX' => '469',
      'N' => 'asciicircum',
      'B' => 
      array (
        0 => '-14',
        1 => '264',
        2 => '483',
        3 => '688',
      ),
    ),
    'asciicircum' => 
    array (
      'C' => '94',
      'WX' => '469',
      'N' => 'asciicircum',
      'B' => 
      array (
        0 => '-14',
        1 => '264',
        2 => '483',
        3 => '688',
      ),
    ),
    95 => 
    array (
      'C' => '95',
      'WX' => '556',
      'N' => 'underscore',
      'B' => 
      array (
        0 => '0',
        1 => '-125',
        2 => '556',
        3 => '-75',
      ),
    ),
    'underscore' => 
    array (
      'C' => '95',
      'WX' => '556',
      'N' => 'underscore',
      'B' => 
      array (
        0 => '0',
        1 => '-125',
        2 => '556',
        3 => '-75',
      ),
    ),
    96 => 
    array (
      'C' => '96',
      'WX' => '222',
      'N' => 'quoteleft',
      'B' => 
      array (
        0 => '65',
        1 => '470',
        2 => '169',
        3 => '725',
      ),
    ),
    'quoteleft' => 
    array (
      'C' => '96',
      'WX' => '222',
      'N' => 'quoteleft',
      'B' => 
      array (
        0 => '65',
        1 => '470',
        2 => '169',
        3 => '725',
      ),
    ),
    97 => 
    array (
      'C' => '97',
      'WX' => '556',
      'N' => 'a',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '530',
        3 => '538',
      ),
    ),
    'a' => 
    array (
      'C' => '97',
      'WX' => '556',
      'N' => 'a',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '530',
        3 => '538',
      ),
    ),
    98 => 
    array (
      'C' => '98',
      'WX' => '556',
      'N' => 'b',
      'B' => 
      array (
        0 => '58',
        1 => '-15',
        2 => '517',
        3 => '718',
      ),
    ),
    'b' => 
    array (
      'C' => '98',
      'WX' => '556',
      'N' => 'b',
      'B' => 
      array (
        0 => '58',
        1 => '-15',
        2 => '517',
        3 => '718',
      ),
    ),
    99 => 
    array (
      'C' => '99',
      'WX' => '500',
      'N' => 'c',
      'B' => 
      array (
        0 => '30',
        1 => '-15',
        2 => '477',
        3 => '538',
      ),
    ),
    'c' => 
    array (
      'C' => '99',
      'WX' => '500',
      'N' => 'c',
      'B' => 
      array (
        0 => '30',
        1 => '-15',
        2 => '477',
        3 => '538',
      ),
    ),
    100 => 
    array (
      'C' => '100',
      'WX' => '556',
      'N' => 'd',
      'B' => 
      array (
        0 => '35',
        1 => '-15',
        2 => '499',
        3 => '718',
      ),
    ),
    'd' => 
    array (
      'C' => '100',
      'WX' => '556',
      'N' => 'd',
      'B' => 
      array (
        0 => '35',
        1 => '-15',
        2 => '499',
        3 => '718',
      ),
    ),
    101 => 
    array (
      'C' => '101',
      'WX' => '556',
      'N' => 'e',
      'B' => 
      array (
        0 => '40',
        1 => '-15',
        2 => '516',
        3 => '538',
      ),
    ),
    'e' => 
    array (
      'C' => '101',
      'WX' => '556',
      'N' => 'e',
      'B' => 
      array (
        0 => '40',
        1 => '-15',
        2 => '516',
        3 => '538',
      ),
    ),
    102 => 
    array (
      'C' => '102',
      'WX' => '278',
      'N' => 'f',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '262',
        3 => '728',
      ),
      'L' => 
      array (
        0 => 'l',
        1 => 'fl',
      ),
    ),
    'f' => 
    array (
      'C' => '102',
      'WX' => '278',
      'N' => 'f',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '262',
        3 => '728',
      ),
      'L' => 
      array (
        0 => 'l',
        1 => 'fl',
      ),
    ),
    103 => 
    array (
      'C' => '103',
      'WX' => '556',
      'N' => 'g',
      'B' => 
      array (
        0 => '40',
        1 => '-220',
        2 => '499',
        3 => '538',
      ),
    ),
    'g' => 
    array (
      'C' => '103',
      'WX' => '556',
      'N' => 'g',
      'B' => 
      array (
        0 => '40',
        1 => '-220',
        2 => '499',
        3 => '538',
      ),
    ),
    104 => 
    array (
      'C' => '104',
      'WX' => '556',
      'N' => 'h',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '491',
        3 => '718',
      ),
    ),
    'h' => 
    array (
      'C' => '104',
      'WX' => '556',
      'N' => 'h',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '491',
        3 => '718',
      ),
    ),
    105 => 
    array (
      'C' => '105',
      'WX' => '222',
      'N' => 'i',
      'B' => 
      array (
        0 => '67',
        1 => '0',
        2 => '155',
        3 => '718',
      ),
    ),
    'i' => 
    array (
      'C' => '105',
      'WX' => '222',
      'N' => 'i',
      'B' => 
      array (
        0 => '67',
        1 => '0',
        2 => '155',
        3 => '718',
      ),
    ),
    106 => 
    array (
      'C' => '106',
      'WX' => '222',
      'N' => 'j',
      'B' => 
      array (
        0 => '-16',
        1 => '-210',
        2 => '155',
        3 => '718',
      ),
    ),
    'j' => 
    array (
      'C' => '106',
      'WX' => '222',
      'N' => 'j',
      'B' => 
      array (
        0 => '-16',
        1 => '-210',
        2 => '155',
        3 => '718',
      ),
    ),
    107 => 
    array (
      'C' => '107',
      'WX' => '500',
      'N' => 'k',
      'B' => 
      array (
        0 => '67',
        1 => '0',
        2 => '501',
        3 => '718',
      ),
    ),
    'k' => 
    array (
      'C' => '107',
      'WX' => '500',
      'N' => 'k',
      'B' => 
      array (
        0 => '67',
        1 => '0',
        2 => '501',
        3 => '718',
      ),
    ),
    108 => 
    array (
      'C' => '108',
      'WX' => '222',
      'N' => 'l',
      'B' => 
      array (
        0 => '67',
        1 => '0',
        2 => '155',
        3 => '718',
      ),
    ),
    'l' => 
    array (
      'C' => '108',
      'WX' => '222',
      'N' => 'l',
      'B' => 
      array (
        0 => '67',
        1 => '0',
        2 => '155',
        3 => '718',
      ),
    ),
    109 => 
    array (
      'C' => '109',
      'WX' => '833',
      'N' => 'm',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '769',
        3 => '538',
      ),
    ),
    'm' => 
    array (
      'C' => '109',
      'WX' => '833',
      'N' => 'm',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '769',
        3 => '538',
      ),
    ),
    110 => 
    array (
      'C' => '110',
      'WX' => '556',
      'N' => 'n',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '491',
        3 => '538',
      ),
    ),
    'n' => 
    array (
      'C' => '110',
      'WX' => '556',
      'N' => 'n',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '491',
        3 => '538',
      ),
    ),
    111 => 
    array (
      'C' => '111',
      'WX' => '556',
      'N' => 'o',
      'B' => 
      array (
        0 => '35',
        1 => '-14',
        2 => '521',
        3 => '538',
      ),
    ),
    'o' => 
    array (
      'C' => '111',
      'WX' => '556',
      'N' => 'o',
      'B' => 
      array (
        0 => '35',
        1 => '-14',
        2 => '521',
        3 => '538',
      ),
    ),
    112 => 
    array (
      'C' => '112',
      'WX' => '556',
      'N' => 'p',
      'B' => 
      array (
        0 => '58',
        1 => '-207',
        2 => '517',
        3 => '538',
      ),
    ),
    'p' => 
    array (
      'C' => '112',
      'WX' => '556',
      'N' => 'p',
      'B' => 
      array (
        0 => '58',
        1 => '-207',
        2 => '517',
        3 => '538',
      ),
    ),
    113 => 
    array (
      'C' => '113',
      'WX' => '556',
      'N' => 'q',
      'B' => 
      array (
        0 => '35',
        1 => '-207',
        2 => '494',
        3 => '538',
      ),
    ),
    'q' => 
    array (
      'C' => '113',
      'WX' => '556',
      'N' => 'q',
      'B' => 
      array (
        0 => '35',
        1 => '-207',
        2 => '494',
        3 => '538',
      ),
    ),
    114 => 
    array (
      'C' => '114',
      'WX' => '333',
      'N' => 'r',
      'B' => 
      array (
        0 => '77',
        1 => '0',
        2 => '332',
        3 => '538',
      ),
    ),
    'r' => 
    array (
      'C' => '114',
      'WX' => '333',
      'N' => 'r',
      'B' => 
      array (
        0 => '77',
        1 => '0',
        2 => '332',
        3 => '538',
      ),
    ),
    115 => 
    array (
      'C' => '115',
      'WX' => '500',
      'N' => 's',
      'B' => 
      array (
        0 => '32',
        1 => '-15',
        2 => '464',
        3 => '538',
      ),
    ),
    's' => 
    array (
      'C' => '115',
      'WX' => '500',
      'N' => 's',
      'B' => 
      array (
        0 => '32',
        1 => '-15',
        2 => '464',
        3 => '538',
      ),
    ),
    116 => 
    array (
      'C' => '116',
      'WX' => '278',
      'N' => 't',
      'B' => 
      array (
        0 => '14',
        1 => '-7',
        2 => '257',
        3 => '669',
      ),
    ),
    't' => 
    array (
      'C' => '116',
      'WX' => '278',
      'N' => 't',
      'B' => 
      array (
        0 => '14',
        1 => '-7',
        2 => '257',
        3 => '669',
      ),
    ),
    117 => 
    array (
      'C' => '117',
      'WX' => '556',
      'N' => 'u',
      'B' => 
      array (
        0 => '68',
        1 => '-15',
        2 => '489',
        3 => '523',
      ),
    ),
    'u' => 
    array (
      'C' => '117',
      'WX' => '556',
      'N' => 'u',
      'B' => 
      array (
        0 => '68',
        1 => '-15',
        2 => '489',
        3 => '523',
      ),
    ),
    118 => 
    array (
      'C' => '118',
      'WX' => '500',
      'N' => 'v',
      'B' => 
      array (
        0 => '8',
        1 => '0',
        2 => '492',
        3 => '523',
      ),
    ),
    'v' => 
    array (
      'C' => '118',
      'WX' => '500',
      'N' => 'v',
      'B' => 
      array (
        0 => '8',
        1 => '0',
        2 => '492',
        3 => '523',
      ),
    ),
    119 => 
    array (
      'C' => '119',
      'WX' => '722',
      'N' => 'w',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '709',
        3 => '523',
      ),
    ),
    'w' => 
    array (
      'C' => '119',
      'WX' => '722',
      'N' => 'w',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '709',
        3 => '523',
      ),
    ),
    120 => 
    array (
      'C' => '120',
      'WX' => '500',
      'N' => 'x',
      'B' => 
      array (
        0 => '11',
        1 => '0',
        2 => '490',
        3 => '523',
      ),
    ),
    'x' => 
    array (
      'C' => '120',
      'WX' => '500',
      'N' => 'x',
      'B' => 
      array (
        0 => '11',
        1 => '0',
        2 => '490',
        3 => '523',
      ),
    ),
    121 => 
    array (
      'C' => '121',
      'WX' => '500',
      'N' => 'y',
      'B' => 
      array (
        0 => '11',
        1 => '-214',
        2 => '489',
        3 => '523',
      ),
    ),
    'y' => 
    array (
      'C' => '121',
      'WX' => '500',
      'N' => 'y',
      'B' => 
      array (
        0 => '11',
        1 => '-214',
        2 => '489',
        3 => '523',
      ),
    ),
    122 => 
    array (
      'C' => '122',
      'WX' => '500',
      'N' => 'z',
      'B' => 
      array (
        0 => '31',
        1 => '0',
        2 => '469',
        3 => '523',
      ),
    ),
    'z' => 
    array (
      'C' => '122',
      'WX' => '500',
      'N' => 'z',
      'B' => 
      array (
        0 => '31',
        1 => '0',
        2 => '469',
        3 => '523',
      ),
    ),
    123 => 
    array (
      'C' => '123',
      'WX' => '334',
      'N' => 'braceleft',
      'B' => 
      array (
        0 => '42',
        1 => '-196',
        2 => '292',
        3 => '722',
      ),
    ),
    'braceleft' => 
    array (
      'C' => '123',
      'WX' => '334',
      'N' => 'braceleft',
      'B' => 
      array (
        0 => '42',
        1 => '-196',
        2 => '292',
        3 => '722',
      ),
    ),
    124 => 
    array (
      'C' => '124',
      'WX' => '260',
      'N' => 'bar',
      'B' => 
      array (
        0 => '94',
        1 => '-225',
        2 => '167',
        3 => '775',
      ),
    ),
    'bar' => 
    array (
      'C' => '124',
      'WX' => '260',
      'N' => 'bar',
      'B' => 
      array (
        0 => '94',
        1 => '-225',
        2 => '167',
        3 => '775',
      ),
    ),
    125 => 
    array (
      'C' => '125',
      'WX' => '334',
      'N' => 'braceright',
      'B' => 
      array (
        0 => '42',
        1 => '-196',
        2 => '292',
        3 => '722',
      ),
    ),
    'braceright' => 
    array (
      'C' => '125',
      'WX' => '334',
      'N' => 'braceright',
      'B' => 
      array (
        0 => '42',
        1 => '-196',
        2 => '292',
        3 => '722',
      ),
    ),
    126 => 
    array (
      'C' => '126',
      'WX' => '584',
      'N' => 'asciitilde',
      'B' => 
      array (
        0 => '61',
        1 => '180',
        2 => '523',
        3 => '326',
      ),
    ),
    'asciitilde' => 
    array (
      'C' => '126',
      'WX' => '584',
      'N' => 'asciitilde',
      'B' => 
      array (
        0 => '61',
        1 => '180',
        2 => '523',
        3 => '326',
      ),
    ),
    161 => 
    array (
      'C' => '161',
      'WX' => '333',
      'N' => 'exclamdown',
      'B' => 
      array (
        0 => '118',
        1 => '-195',
        2 => '215',
        3 => '523',
      ),
    ),
    'exclamdown' => 
    array (
      'C' => '161',
      'WX' => '333',
      'N' => 'exclamdown',
      'B' => 
      array (
        0 => '118',
        1 => '-195',
        2 => '215',
        3 => '523',
      ),
    ),
    162 => 
    array (
      'C' => '162',
      'WX' => '556',
      'N' => 'cent',
      'B' => 
      array (
        0 => '51',
        1 => '-115',
        2 => '513',
        3 => '623',
      ),
    ),
    'cent' => 
    array (
      'C' => '162',
      'WX' => '556',
      'N' => 'cent',
      'B' => 
      array (
        0 => '51',
        1 => '-115',
        2 => '513',
        3 => '623',
      ),
    ),
    163 => 
    array (
      'C' => '163',
      'WX' => '556',
      'N' => 'sterling',
      'B' => 
      array (
        0 => '33',
        1 => '-16',
        2 => '539',
        3 => '718',
      ),
    ),
    'sterling' => 
    array (
      'C' => '163',
      'WX' => '556',
      'N' => 'sterling',
      'B' => 
      array (
        0 => '33',
        1 => '-16',
        2 => '539',
        3 => '718',
      ),
    ),
    164 => 
    array (
      'C' => '164',
      'WX' => '167',
      'N' => 'fraction',
      'B' => 
      array (
        0 => '-166',
        1 => '-19',
        2 => '333',
        3 => '703',
      ),
    ),
    'fraction' => 
    array (
      'C' => '164',
      'WX' => '167',
      'N' => 'fraction',
      'B' => 
      array (
        0 => '-166',
        1 => '-19',
        2 => '333',
        3 => '703',
      ),
    ),
    165 => 
    array (
      'C' => '165',
      'WX' => '556',
      'N' => 'yen',
      'B' => 
      array (
        0 => '3',
        1 => '0',
        2 => '553',
        3 => '688',
      ),
    ),
    'yen' => 
    array (
      'C' => '165',
      'WX' => '556',
      'N' => 'yen',
      'B' => 
      array (
        0 => '3',
        1 => '0',
        2 => '553',
        3 => '688',
      ),
    ),
    166 => 
    array (
      'C' => '166',
      'WX' => '556',
      'N' => 'florin',
      'B' => 
      array (
        0 => '-11',
        1 => '-207',
        2 => '501',
        3 => '737',
      ),
    ),
    'florin' => 
    array (
      'C' => '166',
      'WX' => '556',
      'N' => 'florin',
      'B' => 
      array (
        0 => '-11',
        1 => '-207',
        2 => '501',
        3 => '737',
      ),
    ),
    167 => 
    array (
      'C' => '167',
      'WX' => '556',
      'N' => 'section',
      'B' => 
      array (
        0 => '43',
        1 => '-191',
        2 => '512',
        3 => '737',
      ),
    ),
    'section' => 
    array (
      'C' => '167',
      'WX' => '556',
      'N' => 'section',
      'B' => 
      array (
        0 => '43',
        1 => '-191',
        2 => '512',
        3 => '737',
      ),
    ),
    168 => 
    array (
      'C' => '168',
      'WX' => '556',
      'N' => 'currency',
      'B' => 
      array (
        0 => '28',
        1 => '99',
        2 => '528',
        3 => '603',
      ),
    ),
    'currency' => 
    array (
      'C' => '168',
      'WX' => '556',
      'N' => 'currency',
      'B' => 
      array (
        0 => '28',
        1 => '99',
        2 => '528',
        3 => '603',
      ),
    ),
    169 => 
    array (
      'C' => '169',
      'WX' => '191',
      'N' => 'quotesingle',
      'B' => 
      array (
        0 => '59',
        1 => '463',
        2 => '132',
        3 => '718',
      ),
    ),
    'quotesingle' => 
    array (
      'C' => '169',
      'WX' => '191',
      'N' => 'quotesingle',
      'B' => 
      array (
        0 => '59',
        1 => '463',
        2 => '132',
        3 => '718',
      ),
    ),
    170 => 
    array (
      'C' => '170',
      'WX' => '333',
      'N' => 'quotedblleft',
      'B' => 
      array (
        0 => '38',
        1 => '470',
        2 => '307',
        3 => '725',
      ),
    ),
    'quotedblleft' => 
    array (
      'C' => '170',
      'WX' => '333',
      'N' => 'quotedblleft',
      'B' => 
      array (
        0 => '38',
        1 => '470',
        2 => '307',
        3 => '725',
      ),
    ),
    171 => 
    array (
      'C' => '171',
      'WX' => '556',
      'N' => 'guillemotleft',
      'B' => 
      array (
        0 => '97',
        1 => '108',
        2 => '459',
        3 => '446',
      ),
    ),
    'guillemotleft' => 
    array (
      'C' => '171',
      'WX' => '556',
      'N' => 'guillemotleft',
      'B' => 
      array (
        0 => '97',
        1 => '108',
        2 => '459',
        3 => '446',
      ),
    ),
    172 => 
    array (
      'C' => '172',
      'WX' => '333',
      'N' => 'guilsinglleft',
      'B' => 
      array (
        0 => '88',
        1 => '108',
        2 => '245',
        3 => '446',
      ),
    ),
    'guilsinglleft' => 
    array (
      'C' => '172',
      'WX' => '333',
      'N' => 'guilsinglleft',
      'B' => 
      array (
        0 => '88',
        1 => '108',
        2 => '245',
        3 => '446',
      ),
    ),
    173 => 
    array (
      'C' => '173',
      'WX' => '333',
      'N' => 'guilsinglright',
      'B' => 
      array (
        0 => '88',
        1 => '108',
        2 => '245',
        3 => '446',
      ),
    ),
    'guilsinglright' => 
    array (
      'C' => '173',
      'WX' => '333',
      'N' => 'guilsinglright',
      'B' => 
      array (
        0 => '88',
        1 => '108',
        2 => '245',
        3 => '446',
      ),
    ),
    174 => 
    array (
      'C' => '174',
      'WX' => '500',
      'N' => 'fi',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '434',
        3 => '728',
      ),
    ),
    'fi' => 
    array (
      'C' => '174',
      'WX' => '500',
      'N' => 'fi',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '434',
        3 => '728',
      ),
    ),
    175 => 
    array (
      'C' => '175',
      'WX' => '500',
      'N' => 'fl',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '432',
        3 => '728',
      ),
    ),
    'fl' => 
    array (
      'C' => '175',
      'WX' => '500',
      'N' => 'fl',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '432',
        3 => '728',
      ),
    ),
    177 => 
    array (
      'C' => '177',
      'WX' => '556',
      'N' => 'endash',
      'B' => 
      array (
        0 => '0',
        1 => '240',
        2 => '556',
        3 => '313',
      ),
    ),
    'endash' => 
    array (
      'C' => '177',
      'WX' => '556',
      'N' => 'endash',
      'B' => 
      array (
        0 => '0',
        1 => '240',
        2 => '556',
        3 => '313',
      ),
    ),
    178 => 
    array (
      'C' => '178',
      'WX' => '556',
      'N' => 'dagger',
      'B' => 
      array (
        0 => '43',
        1 => '-159',
        2 => '514',
        3 => '718',
      ),
    ),
    'dagger' => 
    array (
      'C' => '178',
      'WX' => '556',
      'N' => 'dagger',
      'B' => 
      array (
        0 => '43',
        1 => '-159',
        2 => '514',
        3 => '718',
      ),
    ),
    179 => 
    array (
      'C' => '179',
      'WX' => '556',
      'N' => 'daggerdbl',
      'B' => 
      array (
        0 => '43',
        1 => '-159',
        2 => '514',
        3 => '718',
      ),
    ),
    'daggerdbl' => 
    array (
      'C' => '179',
      'WX' => '556',
      'N' => 'daggerdbl',
      'B' => 
      array (
        0 => '43',
        1 => '-159',
        2 => '514',
        3 => '718',
      ),
    ),
    180 => 
    array (
      'C' => '180',
      'WX' => '278',
      'N' => 'periodcentered',
      'B' => 
      array (
        0 => '77',
        1 => '190',
        2 => '202',
        3 => '315',
      ),
    ),
    'periodcentered' => 
    array (
      'C' => '180',
      'WX' => '278',
      'N' => 'periodcentered',
      'B' => 
      array (
        0 => '77',
        1 => '190',
        2 => '202',
        3 => '315',
      ),
    ),
    182 => 
    array (
      'C' => '182',
      'WX' => '537',
      'N' => 'paragraph',
      'B' => 
      array (
        0 => '18',
        1 => '-173',
        2 => '497',
        3 => '718',
      ),
    ),
    'paragraph' => 
    array (
      'C' => '182',
      'WX' => '537',
      'N' => 'paragraph',
      'B' => 
      array (
        0 => '18',
        1 => '-173',
        2 => '497',
        3 => '718',
      ),
    ),
    183 => 
    array (
      'C' => '183',
      'WX' => '350',
      'N' => 'bullet',
      'B' => 
      array (
        0 => '18',
        1 => '202',
        2 => '333',
        3 => '517',
      ),
    ),
    'bullet' => 
    array (
      'C' => '183',
      'WX' => '350',
      'N' => 'bullet',
      'B' => 
      array (
        0 => '18',
        1 => '202',
        2 => '333',
        3 => '517',
      ),
    ),
    184 => 
    array (
      'C' => '184',
      'WX' => '222',
      'N' => 'quotesinglbase',
      'B' => 
      array (
        0 => '53',
        1 => '-149',
        2 => '157',
        3 => '106',
      ),
    ),
    'quotesinglbase' => 
    array (
      'C' => '184',
      'WX' => '222',
      'N' => 'quotesinglbase',
      'B' => 
      array (
        0 => '53',
        1 => '-149',
        2 => '157',
        3 => '106',
      ),
    ),
    185 => 
    array (
      'C' => '185',
      'WX' => '333',
      'N' => 'quotedblbase',
      'B' => 
      array (
        0 => '26',
        1 => '-149',
        2 => '295',
        3 => '106',
      ),
    ),
    'quotedblbase' => 
    array (
      'C' => '185',
      'WX' => '333',
      'N' => 'quotedblbase',
      'B' => 
      array (
        0 => '26',
        1 => '-149',
        2 => '295',
        3 => '106',
      ),
    ),
    186 => 
    array (
      'C' => '186',
      'WX' => '333',
      'N' => 'quotedblright',
      'B' => 
      array (
        0 => '26',
        1 => '463',
        2 => '295',
        3 => '718',
      ),
    ),
    'quotedblright' => 
    array (
      'C' => '186',
      'WX' => '333',
      'N' => 'quotedblright',
      'B' => 
      array (
        0 => '26',
        1 => '463',
        2 => '295',
        3 => '718',
      ),
    ),
    187 => 
    array (
      'C' => '187',
      'WX' => '556',
      'N' => 'guillemotright',
      'B' => 
      array (
        0 => '97',
        1 => '108',
        2 => '459',
        3 => '446',
      ),
    ),
    'guillemotright' => 
    array (
      'C' => '187',
      'WX' => '556',
      'N' => 'guillemotright',
      'B' => 
      array (
        0 => '97',
        1 => '108',
        2 => '459',
        3 => '446',
      ),
    ),
    188 => 
    array (
      'C' => '188',
      'WX' => '1000',
      'N' => 'ellipsis',
      'B' => 
      array (
        0 => '115',
        1 => '0',
        2 => '885',
        3 => '106',
      ),
    ),
    'ellipsis' => 
    array (
      'C' => '188',
      'WX' => '1000',
      'N' => 'ellipsis',
      'B' => 
      array (
        0 => '115',
        1 => '0',
        2 => '885',
        3 => '106',
      ),
    ),
    189 => 
    array (
      'C' => '189',
      'WX' => '1000',
      'N' => 'perthousand',
      'B' => 
      array (
        0 => '7',
        1 => '-19',
        2 => '994',
        3 => '703',
      ),
    ),
    'perthousand' => 
    array (
      'C' => '189',
      'WX' => '1000',
      'N' => 'perthousand',
      'B' => 
      array (
        0 => '7',
        1 => '-19',
        2 => '994',
        3 => '703',
      ),
    ),
    191 => 
    array (
      'C' => '191',
      'WX' => '611',
      'N' => 'questiondown',
      'B' => 
      array (
        0 => '91',
        1 => '-201',
        2 => '527',
        3 => '525',
      ),
    ),
    'questiondown' => 
    array (
      'C' => '191',
      'WX' => '611',
      'N' => 'questiondown',
      'B' => 
      array (
        0 => '91',
        1 => '-201',
        2 => '527',
        3 => '525',
      ),
    ),
    193 => 
    array (
      'C' => '193',
      'WX' => '333',
      'N' => 'grave',
      'B' => 
      array (
        0 => '14',
        1 => '593',
        2 => '211',
        3 => '734',
      ),
    ),
    'grave' => 
    array (
      'C' => '193',
      'WX' => '333',
      'N' => 'grave',
      'B' => 
      array (
        0 => '14',
        1 => '593',
        2 => '211',
        3 => '734',
      ),
    ),
    194 => 
    array (
      'C' => '194',
      'WX' => '333',
      'N' => 'acute',
      'B' => 
      array (
        0 => '122',
        1 => '593',
        2 => '319',
        3 => '734',
      ),
    ),
    'acute' => 
    array (
      'C' => '194',
      'WX' => '333',
      'N' => 'acute',
      'B' => 
      array (
        0 => '122',
        1 => '593',
        2 => '319',
        3 => '734',
      ),
    ),
    195 => 
    array (
      'C' => '195',
      'WX' => '333',
      'N' => 'circumflex',
      'B' => 
      array (
        0 => '21',
        1 => '593',
        2 => '312',
        3 => '734',
      ),
    ),
    'circumflex' => 
    array (
      'C' => '195',
      'WX' => '333',
      'N' => 'circumflex',
      'B' => 
      array (
        0 => '21',
        1 => '593',
        2 => '312',
        3 => '734',
      ),
    ),
    196 => 
    array (
      'C' => '196',
      'WX' => '333',
      'N' => 'tilde',
      'B' => 
      array (
        0 => '-4',
        1 => '606',
        2 => '337',
        3 => '722',
      ),
    ),
    'tilde' => 
    array (
      'C' => '196',
      'WX' => '333',
      'N' => 'tilde',
      'B' => 
      array (
        0 => '-4',
        1 => '606',
        2 => '337',
        3 => '722',
      ),
    ),
    197 => 
    array (
      'C' => '197',
      'WX' => '333',
      'N' => 'macron',
      'B' => 
      array (
        0 => '10',
        1 => '627',
        2 => '323',
        3 => '684',
      ),
    ),
    'macron' => 
    array (
      'C' => '197',
      'WX' => '333',
      'N' => 'macron',
      'B' => 
      array (
        0 => '10',
        1 => '627',
        2 => '323',
        3 => '684',
      ),
    ),
    198 => 
    array (
      'C' => '198',
      'WX' => '333',
      'N' => 'breve',
      'B' => 
      array (
        0 => '13',
        1 => '595',
        2 => '321',
        3 => '731',
      ),
    ),
    'breve' => 
    array (
      'C' => '198',
      'WX' => '333',
      'N' => 'breve',
      'B' => 
      array (
        0 => '13',
        1 => '595',
        2 => '321',
        3 => '731',
      ),
    ),
    199 => 
    array (
      'C' => '199',
      'WX' => '333',
      'N' => 'dotaccent',
      'B' => 
      array (
        0 => '121',
        1 => '604',
        2 => '212',
        3 => '706',
      ),
    ),
    'dotaccent' => 
    array (
      'C' => '199',
      'WX' => '333',
      'N' => 'dotaccent',
      'B' => 
      array (
        0 => '121',
        1 => '604',
        2 => '212',
        3 => '706',
      ),
    ),
    200 => 
    array (
      'C' => '200',
      'WX' => '333',
      'N' => 'dieresis',
      'B' => 
      array (
        0 => '40',
        1 => '604',
        2 => '293',
        3 => '706',
      ),
    ),
    'dieresis' => 
    array (
      'C' => '200',
      'WX' => '333',
      'N' => 'dieresis',
      'B' => 
      array (
        0 => '40',
        1 => '604',
        2 => '293',
        3 => '706',
      ),
    ),
    202 => 
    array (
      'C' => '202',
      'WX' => '333',
      'N' => 'ring',
      'B' => 
      array (
        0 => '75',
        1 => '572',
        2 => '259',
        3 => '756',
      ),
    ),
    'ring' => 
    array (
      'C' => '202',
      'WX' => '333',
      'N' => 'ring',
      'B' => 
      array (
        0 => '75',
        1 => '572',
        2 => '259',
        3 => '756',
      ),
    ),
    203 => 
    array (
      'C' => '203',
      'WX' => '333',
      'N' => 'cedilla',
      'B' => 
      array (
        0 => '45',
        1 => '-225',
        2 => '259',
        3 => '0',
      ),
    ),
    'cedilla' => 
    array (
      'C' => '203',
      'WX' => '333',
      'N' => 'cedilla',
      'B' => 
      array (
        0 => '45',
        1 => '-225',
        2 => '259',
        3 => '0',
      ),
    ),
    205 => 
    array (
      'C' => '205',
      'WX' => '333',
      'N' => 'hungarumlaut',
      'B' => 
      array (
        0 => '31',
        1 => '593',
        2 => '409',
        3 => '734',
      ),
    ),
    'hungarumlaut' => 
    array (
      'C' => '205',
      'WX' => '333',
      'N' => 'hungarumlaut',
      'B' => 
      array (
        0 => '31',
        1 => '593',
        2 => '409',
        3 => '734',
      ),
    ),
    206 => 
    array (
      'C' => '206',
      'WX' => '333',
      'N' => 'ogonek',
      'B' => 
      array (
        0 => '73',
        1 => '-225',
        2 => '287',
        3 => '0',
      ),
    ),
    'ogonek' => 
    array (
      'C' => '206',
      'WX' => '333',
      'N' => 'ogonek',
      'B' => 
      array (
        0 => '73',
        1 => '-225',
        2 => '287',
        3 => '0',
      ),
    ),
    207 => 
    array (
      'C' => '207',
      'WX' => '333',
      'N' => 'caron',
      'B' => 
      array (
        0 => '21',
        1 => '593',
        2 => '312',
        3 => '734',
      ),
    ),
    'caron' => 
    array (
      'C' => '207',
      'WX' => '333',
      'N' => 'caron',
      'B' => 
      array (
        0 => '21',
        1 => '593',
        2 => '312',
        3 => '734',
      ),
    ),
    208 => 
    array (
      'C' => '208',
      'WX' => '1000',
      'N' => 'emdash',
      'B' => 
      array (
        0 => '0',
        1 => '240',
        2 => '1000',
        3 => '313',
      ),
    ),
    'emdash' => 
    array (
      'C' => '208',
      'WX' => '1000',
      'N' => 'emdash',
      'B' => 
      array (
        0 => '0',
        1 => '240',
        2 => '1000',
        3 => '313',
      ),
    ),
    225 => 
    array (
      'C' => '225',
      'WX' => '1000',
      'N' => 'AE',
      'B' => 
      array (
        0 => '8',
        1 => '0',
        2 => '951',
        3 => '718',
      ),
    ),
    'AE' => 
    array (
      'C' => '225',
      'WX' => '1000',
      'N' => 'AE',
      'B' => 
      array (
        0 => '8',
        1 => '0',
        2 => '951',
        3 => '718',
      ),
    ),
    227 => 
    array (
      'C' => '227',
      'WX' => '370',
      'N' => 'ordfeminine',
      'B' => 
      array (
        0 => '24',
        1 => '405',
        2 => '346',
        3 => '737',
      ),
    ),
    'ordfeminine' => 
    array (
      'C' => '227',
      'WX' => '370',
      'N' => 'ordfeminine',
      'B' => 
      array (
        0 => '24',
        1 => '405',
        2 => '346',
        3 => '737',
      ),
    ),
    232 => 
    array (
      'C' => '232',
      'WX' => '556',
      'N' => 'Lslash',
      'B' => 
      array (
        0 => '-20',
        1 => '0',
        2 => '537',
        3 => '718',
      ),
    ),
    'Lslash' => 
    array (
      'C' => '232',
      'WX' => '556',
      'N' => 'Lslash',
      'B' => 
      array (
        0 => '-20',
        1 => '0',
        2 => '537',
        3 => '718',
      ),
    ),
    233 => 
    array (
      'C' => '233',
      'WX' => '778',
      'N' => 'Oslash',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '740',
        3 => '737',
      ),
    ),
    'Oslash' => 
    array (
      'C' => '233',
      'WX' => '778',
      'N' => 'Oslash',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '740',
        3 => '737',
      ),
    ),
    234 => 
    array (
      'C' => '234',
      'WX' => '1000',
      'N' => 'OE',
      'B' => 
      array (
        0 => '36',
        1 => '-19',
        2 => '965',
        3 => '737',
      ),
    ),
    'OE' => 
    array (
      'C' => '234',
      'WX' => '1000',
      'N' => 'OE',
      'B' => 
      array (
        0 => '36',
        1 => '-19',
        2 => '965',
        3 => '737',
      ),
    ),
    235 => 
    array (
      'C' => '235',
      'WX' => '365',
      'N' => 'ordmasculine',
      'B' => 
      array (
        0 => '25',
        1 => '405',
        2 => '341',
        3 => '737',
      ),
    ),
    'ordmasculine' => 
    array (
      'C' => '235',
      'WX' => '365',
      'N' => 'ordmasculine',
      'B' => 
      array (
        0 => '25',
        1 => '405',
        2 => '341',
        3 => '737',
      ),
    ),
    241 => 
    array (
      'C' => '241',
      'WX' => '889',
      'N' => 'ae',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '847',
        3 => '538',
      ),
    ),
    'ae' => 
    array (
      'C' => '241',
      'WX' => '889',
      'N' => 'ae',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '847',
        3 => '538',
      ),
    ),
    245 => 
    array (
      'C' => '245',
      'WX' => '278',
      'N' => 'dotlessi',
      'B' => 
      array (
        0 => '95',
        1 => '0',
        2 => '183',
        3 => '523',
      ),
    ),
    'dotlessi' => 
    array (
      'C' => '245',
      'WX' => '278',
      'N' => 'dotlessi',
      'B' => 
      array (
        0 => '95',
        1 => '0',
        2 => '183',
        3 => '523',
      ),
    ),
    248 => 
    array (
      'C' => '248',
      'WX' => '222',
      'N' => 'lslash',
      'B' => 
      array (
        0 => '-20',
        1 => '0',
        2 => '242',
        3 => '718',
      ),
    ),
    'lslash' => 
    array (
      'C' => '248',
      'WX' => '222',
      'N' => 'lslash',
      'B' => 
      array (
        0 => '-20',
        1 => '0',
        2 => '242',
        3 => '718',
      ),
    ),
    249 => 
    array (
      'C' => '249',
      'WX' => '611',
      'N' => 'oslash',
      'B' => 
      array (
        0 => '28',
        1 => '-22',
        2 => '537',
        3 => '545',
      ),
    ),
    'oslash' => 
    array (
      'C' => '249',
      'WX' => '611',
      'N' => 'oslash',
      'B' => 
      array (
        0 => '28',
        1 => '-22',
        2 => '537',
        3 => '545',
      ),
    ),
    250 => 
    array (
      'C' => '250',
      'WX' => '944',
      'N' => 'oe',
      'B' => 
      array (
        0 => '35',
        1 => '-15',
        2 => '902',
        3 => '538',
      ),
    ),
    'oe' => 
    array (
      'C' => '250',
      'WX' => '944',
      'N' => 'oe',
      'B' => 
      array (
        0 => '35',
        1 => '-15',
        2 => '902',
        3 => '538',
      ),
    ),
    251 => 
    array (
      'C' => '251',
      'WX' => '611',
      'N' => 'germandbls',
      'B' => 
      array (
        0 => '67',
        1 => '-15',
        2 => '571',
        3 => '728',
      ),
    ),
    'germandbls' => 
    array (
      'C' => '251',
      'WX' => '611',
      'N' => 'germandbls',
      'B' => 
      array (
        0 => '67',
        1 => '-15',
        2 => '571',
        3 => '728',
      ),
    ),
    'Idieresis' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Idieresis',
      'B' => 
      array (
        0 => '13',
        1 => '0',
        2 => '266',
        3 => '901',
      ),
    ),
    'eacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'eacute',
      'B' => 
      array (
        0 => '40',
        1 => '-15',
        2 => '516',
        3 => '734',
      ),
    ),
    'abreve' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'abreve',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '530',
        3 => '731',
      ),
    ),
    'uhungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uhungarumlaut',
      'B' => 
      array (
        0 => '68',
        1 => '-15',
        2 => '521',
        3 => '734',
      ),
    ),
    'ecaron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ecaron',
      'B' => 
      array (
        0 => '40',
        1 => '-15',
        2 => '516',
        3 => '734',
      ),
    ),
    'Ydieresis' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ydieresis',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '653',
        3 => '901',
      ),
    ),
    'divide' => 
    array (
      'C' => '-1',
      'WX' => '584',
      'N' => 'divide',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '545',
        3 => '524',
      ),
    ),
    'Yacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Yacute',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '653',
        3 => '929',
      ),
    ),
    'Acircumflex' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Acircumflex',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '654',
        3 => '929',
      ),
    ),
    'aacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'aacute',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '530',
        3 => '734',
      ),
    ),
    'Ucircumflex' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ucircumflex',
      'B' => 
      array (
        0 => '79',
        1 => '-19',
        2 => '644',
        3 => '929',
      ),
    ),
    'yacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'yacute',
      'B' => 
      array (
        0 => '11',
        1 => '-214',
        2 => '489',
        3 => '734',
      ),
    ),
    'scommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'scommaaccent',
      'B' => 
      array (
        0 => '32',
        1 => '-225',
        2 => '464',
        3 => '538',
      ),
    ),
    'ecircumflex' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ecircumflex',
      'B' => 
      array (
        0 => '40',
        1 => '-15',
        2 => '516',
        3 => '734',
      ),
    ),
    'Uring' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uring',
      'B' => 
      array (
        0 => '79',
        1 => '-19',
        2 => '644',
        3 => '931',
      ),
    ),
    'Udieresis' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Udieresis',
      'B' => 
      array (
        0 => '79',
        1 => '-19',
        2 => '644',
        3 => '901',
      ),
    ),
    'aogonek' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'aogonek',
      'B' => 
      array (
        0 => '36',
        1 => '-220',
        2 => '547',
        3 => '538',
      ),
    ),
    'Uacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uacute',
      'B' => 
      array (
        0 => '79',
        1 => '-19',
        2 => '644',
        3 => '929',
      ),
    ),
    'uogonek' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uogonek',
      'B' => 
      array (
        0 => '68',
        1 => '-225',
        2 => '519',
        3 => '523',
      ),
    ),
    'Edieresis' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Edieresis',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '616',
        3 => '901',
      ),
    ),
    'Dcroat' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Dcroat',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '674',
        3 => '718',
      ),
    ),
    'commaaccent' => 
    array (
      'C' => '-1',
      'WX' => '250',
      'N' => 'commaaccent',
      'B' => 
      array (
        0 => '87',
        1 => '-225',
        2 => '181',
        3 => '-40',
      ),
    ),
    'copyright' => 
    array (
      'C' => '-1',
      'WX' => '737',
      'N' => 'copyright',
      'B' => 
      array (
        0 => '-14',
        1 => '-19',
        2 => '752',
        3 => '737',
      ),
    ),
    'Emacron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Emacron',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '616',
        3 => '879',
      ),
    ),
    'ccaron' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ccaron',
      'B' => 
      array (
        0 => '30',
        1 => '-15',
        2 => '477',
        3 => '734',
      ),
    ),
    'aring' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'aring',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '530',
        3 => '756',
      ),
    ),
    'Ncommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ncommaaccent',
      'B' => 
      array (
        0 => '76',
        1 => '-225',
        2 => '646',
        3 => '718',
      ),
    ),
    'lacute' => 
    array (
      'C' => '-1',
      'WX' => '222',
      'N' => 'lacute',
      'B' => 
      array (
        0 => '67',
        1 => '0',
        2 => '264',
        3 => '929',
      ),
    ),
    'agrave' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'agrave',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '530',
        3 => '734',
      ),
    ),
    'Tcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Tcommaaccent',
      'B' => 
      array (
        0 => '14',
        1 => '-225',
        2 => '597',
        3 => '718',
      ),
    ),
    'Cacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Cacute',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '681',
        3 => '929',
      ),
    ),
    'atilde' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'atilde',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '530',
        3 => '722',
      ),
    ),
    'Edotaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Edotaccent',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '616',
        3 => '901',
      ),
    ),
    'scaron' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'scaron',
      'B' => 
      array (
        0 => '32',
        1 => '-15',
        2 => '464',
        3 => '734',
      ),
    ),
    'scedilla' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'scedilla',
      'B' => 
      array (
        0 => '32',
        1 => '-225',
        2 => '464',
        3 => '538',
      ),
    ),
    'iacute' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'iacute',
      'B' => 
      array (
        0 => '95',
        1 => '0',
        2 => '292',
        3 => '734',
      ),
    ),
    'lozenge' => 
    array (
      'C' => '-1',
      'WX' => '471',
      'N' => 'lozenge',
      'B' => 
      array (
        0 => '10',
        1 => '0',
        2 => '462',
        3 => '728',
      ),
    ),
    'Rcaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Rcaron',
      'B' => 
      array (
        0 => '88',
        1 => '0',
        2 => '684',
        3 => '929',
      ),
    ),
    'Gcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Gcommaaccent',
      'B' => 
      array (
        0 => '48',
        1 => '-225',
        2 => '704',
        3 => '737',
      ),
    ),
    'ucircumflex' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ucircumflex',
      'B' => 
      array (
        0 => '68',
        1 => '-15',
        2 => '489',
        3 => '734',
      ),
    ),
    'acircumflex' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'acircumflex',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '530',
        3 => '734',
      ),
    ),
    'Amacron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Amacron',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '654',
        3 => '879',
      ),
    ),
    'rcaron' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'rcaron',
      'B' => 
      array (
        0 => '61',
        1 => '0',
        2 => '352',
        3 => '734',
      ),
    ),
    'ccedilla' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ccedilla',
      'B' => 
      array (
        0 => '30',
        1 => '-225',
        2 => '477',
        3 => '538',
      ),
    ),
    'Zdotaccent' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zdotaccent',
      'B' => 
      array (
        0 => '23',
        1 => '0',
        2 => '588',
        3 => '901',
      ),
    ),
    'Thorn' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Thorn',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '622',
        3 => '718',
      ),
    ),
    'Omacron' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Omacron',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '739',
        3 => '879',
      ),
    ),
    'Racute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Racute',
      'B' => 
      array (
        0 => '88',
        1 => '0',
        2 => '684',
        3 => '929',
      ),
    ),
    'Sacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Sacute',
      'B' => 
      array (
        0 => '49',
        1 => '-19',
        2 => '620',
        3 => '929',
      ),
    ),
    'dcaron' => 
    array (
      'C' => '-1',
      'WX' => '643',
      'N' => 'dcaron',
      'B' => 
      array (
        0 => '35',
        1 => '-15',
        2 => '655',
        3 => '718',
      ),
    ),
    'Umacron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Umacron',
      'B' => 
      array (
        0 => '79',
        1 => '-19',
        2 => '644',
        3 => '879',
      ),
    ),
    'uring' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uring',
      'B' => 
      array (
        0 => '68',
        1 => '-15',
        2 => '489',
        3 => '756',
      ),
    ),
    'threesuperior' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'threesuperior',
      'B' => 
      array (
        0 => '5',
        1 => '270',
        2 => '325',
        3 => '703',
      ),
    ),
    'Ograve' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Ograve',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '739',
        3 => '929',
      ),
    ),
    'Agrave' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Agrave',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '654',
        3 => '929',
      ),
    ),
    'Abreve' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Abreve',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '654',
        3 => '926',
      ),
    ),
    'multiply' => 
    array (
      'C' => '-1',
      'WX' => '584',
      'N' => 'multiply',
      'B' => 
      array (
        0 => '39',
        1 => '0',
        2 => '545',
        3 => '506',
      ),
    ),
    'uacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'uacute',
      'B' => 
      array (
        0 => '68',
        1 => '-15',
        2 => '489',
        3 => '734',
      ),
    ),
    'Tcaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Tcaron',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '597',
        3 => '929',
      ),
    ),
    'partialdiff' => 
    array (
      'C' => '-1',
      'WX' => '476',
      'N' => 'partialdiff',
      'B' => 
      array (
        0 => '13',
        1 => '-38',
        2 => '463',
        3 => '714',
      ),
    ),
    'ydieresis' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'ydieresis',
      'B' => 
      array (
        0 => '11',
        1 => '-214',
        2 => '489',
        3 => '706',
      ),
    ),
    'Nacute' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Nacute',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '646',
        3 => '929',
      ),
    ),
    'icircumflex' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'icircumflex',
      'B' => 
      array (
        0 => '-6',
        1 => '0',
        2 => '285',
        3 => '734',
      ),
    ),
    'Ecircumflex' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ecircumflex',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '616',
        3 => '929',
      ),
    ),
    'adieresis' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'adieresis',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '530',
        3 => '706',
      ),
    ),
    'edieresis' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'edieresis',
      'B' => 
      array (
        0 => '40',
        1 => '-15',
        2 => '516',
        3 => '706',
      ),
    ),
    'cacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'cacute',
      'B' => 
      array (
        0 => '30',
        1 => '-15',
        2 => '477',
        3 => '734',
      ),
    ),
    'nacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'nacute',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '491',
        3 => '734',
      ),
    ),
    'umacron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'umacron',
      'B' => 
      array (
        0 => '68',
        1 => '-15',
        2 => '489',
        3 => '684',
      ),
    ),
    'Ncaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ncaron',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '646',
        3 => '929',
      ),
    ),
    'Iacute' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Iacute',
      'B' => 
      array (
        0 => '91',
        1 => '0',
        2 => '292',
        3 => '929',
      ),
    ),
    'plusminus' => 
    array (
      'C' => '-1',
      'WX' => '584',
      'N' => 'plusminus',
      'B' => 
      array (
        0 => '39',
        1 => '0',
        2 => '545',
        3 => '506',
      ),
    ),
    'brokenbar' => 
    array (
      'C' => '-1',
      'WX' => '260',
      'N' => 'brokenbar',
      'B' => 
      array (
        0 => '94',
        1 => '-150',
        2 => '167',
        3 => '700',
      ),
    ),
    'registered' => 
    array (
      'C' => '-1',
      'WX' => '737',
      'N' => 'registered',
      'B' => 
      array (
        0 => '-14',
        1 => '-19',
        2 => '752',
        3 => '737',
      ),
    ),
    'Gbreve' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Gbreve',
      'B' => 
      array (
        0 => '48',
        1 => '-19',
        2 => '704',
        3 => '926',
      ),
    ),
    'Idotaccent' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Idotaccent',
      'B' => 
      array (
        0 => '91',
        1 => '0',
        2 => '188',
        3 => '901',
      ),
    ),
    'summation' => 
    array (
      'C' => '-1',
      'WX' => '600',
      'N' => 'summation',
      'B' => 
      array (
        0 => '15',
        1 => '-10',
        2 => '586',
        3 => '706',
      ),
    ),
    'Egrave' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Egrave',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '616',
        3 => '929',
      ),
    ),
    'racute' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'racute',
      'B' => 
      array (
        0 => '77',
        1 => '0',
        2 => '332',
        3 => '734',
      ),
    ),
    'omacron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'omacron',
      'B' => 
      array (
        0 => '35',
        1 => '-14',
        2 => '521',
        3 => '684',
      ),
    ),
    'Zacute' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zacute',
      'B' => 
      array (
        0 => '23',
        1 => '0',
        2 => '588',
        3 => '929',
      ),
    ),
    'Zcaron' => 
    array (
      'C' => '-1',
      'WX' => '611',
      'N' => 'Zcaron',
      'B' => 
      array (
        0 => '23',
        1 => '0',
        2 => '588',
        3 => '929',
      ),
    ),
    'greaterequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'greaterequal',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '523',
        3 => '674',
      ),
    ),
    'Eth' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Eth',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '674',
        3 => '718',
      ),
    ),
    'Ccedilla' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ccedilla',
      'B' => 
      array (
        0 => '44',
        1 => '-225',
        2 => '681',
        3 => '737',
      ),
    ),
    'lcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '222',
      'N' => 'lcommaaccent',
      'B' => 
      array (
        0 => '67',
        1 => '-225',
        2 => '167',
        3 => '718',
      ),
    ),
    'tcaron' => 
    array (
      'C' => '-1',
      'WX' => '317',
      'N' => 'tcaron',
      'B' => 
      array (
        0 => '14',
        1 => '-7',
        2 => '329',
        3 => '808',
      ),
    ),
    'eogonek' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'eogonek',
      'B' => 
      array (
        0 => '40',
        1 => '-225',
        2 => '516',
        3 => '538',
      ),
    ),
    'Uogonek' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uogonek',
      'B' => 
      array (
        0 => '79',
        1 => '-225',
        2 => '644',
        3 => '718',
      ),
    ),
    'Aacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Aacute',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '654',
        3 => '929',
      ),
    ),
    'Adieresis' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Adieresis',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '654',
        3 => '901',
      ),
    ),
    'egrave' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'egrave',
      'B' => 
      array (
        0 => '40',
        1 => '-15',
        2 => '516',
        3 => '734',
      ),
    ),
    'zacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'zacute',
      'B' => 
      array (
        0 => '31',
        1 => '0',
        2 => '469',
        3 => '734',
      ),
    ),
    'iogonek' => 
    array (
      'C' => '-1',
      'WX' => '222',
      'N' => 'iogonek',
      'B' => 
      array (
        0 => '-31',
        1 => '-225',
        2 => '183',
        3 => '718',
      ),
    ),
    'Oacute' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Oacute',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '739',
        3 => '929',
      ),
    ),
    'oacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'oacute',
      'B' => 
      array (
        0 => '35',
        1 => '-14',
        2 => '521',
        3 => '734',
      ),
    ),
    'amacron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'amacron',
      'B' => 
      array (
        0 => '36',
        1 => '-15',
        2 => '530',
        3 => '684',
      ),
    ),
    'sacute' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'sacute',
      'B' => 
      array (
        0 => '32',
        1 => '-15',
        2 => '464',
        3 => '734',
      ),
    ),
    'idieresis' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'idieresis',
      'B' => 
      array (
        0 => '13',
        1 => '0',
        2 => '266',
        3 => '706',
      ),
    ),
    'Ocircumflex' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Ocircumflex',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '739',
        3 => '929',
      ),
    ),
    'Ugrave' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ugrave',
      'B' => 
      array (
        0 => '79',
        1 => '-19',
        2 => '644',
        3 => '929',
      ),
    ),
    'Delta' => 
    array (
      'C' => '-1',
      'WX' => '612',
      'N' => 'Delta',
      'B' => 
      array (
        0 => '6',
        1 => '0',
        2 => '608',
        3 => '688',
      ),
    ),
    'thorn' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'thorn',
      'B' => 
      array (
        0 => '58',
        1 => '-207',
        2 => '517',
        3 => '718',
      ),
    ),
    'twosuperior' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'twosuperior',
      'B' => 
      array (
        0 => '4',
        1 => '281',
        2 => '323',
        3 => '703',
      ),
    ),
    'Odieresis' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Odieresis',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '739',
        3 => '901',
      ),
    ),
    'mu' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'mu',
      'B' => 
      array (
        0 => '68',
        1 => '-207',
        2 => '489',
        3 => '523',
      ),
    ),
    'igrave' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'igrave',
      'B' => 
      array (
        0 => '-13',
        1 => '0',
        2 => '184',
        3 => '734',
      ),
    ),
    'ohungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ohungarumlaut',
      'B' => 
      array (
        0 => '35',
        1 => '-14',
        2 => '521',
        3 => '734',
      ),
    ),
    'Eogonek' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Eogonek',
      'B' => 
      array (
        0 => '86',
        1 => '-220',
        2 => '633',
        3 => '718',
      ),
    ),
    'dcroat' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'dcroat',
      'B' => 
      array (
        0 => '35',
        1 => '-15',
        2 => '550',
        3 => '718',
      ),
    ),
    'threequarters' => 
    array (
      'C' => '-1',
      'WX' => '834',
      'N' => 'threequarters',
      'B' => 
      array (
        0 => '45',
        1 => '-19',
        2 => '810',
        3 => '703',
      ),
    ),
    'Scedilla' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Scedilla',
      'B' => 
      array (
        0 => '49',
        1 => '-225',
        2 => '620',
        3 => '737',
      ),
    ),
    'lcaron' => 
    array (
      'C' => '-1',
      'WX' => '299',
      'N' => 'lcaron',
      'B' => 
      array (
        0 => '67',
        1 => '0',
        2 => '311',
        3 => '718',
      ),
    ),
    'Kcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Kcommaaccent',
      'B' => 
      array (
        0 => '76',
        1 => '-225',
        2 => '663',
        3 => '718',
      ),
    ),
    'Lacute' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Lacute',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '537',
        3 => '929',
      ),
    ),
    'trademark' => 
    array (
      'C' => '-1',
      'WX' => '1000',
      'N' => 'trademark',
      'B' => 
      array (
        0 => '46',
        1 => '306',
        2 => '903',
        3 => '718',
      ),
    ),
    'edotaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'edotaccent',
      'B' => 
      array (
        0 => '40',
        1 => '-15',
        2 => '516',
        3 => '706',
      ),
    ),
    'Igrave' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Igrave',
      'B' => 
      array (
        0 => '-13',
        1 => '0',
        2 => '188',
        3 => '929',
      ),
    ),
    'Imacron' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Imacron',
      'B' => 
      array (
        0 => '-17',
        1 => '0',
        2 => '296',
        3 => '879',
      ),
    ),
    'Lcaron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Lcaron',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '537',
        3 => '718',
      ),
    ),
    'onehalf' => 
    array (
      'C' => '-1',
      'WX' => '834',
      'N' => 'onehalf',
      'B' => 
      array (
        0 => '43',
        1 => '-19',
        2 => '773',
        3 => '703',
      ),
    ),
    'lessequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'lessequal',
      'B' => 
      array (
        0 => '26',
        1 => '0',
        2 => '523',
        3 => '674',
      ),
    ),
    'ocircumflex' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ocircumflex',
      'B' => 
      array (
        0 => '35',
        1 => '-14',
        2 => '521',
        3 => '734',
      ),
    ),
    'ntilde' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ntilde',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '491',
        3 => '722',
      ),
    ),
    'Uhungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Uhungarumlaut',
      'B' => 
      array (
        0 => '79',
        1 => '-19',
        2 => '644',
        3 => '929',
      ),
    ),
    'Eacute' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Eacute',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '616',
        3 => '929',
      ),
    ),
    'emacron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'emacron',
      'B' => 
      array (
        0 => '40',
        1 => '-15',
        2 => '516',
        3 => '684',
      ),
    ),
    'gbreve' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'gbreve',
      'B' => 
      array (
        0 => '40',
        1 => '-220',
        2 => '499',
        3 => '731',
      ),
    ),
    'onequarter' => 
    array (
      'C' => '-1',
      'WX' => '834',
      'N' => 'onequarter',
      'B' => 
      array (
        0 => '73',
        1 => '-19',
        2 => '756',
        3 => '703',
      ),
    ),
    'Scaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Scaron',
      'B' => 
      array (
        0 => '49',
        1 => '-19',
        2 => '620',
        3 => '929',
      ),
    ),
    'Scommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Scommaaccent',
      'B' => 
      array (
        0 => '49',
        1 => '-225',
        2 => '620',
        3 => '737',
      ),
    ),
    'Ohungarumlaut' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Ohungarumlaut',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '739',
        3 => '929',
      ),
    ),
    'degree' => 
    array (
      'C' => '-1',
      'WX' => '400',
      'N' => 'degree',
      'B' => 
      array (
        0 => '54',
        1 => '411',
        2 => '346',
        3 => '703',
      ),
    ),
    'ograve' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ograve',
      'B' => 
      array (
        0 => '35',
        1 => '-14',
        2 => '521',
        3 => '734',
      ),
    ),
    'Ccaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ccaron',
      'B' => 
      array (
        0 => '44',
        1 => '-19',
        2 => '681',
        3 => '929',
      ),
    ),
    'ugrave' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ugrave',
      'B' => 
      array (
        0 => '68',
        1 => '-15',
        2 => '489',
        3 => '734',
      ),
    ),
    'radical' => 
    array (
      'C' => '-1',
      'WX' => '453',
      'N' => 'radical',
      'B' => 
      array (
        0 => '-4',
        1 => '-80',
        2 => '458',
        3 => '762',
      ),
    ),
    'Dcaron' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Dcaron',
      'B' => 
      array (
        0 => '81',
        1 => '0',
        2 => '674',
        3 => '929',
      ),
    ),
    'rcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'rcommaaccent',
      'B' => 
      array (
        0 => '77',
        1 => '-225',
        2 => '332',
        3 => '538',
      ),
    ),
    'Ntilde' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Ntilde',
      'B' => 
      array (
        0 => '76',
        1 => '0',
        2 => '646',
        3 => '917',
      ),
    ),
    'otilde' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'otilde',
      'B' => 
      array (
        0 => '35',
        1 => '-14',
        2 => '521',
        3 => '722',
      ),
    ),
    'Rcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '722',
      'N' => 'Rcommaaccent',
      'B' => 
      array (
        0 => '88',
        1 => '-225',
        2 => '684',
        3 => '718',
      ),
    ),
    'Lcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Lcommaaccent',
      'B' => 
      array (
        0 => '76',
        1 => '-225',
        2 => '537',
        3 => '718',
      ),
    ),
    'Atilde' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Atilde',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '654',
        3 => '917',
      ),
    ),
    'Aogonek' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Aogonek',
      'B' => 
      array (
        0 => '14',
        1 => '-225',
        2 => '654',
        3 => '718',
      ),
    ),
    'Aring' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Aring',
      'B' => 
      array (
        0 => '14',
        1 => '0',
        2 => '654',
        3 => '931',
      ),
    ),
    'Otilde' => 
    array (
      'C' => '-1',
      'WX' => '778',
      'N' => 'Otilde',
      'B' => 
      array (
        0 => '39',
        1 => '-19',
        2 => '739',
        3 => '917',
      ),
    ),
    'zdotaccent' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'zdotaccent',
      'B' => 
      array (
        0 => '31',
        1 => '0',
        2 => '469',
        3 => '706',
      ),
    ),
    'Ecaron' => 
    array (
      'C' => '-1',
      'WX' => '667',
      'N' => 'Ecaron',
      'B' => 
      array (
        0 => '86',
        1 => '0',
        2 => '616',
        3 => '929',
      ),
    ),
    'Iogonek' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Iogonek',
      'B' => 
      array (
        0 => '-3',
        1 => '-225',
        2 => '211',
        3 => '718',
      ),
    ),
    'kcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'kcommaaccent',
      'B' => 
      array (
        0 => '67',
        1 => '-225',
        2 => '501',
        3 => '718',
      ),
    ),
    'minus' => 
    array (
      'C' => '-1',
      'WX' => '584',
      'N' => 'minus',
      'B' => 
      array (
        0 => '39',
        1 => '216',
        2 => '545',
        3 => '289',
      ),
    ),
    'Icircumflex' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'Icircumflex',
      'B' => 
      array (
        0 => '-6',
        1 => '0',
        2 => '285',
        3 => '929',
      ),
    ),
    'ncaron' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ncaron',
      'B' => 
      array (
        0 => '65',
        1 => '0',
        2 => '491',
        3 => '734',
      ),
    ),
    'tcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'tcommaaccent',
      'B' => 
      array (
        0 => '14',
        1 => '-225',
        2 => '257',
        3 => '669',
      ),
    ),
    'logicalnot' => 
    array (
      'C' => '-1',
      'WX' => '584',
      'N' => 'logicalnot',
      'B' => 
      array (
        0 => '39',
        1 => '108',
        2 => '545',
        3 => '390',
      ),
    ),
    'odieresis' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'odieresis',
      'B' => 
      array (
        0 => '35',
        1 => '-14',
        2 => '521',
        3 => '706',
      ),
    ),
    'udieresis' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'udieresis',
      'B' => 
      array (
        0 => '68',
        1 => '-15',
        2 => '489',
        3 => '706',
      ),
    ),
    'notequal' => 
    array (
      'C' => '-1',
      'WX' => '549',
      'N' => 'notequal',
      'B' => 
      array (
        0 => '12',
        1 => '-35',
        2 => '537',
        3 => '551',
      ),
    ),
    'gcommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'gcommaaccent',
      'B' => 
      array (
        0 => '40',
        1 => '-220',
        2 => '499',
        3 => '822',
      ),
    ),
    'eth' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'eth',
      'B' => 
      array (
        0 => '35',
        1 => '-15',
        2 => '522',
        3 => '737',
      ),
    ),
    'zcaron' => 
    array (
      'C' => '-1',
      'WX' => '500',
      'N' => 'zcaron',
      'B' => 
      array (
        0 => '31',
        1 => '0',
        2 => '469',
        3 => '734',
      ),
    ),
    'ncommaaccent' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'ncommaaccent',
      'B' => 
      array (
        0 => '65',
        1 => '-225',
        2 => '491',
        3 => '538',
      ),
    ),
    'onesuperior' => 
    array (
      'C' => '-1',
      'WX' => '333',
      'N' => 'onesuperior',
      'B' => 
      array (
        0 => '43',
        1 => '281',
        2 => '222',
        3 => '703',
      ),
    ),
    'imacron' => 
    array (
      'C' => '-1',
      'WX' => '278',
      'N' => 'imacron',
      'B' => 
      array (
        0 => '5',
        1 => '0',
        2 => '272',
        3 => '684',
      ),
    ),
    'Euro' => 
    array (
      'C' => '-1',
      'WX' => '556',
      'N' => 'Euro',
      'B' => 
      array (
        0 => '0',
        1 => '0',
        2 => '0',
        3 => '0',
      ),
    ),
  ),
  'KPX' => 
  array (
    'A' => 
    array (
      'C' => '-30',
      'Cacute' => '-30',
      'Ccaron' => '-30',
      'Ccedilla' => '-30',
      'G' => '-30',
      'Gbreve' => '-30',
      'Gcommaaccent' => '-30',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'Q' => '-30',
      'T' => '-120',
      'Tcaron' => '-120',
      'Tcommaaccent' => '-120',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-70',
      'W' => '-50',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-40',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'Aacute' => 
    array (
      'C' => '-30',
      'Cacute' => '-30',
      'Ccaron' => '-30',
      'Ccedilla' => '-30',
      'G' => '-30',
      'Gbreve' => '-30',
      'Gcommaaccent' => '-30',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'Q' => '-30',
      'T' => '-120',
      'Tcaron' => '-120',
      'Tcommaaccent' => '-120',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-70',
      'W' => '-50',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-40',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'Abreve' => 
    array (
      'C' => '-30',
      'Cacute' => '-30',
      'Ccaron' => '-30',
      'Ccedilla' => '-30',
      'G' => '-30',
      'Gbreve' => '-30',
      'Gcommaaccent' => '-30',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'Q' => '-30',
      'T' => '-120',
      'Tcaron' => '-120',
      'Tcommaaccent' => '-120',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-70',
      'W' => '-50',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-40',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'Acircumflex' => 
    array (
      'C' => '-30',
      'Cacute' => '-30',
      'Ccaron' => '-30',
      'Ccedilla' => '-30',
      'G' => '-30',
      'Gbreve' => '-30',
      'Gcommaaccent' => '-30',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'Q' => '-30',
      'T' => '-120',
      'Tcaron' => '-120',
      'Tcommaaccent' => '-120',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-70',
      'W' => '-50',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-40',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'Adieresis' => 
    array (
      'C' => '-30',
      'Cacute' => '-30',
      'Ccaron' => '-30',
      'Ccedilla' => '-30',
      'G' => '-30',
      'Gbreve' => '-30',
      'Gcommaaccent' => '-30',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'Q' => '-30',
      'T' => '-120',
      'Tcaron' => '-120',
      'Tcommaaccent' => '-120',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-70',
      'W' => '-50',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-40',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'Agrave' => 
    array (
      'C' => '-30',
      'Cacute' => '-30',
      'Ccaron' => '-30',
      'Ccedilla' => '-30',
      'G' => '-30',
      'Gbreve' => '-30',
      'Gcommaaccent' => '-30',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'Q' => '-30',
      'T' => '-120',
      'Tcaron' => '-120',
      'Tcommaaccent' => '-120',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-70',
      'W' => '-50',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-40',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'Amacron' => 
    array (
      'C' => '-30',
      'Cacute' => '-30',
      'Ccaron' => '-30',
      'Ccedilla' => '-30',
      'G' => '-30',
      'Gbreve' => '-30',
      'Gcommaaccent' => '-30',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'Q' => '-30',
      'T' => '-120',
      'Tcaron' => '-120',
      'Tcommaaccent' => '-120',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-70',
      'W' => '-50',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-40',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'Aogonek' => 
    array (
      'C' => '-30',
      'Cacute' => '-30',
      'Ccaron' => '-30',
      'Ccedilla' => '-30',
      'G' => '-30',
      'Gbreve' => '-30',
      'Gcommaaccent' => '-30',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'Q' => '-30',
      'T' => '-120',
      'Tcaron' => '-120',
      'Tcommaaccent' => '-120',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-70',
      'W' => '-50',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-40',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'Aring' => 
    array (
      'C' => '-30',
      'Cacute' => '-30',
      'Ccaron' => '-30',
      'Ccedilla' => '-30',
      'G' => '-30',
      'Gbreve' => '-30',
      'Gcommaaccent' => '-30',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'Q' => '-30',
      'T' => '-120',
      'Tcaron' => '-120',
      'Tcommaaccent' => '-120',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-70',
      'W' => '-50',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-40',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'Atilde' => 
    array (
      'C' => '-30',
      'Cacute' => '-30',
      'Ccaron' => '-30',
      'Ccedilla' => '-30',
      'G' => '-30',
      'Gbreve' => '-30',
      'Gcommaaccent' => '-30',
      'O' => '-30',
      'Oacute' => '-30',
      'Ocircumflex' => '-30',
      'Odieresis' => '-30',
      'Ograve' => '-30',
      'Ohungarumlaut' => '-30',
      'Omacron' => '-30',
      'Oslash' => '-30',
      'Otilde' => '-30',
      'Q' => '-30',
      'T' => '-120',
      'Tcaron' => '-120',
      'Tcommaaccent' => '-120',
      'U' => '-50',
      'Uacute' => '-50',
      'Ucircumflex' => '-50',
      'Udieresis' => '-50',
      'Ugrave' => '-50',
      'Uhungarumlaut' => '-50',
      'Umacron' => '-50',
      'Uogonek' => '-50',
      'Uring' => '-50',
      'V' => '-70',
      'W' => '-50',
      'Y' => '-100',
      'Yacute' => '-100',
      'Ydieresis' => '-100',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'v' => '-40',
      'w' => '-40',
      'y' => '-40',
      'yacute' => '-40',
      'ydieresis' => '-40',
    ),
    'B' => 
    array (
      'U' => '-10',
      'Uacute' => '-10',
      'Ucircumflex' => '-10',
      'Udieresis' => '-10',
      'Ugrave' => '-10',
      'Uhungarumlaut' => '-10',
      'Umacron' => '-10',
      'Uogonek' => '-10',
      'Uring' => '-10',
      'comma' => '-20',
      'period' => '-20',
    ),
    'C' => 
    array (
      'comma' => '-30',
      'period' => '-30',
    ),
    'Cacute' => 
    array (
      'comma' => '-30',
      'period' => '-30',
    ),
    'Ccaron' => 
    array (
      'comma' => '-30',
      'period' => '-30',
    ),
    'Ccedilla' => 
    array (
      'comma' => '-30',
      'period' => '-30',
    ),
    'D' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'V' => '-70',
      'W' => '-40',
      'Y' => '-90',
      'Yacute' => '-90',
      'Ydieresis' => '-90',
      'comma' => '-70',
      'period' => '-70',
    ),
    'Dcaron' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'V' => '-70',
      'W' => '-40',
      'Y' => '-90',
      'Yacute' => '-90',
      'Ydieresis' => '-90',
      'comma' => '-70',
      'period' => '-70',
    ),
    'Dcroat' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'V' => '-70',
      'W' => '-40',
      'Y' => '-90',
      'Yacute' => '-90',
      'Ydieresis' => '-90',
      'comma' => '-70',
      'period' => '-70',
    ),
    'F' => 
    array (
      'A' => '-80',
      'Aacute' => '-80',
      'Abreve' => '-80',
      'Acircumflex' => '-80',
      'Adieresis' => '-80',
      'Agrave' => '-80',
      'Amacron' => '-80',
      'Aogonek' => '-80',
      'Aring' => '-80',
      'Atilde' => '-80',
      'a' => '-50',
      'aacute' => '-50',
      'abreve' => '-50',
      'acircumflex' => '-50',
      'adieresis' => '-50',
      'agrave' => '-50',
      'amacron' => '-50',
      'aogonek' => '-50',
      'aring' => '-50',
      'atilde' => '-50',
      'comma' => '-150',
      'e' => '-30',
      'eacute' => '-30',
      'ecaron' => '-30',
      'ecircumflex' => '-30',
      'edieresis' => '-30',
      'edotaccent' => '-30',
      'egrave' => '-30',
      'emacron' => '-30',
      'eogonek' => '-30',
      'o' => '-30',
      'oacute' => '-30',
      'ocircumflex' => '-30',
      'odieresis' => '-30',
      'ograve' => '-30',
      'ohungarumlaut' => '-30',
      'omacron' => '-30',
      'oslash' => '-30',
      'otilde' => '-30',
      'period' => '-150',
      'r' => '-45',
      'racute' => '-45',
      'rcaron' => '-45',
      'rcommaaccent' => '-45',
    ),
    'J' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
      'a' => '-20',
      'aacute' => '-20',
      'abreve' => '-20',
      'acircumflex' => '-20',
      'adieresis' => '-20',
      'agrave' => '-20',
      'amacron' => '-20',
      'aogonek' => '-20',
      'aring' => '-20',
      'atilde' => '-20',
      'comma' => '-30',
      'period' => '-30',
      'u' => '-20',
      'uacute' => '-20',
      'ucircumflex' => '-20',
      'udieresis' => '-20',
      'ugrave' => '-20',
      'uhungarumlaut' => '-20',
      'umacron' => '-20',
      'uogonek' => '-20',
      'uring' => '-20',
    ),
    'K' => 
    array (
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'e' => '-40',
      'eacute' => '-40',
      'ecaron' => '-40',
      'ecircumflex' => '-40',
      'edieresis' => '-40',
      'edotaccent' => '-40',
      'egrave' => '-40',
      'emacron' => '-40',
      'eogonek' => '-40',
      'o' => '-40',
      'oacute' => '-40',
      'ocircumflex' => '-40',
      'odieresis' => '-40',
      'ograve' => '-40',
      'ohungarumlaut' => '-40',
      'omacron' => '-40',
      'oslash' => '-40',
      'otilde' => '-40',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'y' => '-50',
      'yacute' => '-50',
      'ydieresis' => '-50',
    ),
    'Kcommaaccent' => 
    array (
      'O' => '-50',
      'Oacute' => '-50',
      'Ocircumflex' => '-50',
      'Odieresis' => '-50',
      'Ograve' => '-50',
      'Ohungarumlaut' => '-50',
      'Omacron' => '-50',
      'Oslash' => '-50',
      'Otilde' => '-50',
      'e' => '-40',
      'eacute' => '-40',
      'ecaron' => '-40',
      'ecircumflex' => '-40',
      'edieresis' => '-40',
      'edotaccent' => '-40',
      'egrave' => '-40',
      'emacron' => '-40',
      'eogonek' => '-40',
      'o' => '-40',
      'oacute' => '-40',
      'ocircumflex' => '-40',
      'odieresis' => '-40',
      'ograve' => '-40',
      'ohungarumlaut' => '-40',
      'omacron' => '-40',
      'oslash' => '-40',
      'otilde' => '-40',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'y' => '-50',
      'yacute' => '-50',
      'ydieresis' => '-50',
    ),
    'L' => 
    array (
      'T' => '-110',
      'Tcaron' => '-110',
      'Tcommaaccent' => '-110',
      'V' => '-110',
      'W' => '-70',
      'Y' => '-140',
      'Yacute' => '-140',
      'Ydieresis' => '-140',
      'quotedblright' => '-140',
      'quoteright' => '-160',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Lacute' => 
    array (
      'T' => '-110',
      'Tcaron' => '-110',
      'Tcommaaccent' => '-110',
      'V' => '-110',
      'W' => '-70',
      'Y' => '-140',
      'Yacute' => '-140',
      'Ydieresis' => '-140',
      'quotedblright' => '-140',
      'quoteright' => '-160',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Lcaron' => 
    array (
      'T' => '-110',
      'Tcaron' => '-110',
      'Tcommaaccent' => '-110',
      'V' => '-110',
      'W' => '-70',
      'Y' => '-140',
      'Yacute' => '-140',
      'Ydieresis' => '-140',
      'quotedblright' => '-140',
      'quoteright' => '-160',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Lcommaaccent' => 
    array (
      'T' => '-110',
      'Tcaron' => '-110',
      'Tcommaaccent' => '-110',
      'V' => '-110',
      'W' => '-70',
      'Y' => '-140',
      'Yacute' => '-140',
      'Ydieresis' => '-140',
      'quotedblright' => '-140',
      'quoteright' => '-160',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'Lslash' => 
    array (
      'T' => '-110',
      'Tcaron' => '-110',
      'Tcommaaccent' => '-110',
      'V' => '-110',
      'W' => '-70',
      'Y' => '-140',
      'Yacute' => '-140',
      'Ydieresis' => '-140',
      'quotedblright' => '-140',
      'quoteright' => '-160',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'O' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-30',
      'X' => '-60',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Oacute' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-30',
      'X' => '-60',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Ocircumflex' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-30',
      'X' => '-60',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Odieresis' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-30',
      'X' => '-60',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Ograve' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-30',
      'X' => '-60',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Ohungarumlaut' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-30',
      'X' => '-60',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Omacron' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-30',
      'X' => '-60',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Oslash' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-30',
      'X' => '-60',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Otilde' => 
    array (
      'A' => '-20',
      'Aacute' => '-20',
      'Abreve' => '-20',
      'Acircumflex' => '-20',
      'Adieresis' => '-20',
      'Agrave' => '-20',
      'Amacron' => '-20',
      'Aogonek' => '-20',
      'Aring' => '-20',
      'Atilde' => '-20',
      'T' => '-40',
      'Tcaron' => '-40',
      'Tcommaaccent' => '-40',
      'V' => '-50',
      'W' => '-30',
      'X' => '-60',
      'Y' => '-70',
      'Yacute' => '-70',
      'Ydieresis' => '-70',
      'comma' => '-40',
      'period' => '-40',
    ),
    'P' => 
    array (
      'A' => '-120',
      'Aacute' => '-120',
      'Abreve' => '-120',
      'Acircumflex' => '-120',
      'Adieresis' => '-120',
      'Agrave' => '-120',
      'Amacron' => '-120',
      'Aogonek' => '-120',
      'Aring' => '-120',
      'Atilde' => '-120',
      'a' => '-40',
      'aacute' => '-40',
      'abreve' => '-40',
      'acircumflex' => '-40',
      'adieresis' => '-40',
      'agrave' => '-40',
      'amacron' => '-40',
      'aogonek' => '-40',
      'aring' => '-40',
      'atilde' => '-40',
      'comma' => '-180',
      'e' => '-50',
      'eacute' => '-50',
      'ecaron' => '-50',
      'ecircumflex' => '-50',
      'edieresis' => '-50',
      'edotaccent' => '-50',
      'egrave' => '-50',
      'emacron' => '-50',
      'eogonek' => '-50',
      'o' => '-50',
      'oacute' => '-50',
      'ocircumflex' => '-50',
      'odieresis' => '-50',
      'ograve' => '-50',
      'ohungarumlaut' => '-50',
      'omacron' => '-50',
      'oslash' => '-50',
      'otilde' => '-50',
      'period' => '-180',
    ),
    'Q' => 
    array (
      'U' => '-10',
      'Uacute' => '-10',
      'Ucircumflex' => '-10',
      'Udieresis' => '-10',
      'Ugrave' => '-10',
      'Uhungarumlaut' => '-10',
      'Umacron' => '-10',
      'Uogonek' => '-10',
      'Uring' => '-10',
    ),
    'R' => 
    array (
      'O' => '-20',
      'Oacute' => '-20',
      'Ocircumflex' => '-20',
      'Odieresis' => '-20',
      'Ograve' => '-20',
      'Ohungarumlaut' => '-20',
      'Omacron' => '-20',
      'Oslash' => '-20',
      'Otilde' => '-20',
      'T' => '-30',
      'Tcaron' => '-30',
      'Tcommaaccent' => '-30',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-50',
      'W' => '-30',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Racute' => 
    array (
      'O' => '-20',
      'Oacute' => '-20',
      'Ocircumflex' => '-20',
      'Odieresis' => '-20',
      'Ograve' => '-20',
      'Ohungarumlaut' => '-20',
      'Omacron' => '-20',
      'Oslash' => '-20',
      'Otilde' => '-20',
      'T' => '-30',
      'Tcaron' => '-30',
      'Tcommaaccent' => '-30',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-50',
      'W' => '-30',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Rcaron' => 
    array (
      'O' => '-20',
      'Oacute' => '-20',
      'Ocircumflex' => '-20',
      'Odieresis' => '-20',
      'Ograve' => '-20',
      'Ohungarumlaut' => '-20',
      'Omacron' => '-20',
      'Oslash' => '-20',
      'Otilde' => '-20',
      'T' => '-30',
      'Tcaron' => '-30',
      'Tcommaaccent' => '-30',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-50',
      'W' => '-30',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'Rcommaaccent' => 
    array (
      'O' => '-20',
      'Oacute' => '-20',
      'Ocircumflex' => '-20',
      'Odieresis' => '-20',
      'Ograve' => '-20',
      'Ohungarumlaut' => '-20',
      'Omacron' => '-20',
      'Oslash' => '-20',
      'Otilde' => '-20',
      'T' => '-30',
      'Tcaron' => '-30',
      'Tcommaaccent' => '-30',
      'U' => '-40',
      'Uacute' => '-40',
      'Ucircumflex' => '-40',
      'Udieresis' => '-40',
      'Ugrave' => '-40',
      'Uhungarumlaut' => '-40',
      'Umacron' => '-40',
      'Uogonek' => '-40',
      'Uring' => '-40',
      'V' => '-50',
      'W' => '-30',
      'Y' => '-50',
      'Yacute' => '-50',
      'Ydieresis' => '-50',
    ),
    'S' => 
    array (
      'comma' => '-20',
      'period' => '-20',
    ),
    'Sacute' => 
    array (
      'comma' => '-20',
      'period' => '-20',
    ),
    'Scaron' => 
    array (
      'comma' => '-20',
      'period' => '-20',
    ),
    'Scedilla' => 
    array (
      'comma' => '-20',
      'period' => '-20',
    ),
    'Scommaaccent' => 
    array (
      'comma' => '-20',
      'period' => '-20',
    ),
    'T' => 
    array (
      'A' => '-120',
      'Aacute' => '-120',
      'Abreve' => '-120',
      'Acircumflex' => '-120',
      'Adieresis' => '-120',
      'Agrave' => '-120',
      'Amacron' => '-120',
      'Aogonek' => '-120',
      'Aring' => '-120',
      'Atilde' => '-120',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'a' => '-120',
      'aacute' => '-120',
      'abreve' => '-60',
      'acircumflex' => '-120',
      'adieresis' => '-120',
      'agrave' => '-120',
      'amacron' => '-60',
      'aogonek' => '-120',
      'aring' => '-120',
      'atilde' => '-60',
      'colon' => '-20',
      'comma' => '-120',
      'e' => '-120',
      'eacute' => '-120',
      'ecaron' => '-120',
      'ecircumflex' => '-120',
      'edieresis' => '-120',
      'edotaccent' => '-120',
      'egrave' => '-60',
      'emacron' => '-60',
      'eogonek' => '-120',
      'hyphen' => '-140',
      'o' => '-120',
      'oacute' => '-120',
      'ocircumflex' => '-120',
      'odieresis' => '-120',
      'ograve' => '-120',
      'ohungarumlaut' => '-120',
      'omacron' => '-60',
      'oslash' => '-120',
      'otilde' => '-60',
      'period' => '-120',
      'r' => '-120',
      'racute' => '-120',
      'rcaron' => '-120',
      'rcommaaccent' => '-120',
      'semicolon' => '-20',
      'u' => '-120',
      'uacute' => '-120',
      'ucircumflex' => '-120',
      'udieresis' => '-120',
      'ugrave' => '-120',
      'uhungarumlaut' => '-120',
      'umacron' => '-60',
      'uogonek' => '-120',
      'uring' => '-120',
      'w' => '-120',
      'y' => '-120',
      'yacute' => '-120',
      'ydieresis' => '-60',
    ),
    'Tcaron' => 
    array (
      'A' => '-120',
      'Aacute' => '-120',
      'Abreve' => '-120',
      'Acircumflex' => '-120',
      'Adieresis' => '-120',
      'Agrave' => '-120',
      'Amacron' => '-120',
      'Aogonek' => '-120',
      'Aring' => '-120',
      'Atilde' => '-120',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'a' => '-120',
      'aacute' => '-120',
      'abreve' => '-60',
      'acircumflex' => '-120',
      'adieresis' => '-120',
      'agrave' => '-120',
      'amacron' => '-60',
      'aogonek' => '-120',
      'aring' => '-120',
      'atilde' => '-60',
      'colon' => '-20',
      'comma' => '-120',
      'e' => '-120',
      'eacute' => '-120',
      'ecaron' => '-120',
      'ecircumflex' => '-120',
      'edieresis' => '-120',
      'edotaccent' => '-120',
      'egrave' => '-60',
      'emacron' => '-60',
      'eogonek' => '-120',
      'hyphen' => '-140',
      'o' => '-120',
      'oacute' => '-120',
      'ocircumflex' => '-120',
      'odieresis' => '-120',
      'ograve' => '-120',
      'ohungarumlaut' => '-120',
      'omacron' => '-60',
      'oslash' => '-120',
      'otilde' => '-60',
      'period' => '-120',
      'r' => '-120',
      'racute' => '-120',
      'rcaron' => '-120',
      'rcommaaccent' => '-120',
      'semicolon' => '-20',
      'u' => '-120',
      'uacute' => '-120',
      'ucircumflex' => '-120',
      'udieresis' => '-120',
      'ugrave' => '-120',
      'uhungarumlaut' => '-120',
      'umacron' => '-60',
      'uogonek' => '-120',
      'uring' => '-120',
      'w' => '-120',
      'y' => '-120',
      'yacute' => '-120',
      'ydieresis' => '-60',
    ),
    'Tcommaaccent' => 
    array (
      'A' => '-120',
      'Aacute' => '-120',
      'Abreve' => '-120',
      'Acircumflex' => '-120',
      'Adieresis' => '-120',
      'Agrave' => '-120',
      'Amacron' => '-120',
      'Aogonek' => '-120',
      'Aring' => '-120',
      'Atilde' => '-120',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'a' => '-120',
      'aacute' => '-120',
      'abreve' => '-60',
      'acircumflex' => '-120',
      'adieresis' => '-120',
      'agrave' => '-120',
      'amacron' => '-60',
      'aogonek' => '-120',
      'aring' => '-120',
      'atilde' => '-60',
      'colon' => '-20',
      'comma' => '-120',
      'e' => '-120',
      'eacute' => '-120',
      'ecaron' => '-120',
      'ecircumflex' => '-120',
      'edieresis' => '-120',
      'edotaccent' => '-120',
      'egrave' => '-60',
      'emacron' => '-60',
      'eogonek' => '-120',
      'hyphen' => '-140',
      'o' => '-120',
      'oacute' => '-120',
      'ocircumflex' => '-120',
      'odieresis' => '-120',
      'ograve' => '-120',
      'ohungarumlaut' => '-120',
      'omacron' => '-60',
      'oslash' => '-120',
      'otilde' => '-60',
      'period' => '-120',
      'r' => '-120',
      'racute' => '-120',
      'rcaron' => '-120',
      'rcommaaccent' => '-120',
      'semicolon' => '-20',
      'u' => '-120',
      'uacute' => '-120',
      'ucircumflex' => '-120',
      'udieresis' => '-120',
      'ugrave' => '-120',
      'uhungarumlaut' => '-120',
      'umacron' => '-60',
      'uogonek' => '-120',
      'uring' => '-120',
      'w' => '-120',
      'y' => '-120',
      'yacute' => '-120',
      'ydieresis' => '-60',
    ),
    'U' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Uacute' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Ucircumflex' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Udieresis' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Ugrave' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Uhungarumlaut' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Umacron' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Uogonek' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'comma' => '-40',
      'period' => '-40',
    ),
    'Uring' => 
    array (
      'A' => '-40',
      'Aacute' => '-40',
      'Abreve' => '-40',
      'Acircumflex' => '-40',
      'Adieresis' => '-40',
      'Agrave' => '-40',
      'Amacron' => '-40',
      'Aogonek' => '-40',
      'Aring' => '-40',
      'Atilde' => '-40',
      'comma' => '-40',
      'period' => '-40',
    ),
    'V' => 
    array (
      'A' => '-80',
      'Aacute' => '-80',
      'Abreve' => '-80',
      'Acircumflex' => '-80',
      'Adieresis' => '-80',
      'Agrave' => '-80',
      'Amacron' => '-80',
      'Aogonek' => '-80',
      'Aring' => '-80',
      'Atilde' => '-80',
      'G' => '-40',
      'Gbreve' => '-40',
      'Gcommaaccent' => '-40',
      'O' => '-40',
      'Oacute' => '-40',
      'Ocircumflex' => '-40',
      'Odieresis' => '-40',
      'Ograve' => '-40',
      'Ohungarumlaut' => '-40',
      'Omacron' => '-40',
      'Oslash' => '-40',
      'Otilde' => '-40',
      'a' => '-70',
      'aacute' => '-70',
      'abreve' => '-70',
      'acircumflex' => '-70',
      'adieresis' => '-70',
      'agrave' => '-70',
      'amacron' => '-70',
      'aogonek' => '-70',
      'aring' => '-70',
      'atilde' => '-70',
      'colon' => '-40',
      'comma' => '-125',
      'e' => '-80',
      'eacute' => '-80',
      'ecaron' => '-80',
      'ecircumflex' => '-80',
      'edieresis' => '-80',
      'edotaccent' => '-80',
      'egrave' => '-80',
      'emacron' => '-80',
      'eogonek' => '-80',
      'hyphen' => '-80',
      'o' => '-80',
      'oacute' => '-80',
      'ocircumflex' => '-80',
      'odieresis' => '-80',
      'ograve' => '-80',
      'ohungarumlaut' => '-80',
      'omacron' => '-80',
      'oslash' => '-80',
      'otilde' => '-80',
      'period' => '-125',
      'semicolon' => '-40',
      'u' => '-70',
      'uacute' => '-70',
      'ucircumflex' => '-70',
      'udieresis' => '-70',
      'ugrave' => '-70',
      'uhungarumlaut' => '-70',
      'umacron' => '-70',
      'uogonek' => '-70',
      'uring' => '-70',
    ),
    'W' => 
    array (
      'A' => '-50',
      'Aacute' => '-50',
      'Abreve' => '-50',
      'Acircumflex' => '-50',
      'Adieresis' => '-50',
      'Agrave' => '-50',
      'Amacron' => '-50',
      'Aogonek' => '-50',
      'Aring' => '-50',
      'Atilde' => '-50',
      'O' => '-20',
      'Oacute' => '-20',
      'Ocircumflex' => '-20',
      'Odieresis' => '-20',
      'Ograve' => '-20',
      'Ohungarumlaut' => '-20',
      'Omacron' => '-20',
      'Oslash' => '-20',
      'Otilde' => '-20',
      'a' => '-40',
      'aacute' => '-40',
      'abreve' => '-40',
      'acircumflex' => '-40',
      'adieresis' => '-40',
      'agrave' => '-40',
      'amacron' => '-40',
      'aogonek' => '-40',
      'aring' => '-40',
      'atilde' => '-40',
      'comma' => '-80',
      'e' => '-30',
      'eacute' => '-30',
      'ecaron' => '-30',
      'ecircumflex' => '-30',
      'edieresis' => '-30',
      'edotaccent' => '-30',
      'egrave' => '-30',
      'emacron' => '-30',
      'eogonek' => '-30',
      'hyphen' => '-40',
      'o' => '-30',
      'oacute' => '-30',
      'ocircumflex' => '-30',
      'odieresis' => '-30',
      'ograve' => '-30',
      'ohungarumlaut' => '-30',
      'omacron' => '-30',
      'oslash' => '-30',
      'otilde' => '-30',
      'period' => '-80',
      'u' => '-30',
      'uacute' => '-30',
      'ucircumflex' => '-30',
      'udieresis' => '-30',
      'ugrave' => '-30',
      'uhungarumlaut' => '-30',
      'umacron' => '-30',
      'uogonek' => '-30',
      'uring' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'Y' => 
    array (
      'A' => '-110',
      'Aacute' => '-110',
      'Abreve' => '-110',
      'Acircumflex' => '-110',
      'Adieresis' => '-110',
      'Agrave' => '-110',
      'Amacron' => '-110',
      'Aogonek' => '-110',
      'Aring' => '-110',
      'Atilde' => '-110',
      'O' => '-85',
      'Oacute' => '-85',
      'Ocircumflex' => '-85',
      'Odieresis' => '-85',
      'Ograve' => '-85',
      'Ohungarumlaut' => '-85',
      'Omacron' => '-85',
      'Oslash' => '-85',
      'Otilde' => '-85',
      'a' => '-140',
      'aacute' => '-140',
      'abreve' => '-70',
      'acircumflex' => '-140',
      'adieresis' => '-140',
      'agrave' => '-140',
      'amacron' => '-70',
      'aogonek' => '-140',
      'aring' => '-140',
      'atilde' => '-140',
      'colon' => '-60',
      'comma' => '-140',
      'e' => '-140',
      'eacute' => '-140',
      'ecaron' => '-140',
      'ecircumflex' => '-140',
      'edieresis' => '-140',
      'edotaccent' => '-140',
      'egrave' => '-140',
      'emacron' => '-70',
      'eogonek' => '-140',
      'hyphen' => '-140',
      'i' => '-20',
      'iacute' => '-20',
      'iogonek' => '-20',
      'o' => '-140',
      'oacute' => '-140',
      'ocircumflex' => '-140',
      'odieresis' => '-140',
      'ograve' => '-140',
      'ohungarumlaut' => '-140',
      'omacron' => '-140',
      'oslash' => '-140',
      'otilde' => '-140',
      'period' => '-140',
      'semicolon' => '-60',
      'u' => '-110',
      'uacute' => '-110',
      'ucircumflex' => '-110',
      'udieresis' => '-110',
      'ugrave' => '-110',
      'uhungarumlaut' => '-110',
      'umacron' => '-110',
      'uogonek' => '-110',
      'uring' => '-110',
    ),
    'Yacute' => 
    array (
      'A' => '-110',
      'Aacute' => '-110',
      'Abreve' => '-110',
      'Acircumflex' => '-110',
      'Adieresis' => '-110',
      'Agrave' => '-110',
      'Amacron' => '-110',
      'Aogonek' => '-110',
      'Aring' => '-110',
      'Atilde' => '-110',
      'O' => '-85',
      'Oacute' => '-85',
      'Ocircumflex' => '-85',
      'Odieresis' => '-85',
      'Ograve' => '-85',
      'Ohungarumlaut' => '-85',
      'Omacron' => '-85',
      'Oslash' => '-85',
      'Otilde' => '-85',
      'a' => '-140',
      'aacute' => '-140',
      'abreve' => '-70',
      'acircumflex' => '-140',
      'adieresis' => '-140',
      'agrave' => '-140',
      'amacron' => '-70',
      'aogonek' => '-140',
      'aring' => '-140',
      'atilde' => '-70',
      'colon' => '-60',
      'comma' => '-140',
      'e' => '-140',
      'eacute' => '-140',
      'ecaron' => '-140',
      'ecircumflex' => '-140',
      'edieresis' => '-140',
      'edotaccent' => '-140',
      'egrave' => '-140',
      'emacron' => '-70',
      'eogonek' => '-140',
      'hyphen' => '-140',
      'i' => '-20',
      'iacute' => '-20',
      'iogonek' => '-20',
      'o' => '-140',
      'oacute' => '-140',
      'ocircumflex' => '-140',
      'odieresis' => '-140',
      'ograve' => '-140',
      'ohungarumlaut' => '-140',
      'omacron' => '-70',
      'oslash' => '-140',
      'otilde' => '-140',
      'period' => '-140',
      'semicolon' => '-60',
      'u' => '-110',
      'uacute' => '-110',
      'ucircumflex' => '-110',
      'udieresis' => '-110',
      'ugrave' => '-110',
      'uhungarumlaut' => '-110',
      'umacron' => '-110',
      'uogonek' => '-110',
      'uring' => '-110',
    ),
    'Ydieresis' => 
    array (
      'A' => '-110',
      'Aacute' => '-110',
      'Abreve' => '-110',
      'Acircumflex' => '-110',
      'Adieresis' => '-110',
      'Agrave' => '-110',
      'Amacron' => '-110',
      'Aogonek' => '-110',
      'Aring' => '-110',
      'Atilde' => '-110',
      'O' => '-85',
      'Oacute' => '-85',
      'Ocircumflex' => '-85',
      'Odieresis' => '-85',
      'Ograve' => '-85',
      'Ohungarumlaut' => '-85',
      'Omacron' => '-85',
      'Oslash' => '-85',
      'Otilde' => '-85',
      'a' => '-140',
      'aacute' => '-140',
      'abreve' => '-70',
      'acircumflex' => '-140',
      'adieresis' => '-140',
      'agrave' => '-140',
      'amacron' => '-70',
      'aogonek' => '-140',
      'aring' => '-140',
      'atilde' => '-70',
      'colon' => '-60',
      'comma' => '-140',
      'e' => '-140',
      'eacute' => '-140',
      'ecaron' => '-140',
      'ecircumflex' => '-140',
      'edieresis' => '-140',
      'edotaccent' => '-140',
      'egrave' => '-140',
      'emacron' => '-70',
      'eogonek' => '-140',
      'hyphen' => '-140',
      'i' => '-20',
      'iacute' => '-20',
      'iogonek' => '-20',
      'o' => '-140',
      'oacute' => '-140',
      'ocircumflex' => '-140',
      'odieresis' => '-140',
      'ograve' => '-140',
      'ohungarumlaut' => '-140',
      'omacron' => '-140',
      'oslash' => '-140',
      'otilde' => '-140',
      'period' => '-140',
      'semicolon' => '-60',
      'u' => '-110',
      'uacute' => '-110',
      'ucircumflex' => '-110',
      'udieresis' => '-110',
      'ugrave' => '-110',
      'uhungarumlaut' => '-110',
      'umacron' => '-110',
      'uogonek' => '-110',
      'uring' => '-110',
    ),
    'a' => 
    array (
      'v' => '-20',
      'w' => '-20',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'aacute' => 
    array (
      'v' => '-20',
      'w' => '-20',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'abreve' => 
    array (
      'v' => '-20',
      'w' => '-20',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'acircumflex' => 
    array (
      'v' => '-20',
      'w' => '-20',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'adieresis' => 
    array (
      'v' => '-20',
      'w' => '-20',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'agrave' => 
    array (
      'v' => '-20',
      'w' => '-20',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'amacron' => 
    array (
      'v' => '-20',
      'w' => '-20',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'aogonek' => 
    array (
      'v' => '-20',
      'w' => '-20',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'aring' => 
    array (
      'v' => '-20',
      'w' => '-20',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'atilde' => 
    array (
      'v' => '-20',
      'w' => '-20',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'b' => 
    array (
      'b' => '-10',
      'comma' => '-40',
      'l' => '-20',
      'lacute' => '-20',
      'lcommaaccent' => '-20',
      'lslash' => '-20',
      'period' => '-40',
      'u' => '-20',
      'uacute' => '-20',
      'ucircumflex' => '-20',
      'udieresis' => '-20',
      'ugrave' => '-20',
      'uhungarumlaut' => '-20',
      'umacron' => '-20',
      'uogonek' => '-20',
      'uring' => '-20',
      'v' => '-20',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'c' => 
    array (
      'comma' => '-15',
      'k' => '-20',
      'kcommaaccent' => '-20',
    ),
    'cacute' => 
    array (
      'comma' => '-15',
      'k' => '-20',
      'kcommaaccent' => '-20',
    ),
    'ccaron' => 
    array (
      'comma' => '-15',
      'k' => '-20',
      'kcommaaccent' => '-20',
    ),
    'ccedilla' => 
    array (
      'comma' => '-15',
      'k' => '-20',
      'kcommaaccent' => '-20',
    ),
    'colon' => 
    array (
      'space' => '-50',
    ),
    'comma' => 
    array (
      'quotedblright' => '-100',
      'quoteright' => '-100',
    ),
    'e' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'v' => '-30',
      'w' => '-20',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'eacute' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'v' => '-30',
      'w' => '-20',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'ecaron' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'v' => '-30',
      'w' => '-20',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'ecircumflex' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'v' => '-30',
      'w' => '-20',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'edieresis' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'v' => '-30',
      'w' => '-20',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'edotaccent' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'v' => '-30',
      'w' => '-20',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'egrave' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'v' => '-30',
      'w' => '-20',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'emacron' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'v' => '-30',
      'w' => '-20',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'eogonek' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'v' => '-30',
      'w' => '-20',
      'x' => '-30',
      'y' => '-20',
      'yacute' => '-20',
      'ydieresis' => '-20',
    ),
    'f' => 
    array (
      'a' => '-30',
      'aacute' => '-30',
      'abreve' => '-30',
      'acircumflex' => '-30',
      'adieresis' => '-30',
      'agrave' => '-30',
      'amacron' => '-30',
      'aogonek' => '-30',
      'aring' => '-30',
      'atilde' => '-30',
      'comma' => '-30',
      'dotlessi' => '-28',
      'e' => '-30',
      'eacute' => '-30',
      'ecaron' => '-30',
      'ecircumflex' => '-30',
      'edieresis' => '-30',
      'edotaccent' => '-30',
      'egrave' => '-30',
      'emacron' => '-30',
      'eogonek' => '-30',
      'o' => '-30',
      'oacute' => '-30',
      'ocircumflex' => '-30',
      'odieresis' => '-30',
      'ograve' => '-30',
      'ohungarumlaut' => '-30',
      'omacron' => '-30',
      'oslash' => '-30',
      'otilde' => '-30',
      'period' => '-30',
      'quotedblright' => '60',
      'quoteright' => '50',
    ),
    'g' => 
    array (
      'r' => '-10',
      'racute' => '-10',
      'rcaron' => '-10',
      'rcommaaccent' => '-10',
    ),
    'gbreve' => 
    array (
      'r' => '-10',
      'racute' => '-10',
      'rcaron' => '-10',
      'rcommaaccent' => '-10',
    ),
    'gcommaaccent' => 
    array (
      'r' => '-10',
      'racute' => '-10',
      'rcaron' => '-10',
      'rcommaaccent' => '-10',
    ),
    'h' => 
    array (
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'k' => 
    array (
      'e' => '-20',
      'eacute' => '-20',
      'ecaron' => '-20',
      'ecircumflex' => '-20',
      'edieresis' => '-20',
      'edotaccent' => '-20',
      'egrave' => '-20',
      'emacron' => '-20',
      'eogonek' => '-20',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
    ),
    'kcommaaccent' => 
    array (
      'e' => '-20',
      'eacute' => '-20',
      'ecaron' => '-20',
      'ecircumflex' => '-20',
      'edieresis' => '-20',
      'edotaccent' => '-20',
      'egrave' => '-20',
      'emacron' => '-20',
      'eogonek' => '-20',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
    ),
    'm' => 
    array (
      'u' => '-10',
      'uacute' => '-10',
      'ucircumflex' => '-10',
      'udieresis' => '-10',
      'ugrave' => '-10',
      'uhungarumlaut' => '-10',
      'umacron' => '-10',
      'uogonek' => '-10',
      'uring' => '-10',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'n' => 
    array (
      'u' => '-10',
      'uacute' => '-10',
      'ucircumflex' => '-10',
      'udieresis' => '-10',
      'ugrave' => '-10',
      'uhungarumlaut' => '-10',
      'umacron' => '-10',
      'uogonek' => '-10',
      'uring' => '-10',
      'v' => '-20',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'nacute' => 
    array (
      'u' => '-10',
      'uacute' => '-10',
      'ucircumflex' => '-10',
      'udieresis' => '-10',
      'ugrave' => '-10',
      'uhungarumlaut' => '-10',
      'umacron' => '-10',
      'uogonek' => '-10',
      'uring' => '-10',
      'v' => '-20',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ncaron' => 
    array (
      'u' => '-10',
      'uacute' => '-10',
      'ucircumflex' => '-10',
      'udieresis' => '-10',
      'ugrave' => '-10',
      'uhungarumlaut' => '-10',
      'umacron' => '-10',
      'uogonek' => '-10',
      'uring' => '-10',
      'v' => '-20',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ncommaaccent' => 
    array (
      'u' => '-10',
      'uacute' => '-10',
      'ucircumflex' => '-10',
      'udieresis' => '-10',
      'ugrave' => '-10',
      'uhungarumlaut' => '-10',
      'umacron' => '-10',
      'uogonek' => '-10',
      'uring' => '-10',
      'v' => '-20',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'ntilde' => 
    array (
      'u' => '-10',
      'uacute' => '-10',
      'ucircumflex' => '-10',
      'udieresis' => '-10',
      'ugrave' => '-10',
      'uhungarumlaut' => '-10',
      'umacron' => '-10',
      'uogonek' => '-10',
      'uring' => '-10',
      'v' => '-20',
      'y' => '-15',
      'yacute' => '-15',
      'ydieresis' => '-15',
    ),
    'o' => 
    array (
      'comma' => '-40',
      'period' => '-40',
      'v' => '-15',
      'w' => '-15',
      'x' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'oacute' => 
    array (
      'comma' => '-40',
      'period' => '-40',
      'v' => '-15',
      'w' => '-15',
      'x' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'ocircumflex' => 
    array (
      'comma' => '-40',
      'period' => '-40',
      'v' => '-15',
      'w' => '-15',
      'x' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'odieresis' => 
    array (
      'comma' => '-40',
      'period' => '-40',
      'v' => '-15',
      'w' => '-15',
      'x' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'ograve' => 
    array (
      'comma' => '-40',
      'period' => '-40',
      'v' => '-15',
      'w' => '-15',
      'x' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'ohungarumlaut' => 
    array (
      'comma' => '-40',
      'period' => '-40',
      'v' => '-15',
      'w' => '-15',
      'x' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'omacron' => 
    array (
      'comma' => '-40',
      'period' => '-40',
      'v' => '-15',
      'w' => '-15',
      'x' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'oslash' => 
    array (
      'a' => '-55',
      'aacute' => '-55',
      'abreve' => '-55',
      'acircumflex' => '-55',
      'adieresis' => '-55',
      'agrave' => '-55',
      'amacron' => '-55',
      'aogonek' => '-55',
      'aring' => '-55',
      'atilde' => '-55',
      'b' => '-55',
      'c' => '-55',
      'cacute' => '-55',
      'ccaron' => '-55',
      'ccedilla' => '-55',
      'comma' => '-95',
      'd' => '-55',
      'dcroat' => '-55',
      'e' => '-55',
      'eacute' => '-55',
      'ecaron' => '-55',
      'ecircumflex' => '-55',
      'edieresis' => '-55',
      'edotaccent' => '-55',
      'egrave' => '-55',
      'emacron' => '-55',
      'eogonek' => '-55',
      'f' => '-55',
      'g' => '-55',
      'gbreve' => '-55',
      'gcommaaccent' => '-55',
      'h' => '-55',
      'i' => '-55',
      'iacute' => '-55',
      'icircumflex' => '-55',
      'idieresis' => '-55',
      'igrave' => '-55',
      'imacron' => '-55',
      'iogonek' => '-55',
      'j' => '-55',
      'k' => '-55',
      'kcommaaccent' => '-55',
      'l' => '-55',
      'lacute' => '-55',
      'lcommaaccent' => '-55',
      'lslash' => '-55',
      'm' => '-55',
      'n' => '-55',
      'nacute' => '-55',
      'ncaron' => '-55',
      'ncommaaccent' => '-55',
      'ntilde' => '-55',
      'o' => '-55',
      'oacute' => '-55',
      'ocircumflex' => '-55',
      'odieresis' => '-55',
      'ograve' => '-55',
      'ohungarumlaut' => '-55',
      'omacron' => '-55',
      'oslash' => '-55',
      'otilde' => '-55',
      'p' => '-55',
      'period' => '-95',
      'q' => '-55',
      'r' => '-55',
      'racute' => '-55',
      'rcaron' => '-55',
      'rcommaaccent' => '-55',
      's' => '-55',
      'sacute' => '-55',
      'scaron' => '-55',
      'scedilla' => '-55',
      'scommaaccent' => '-55',
      't' => '-55',
      'tcommaaccent' => '-55',
      'u' => '-55',
      'uacute' => '-55',
      'ucircumflex' => '-55',
      'udieresis' => '-55',
      'ugrave' => '-55',
      'uhungarumlaut' => '-55',
      'umacron' => '-55',
      'uogonek' => '-55',
      'uring' => '-55',
      'v' => '-70',
      'w' => '-70',
      'x' => '-85',
      'y' => '-70',
      'yacute' => '-70',
      'ydieresis' => '-70',
      'z' => '-55',
      'zacute' => '-55',
      'zcaron' => '-55',
      'zdotaccent' => '-55',
    ),
    'otilde' => 
    array (
      'comma' => '-40',
      'period' => '-40',
      'v' => '-15',
      'w' => '-15',
      'x' => '-30',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'p' => 
    array (
      'comma' => '-35',
      'period' => '-35',
      'y' => '-30',
      'yacute' => '-30',
      'ydieresis' => '-30',
    ),
    'period' => 
    array (
      'quotedblright' => '-100',
      'quoteright' => '-100',
      'space' => '-60',
    ),
    'quotedblright' => 
    array (
      'space' => '-40',
    ),
    'quoteleft' => 
    array (
      'quoteleft' => '-57',
    ),
    'quoteright' => 
    array (
      'd' => '-50',
      'dcroat' => '-50',
      'quoteright' => '-57',
      'r' => '-50',
      'racute' => '-50',
      'rcaron' => '-50',
      'rcommaaccent' => '-50',
      's' => '-50',
      'sacute' => '-50',
      'scaron' => '-50',
      'scedilla' => '-50',
      'scommaaccent' => '-50',
      'space' => '-70',
    ),
    'r' => 
    array (
      'a' => '-10',
      'aacute' => '-10',
      'abreve' => '-10',
      'acircumflex' => '-10',
      'adieresis' => '-10',
      'agrave' => '-10',
      'amacron' => '-10',
      'aogonek' => '-10',
      'aring' => '-10',
      'atilde' => '-10',
      'colon' => '30',
      'comma' => '-50',
      'i' => '15',
      'iacute' => '15',
      'icircumflex' => '15',
      'idieresis' => '15',
      'igrave' => '15',
      'imacron' => '15',
      'iogonek' => '15',
      'k' => '15',
      'kcommaaccent' => '15',
      'l' => '15',
      'lacute' => '15',
      'lcommaaccent' => '15',
      'lslash' => '15',
      'm' => '25',
      'n' => '25',
      'nacute' => '25',
      'ncaron' => '25',
      'ncommaaccent' => '25',
      'ntilde' => '25',
      'p' => '30',
      'period' => '-50',
      'semicolon' => '30',
      't' => '40',
      'tcommaaccent' => '40',
      'u' => '15',
      'uacute' => '15',
      'ucircumflex' => '15',
      'udieresis' => '15',
      'ugrave' => '15',
      'uhungarumlaut' => '15',
      'umacron' => '15',
      'uogonek' => '15',
      'uring' => '15',
      'v' => '30',
      'y' => '30',
      'yacute' => '30',
      'ydieresis' => '30',
    ),
    'racute' => 
    array (
      'a' => '-10',
      'aacute' => '-10',
      'abreve' => '-10',
      'acircumflex' => '-10',
      'adieresis' => '-10',
      'agrave' => '-10',
      'amacron' => '-10',
      'aogonek' => '-10',
      'aring' => '-10',
      'atilde' => '-10',
      'colon' => '30',
      'comma' => '-50',
      'i' => '15',
      'iacute' => '15',
      'icircumflex' => '15',
      'idieresis' => '15',
      'igrave' => '15',
      'imacron' => '15',
      'iogonek' => '15',
      'k' => '15',
      'kcommaaccent' => '15',
      'l' => '15',
      'lacute' => '15',
      'lcommaaccent' => '15',
      'lslash' => '15',
      'm' => '25',
      'n' => '25',
      'nacute' => '25',
      'ncaron' => '25',
      'ncommaaccent' => '25',
      'ntilde' => '25',
      'p' => '30',
      'period' => '-50',
      'semicolon' => '30',
      't' => '40',
      'tcommaaccent' => '40',
      'u' => '15',
      'uacute' => '15',
      'ucircumflex' => '15',
      'udieresis' => '15',
      'ugrave' => '15',
      'uhungarumlaut' => '15',
      'umacron' => '15',
      'uogonek' => '15',
      'uring' => '15',
      'v' => '30',
      'y' => '30',
      'yacute' => '30',
      'ydieresis' => '30',
    ),
    'rcaron' => 
    array (
      'a' => '-10',
      'aacute' => '-10',
      'abreve' => '-10',
      'acircumflex' => '-10',
      'adieresis' => '-10',
      'agrave' => '-10',
      'amacron' => '-10',
      'aogonek' => '-10',
      'aring' => '-10',
      'atilde' => '-10',
      'colon' => '30',
      'comma' => '-50',
      'i' => '15',
      'iacute' => '15',
      'icircumflex' => '15',
      'idieresis' => '15',
      'igrave' => '15',
      'imacron' => '15',
      'iogonek' => '15',
      'k' => '15',
      'kcommaaccent' => '15',
      'l' => '15',
      'lacute' => '15',
      'lcommaaccent' => '15',
      'lslash' => '15',
      'm' => '25',
      'n' => '25',
      'nacute' => '25',
      'ncaron' => '25',
      'ncommaaccent' => '25',
      'ntilde' => '25',
      'p' => '30',
      'period' => '-50',
      'semicolon' => '30',
      't' => '40',
      'tcommaaccent' => '40',
      'u' => '15',
      'uacute' => '15',
      'ucircumflex' => '15',
      'udieresis' => '15',
      'ugrave' => '15',
      'uhungarumlaut' => '15',
      'umacron' => '15',
      'uogonek' => '15',
      'uring' => '15',
      'v' => '30',
      'y' => '30',
      'yacute' => '30',
      'ydieresis' => '30',
    ),
    'rcommaaccent' => 
    array (
      'a' => '-10',
      'aacute' => '-10',
      'abreve' => '-10',
      'acircumflex' => '-10',
      'adieresis' => '-10',
      'agrave' => '-10',
      'amacron' => '-10',
      'aogonek' => '-10',
      'aring' => '-10',
      'atilde' => '-10',
      'colon' => '30',
      'comma' => '-50',
      'i' => '15',
      'iacute' => '15',
      'icircumflex' => '15',
      'idieresis' => '15',
      'igrave' => '15',
      'imacron' => '15',
      'iogonek' => '15',
      'k' => '15',
      'kcommaaccent' => '15',
      'l' => '15',
      'lacute' => '15',
      'lcommaaccent' => '15',
      'lslash' => '15',
      'm' => '25',
      'n' => '25',
      'nacute' => '25',
      'ncaron' => '25',
      'ncommaaccent' => '25',
      'ntilde' => '25',
      'p' => '30',
      'period' => '-50',
      'semicolon' => '30',
      't' => '40',
      'tcommaaccent' => '40',
      'u' => '15',
      'uacute' => '15',
      'ucircumflex' => '15',
      'udieresis' => '15',
      'ugrave' => '15',
      'uhungarumlaut' => '15',
      'umacron' => '15',
      'uogonek' => '15',
      'uring' => '15',
      'v' => '30',
      'y' => '30',
      'yacute' => '30',
      'ydieresis' => '30',
    ),
    's' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'w' => '-30',
    ),
    'sacute' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'w' => '-30',
    ),
    'scaron' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'w' => '-30',
    ),
    'scedilla' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'w' => '-30',
    ),
    'scommaaccent' => 
    array (
      'comma' => '-15',
      'period' => '-15',
      'w' => '-30',
    ),
    'semicolon' => 
    array (
      'space' => '-50',
    ),
    'space' => 
    array (
      'T' => '-50',
      'Tcaron' => '-50',
      'Tcommaaccent' => '-50',
      'V' => '-50',
      'W' => '-40',
      'Y' => '-90',
      'Yacute' => '-90',
      'Ydieresis' => '-90',
      'quotedblleft' => '-30',
      'quoteleft' => '-60',
    ),
    'v' => 
    array (
      'a' => '-25',
      'aacute' => '-25',
      'abreve' => '-25',
      'acircumflex' => '-25',
      'adieresis' => '-25',
      'agrave' => '-25',
      'amacron' => '-25',
      'aogonek' => '-25',
      'aring' => '-25',
      'atilde' => '-25',
      'comma' => '-80',
      'e' => '-25',
      'eacute' => '-25',
      'ecaron' => '-25',
      'ecircumflex' => '-25',
      'edieresis' => '-25',
      'edotaccent' => '-25',
      'egrave' => '-25',
      'emacron' => '-25',
      'eogonek' => '-25',
      'o' => '-25',
      'oacute' => '-25',
      'ocircumflex' => '-25',
      'odieresis' => '-25',
      'ograve' => '-25',
      'ohungarumlaut' => '-25',
      'omacron' => '-25',
      'oslash' => '-25',
      'otilde' => '-25',
      'period' => '-80',
    ),
    'w' => 
    array (
      'a' => '-15',
      'aacute' => '-15',
      'abreve' => '-15',
      'acircumflex' => '-15',
      'adieresis' => '-15',
      'agrave' => '-15',
      'amacron' => '-15',
      'aogonek' => '-15',
      'aring' => '-15',
      'atilde' => '-15',
      'comma' => '-60',
      'e' => '-10',
      'eacute' => '-10',
      'ecaron' => '-10',
      'ecircumflex' => '-10',
      'edieresis' => '-10',
      'edotaccent' => '-10',
      'egrave' => '-10',
      'emacron' => '-10',
      'eogonek' => '-10',
      'o' => '-10',
      'oacute' => '-10',
      'ocircumflex' => '-10',
      'odieresis' => '-10',
      'ograve' => '-10',
      'ohungarumlaut' => '-10',
      'omacron' => '-10',
      'oslash' => '-10',
      'otilde' => '-10',
      'period' => '-60',
    ),
    'x' => 
    array (
      'e' => '-30',
      'eacute' => '-30',
      'ecaron' => '-30',
      'ecircumflex' => '-30',
      'edieresis' => '-30',
      'edotaccent' => '-30',
      'egrave' => '-30',
      'emacron' => '-30',
      'eogonek' => '-30',
    ),
    'y' => 
    array (
      'a' => '-20',
      'aacute' => '-20',
      'abreve' => '-20',
      'acircumflex' => '-20',
      'adieresis' => '-20',
      'agrave' => '-20',
      'amacron' => '-20',
      'aogonek' => '-20',
      'aring' => '-20',
      'atilde' => '-20',
      'comma' => '-100',
      'e' => '-20',
      'eacute' => '-20',
      'ecaron' => '-20',
      'ecircumflex' => '-20',
      'edieresis' => '-20',
      'edotaccent' => '-20',
      'egrave' => '-20',
      'emacron' => '-20',
      'eogonek' => '-20',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
      'period' => '-100',
    ),
    'yacute' => 
    array (
      'a' => '-20',
      'aacute' => '-20',
      'abreve' => '-20',
      'acircumflex' => '-20',
      'adieresis' => '-20',
      'agrave' => '-20',
      'amacron' => '-20',
      'aogonek' => '-20',
      'aring' => '-20',
      'atilde' => '-20',
      'comma' => '-100',
      'e' => '-20',
      'eacute' => '-20',
      'ecaron' => '-20',
      'ecircumflex' => '-20',
      'edieresis' => '-20',
      'edotaccent' => '-20',
      'egrave' => '-20',
      'emacron' => '-20',
      'eogonek' => '-20',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
      'period' => '-100',
    ),
    'ydieresis' => 
    array (
      'a' => '-20',
      'aacute' => '-20',
      'abreve' => '-20',
      'acircumflex' => '-20',
      'adieresis' => '-20',
      'agrave' => '-20',
      'amacron' => '-20',
      'aogonek' => '-20',
      'aring' => '-20',
      'atilde' => '-20',
      'comma' => '-100',
      'e' => '-20',
      'eacute' => '-20',
      'ecaron' => '-20',
      'ecircumflex' => '-20',
      'edieresis' => '-20',
      'edotaccent' => '-20',
      'egrave' => '-20',
      'emacron' => '-20',
      'eogonek' => '-20',
      'o' => '-20',
      'oacute' => '-20',
      'ocircumflex' => '-20',
      'odieresis' => '-20',
      'ograve' => '-20',
      'ohungarumlaut' => '-20',
      'omacron' => '-20',
      'oslash' => '-20',
      'otilde' => '-20',
      'period' => '-100',
    ),
    'z' => 
    array (
      'e' => '-15',
      'eacute' => '-15',
      'ecaron' => '-15',
      'ecircumflex' => '-15',
      'edieresis' => '-15',
      'edotaccent' => '-15',
      'egrave' => '-15',
      'emacron' => '-15',
      'eogonek' => '-15',
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
    ),
    'zacute' => 
    array (
      'e' => '-15',
      'eacute' => '-15',
      'ecaron' => '-15',
      'ecircumflex' => '-15',
      'edieresis' => '-15',
      'edotaccent' => '-15',
      'egrave' => '-15',
      'emacron' => '-15',
      'eogonek' => '-15',
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
    ),
    'zcaron' => 
    array (
      'e' => '-15',
      'eacute' => '-15',
      'ecaron' => '-15',
      'ecircumflex' => '-15',
      'edieresis' => '-15',
      'edotaccent' => '-15',
      'egrave' => '-15',
      'emacron' => '-15',
      'eogonek' => '-15',
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
    ),
    'zdotaccent' => 
    array (
      'e' => '-15',
      'eacute' => '-15',
      'ecaron' => '-15',
      'ecircumflex' => '-15',
      'edieresis' => '-15',
      'edotaccent' => '-15',
      'egrave' => '-15',
      'emacron' => '-15',
      'eogonek' => '-15',
      'o' => '-15',
      'oacute' => '-15',
      'ocircumflex' => '-15',
      'odieresis' => '-15',
      'ograve' => '-15',
      'ohungarumlaut' => '-15',
      'omacron' => '-15',
      'oslash' => '-15',
      'otilde' => '-15',
    ),
  ),
  '_version_' => 1,
);