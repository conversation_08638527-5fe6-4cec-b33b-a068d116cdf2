/**
 * PWA Install Prompt Manager
 * Handles app installation prompts and user engagement
 */

class PWAInstallManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.installButton = null;
        this.installBanner = null;
        
        this.init();
    }
    
    init() {
        this.checkInstallStatus();
        this.setupEventListeners();
        this.createInstallElements();
        this.trackInstallMetrics();
    }
    
    /**
     * Check if app is already installed
     */
    checkInstallStatus() {
        // Check if running in standalone mode (installed)
        this.isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                          window.navigator.standalone ||
                          document.referrer.includes('android-app://');
        
        if (this.isInstalled) {
            console.log('PWA: App is already installed');
            this.hideInstallPrompts();
        }
    }
    
    /**
     * Setup event listeners for PWA installation
     */
    setupEventListeners() {
        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: beforeinstallprompt event fired');
            
            // Prevent the mini-infobar from appearing on mobile
            e.preventDefault();
            
            // Store the event for later use
            this.deferredPrompt = e;
            
            // Show custom install prompt
            this.showInstallPrompt();
        });
        
        // Listen for app installed event
        window.addEventListener('appinstalled', (e) => {
            console.log('PWA: App was installed');
            this.isInstalled = true;
            this.hideInstallPrompts();
            this.trackInstallEvent('installed');
            this.showInstallSuccessMessage();
        });
        
        // Listen for display mode changes
        window.matchMedia('(display-mode: standalone)').addEventListener('change', (e) => {
            if (e.matches) {
                console.log('PWA: App is running in standalone mode');
                this.isInstalled = true;
                this.hideInstallPrompts();
            }
        });
    }
    
    /**
     * Create install UI elements
     */
    createInstallElements() {
        // Create install banner
        this.createInstallBanner();
        
        // Create install button
        this.createInstallButton();
        
        // Create install modal
        this.createInstallModal();
    }
    
    /**
     * Create install banner
     */
    createInstallBanner() {
        const bannerHTML = `
            <div id="pwa-install-banner" class="pwa-install-banner" style="display: none;">
                <div class="install-banner-content">
                    <div class="install-banner-icon">
                        <img src="img/pwa/icon-72x72.png" alt="Vasavi Matrimony" class="banner-app-icon">
                    </div>
                    <div class="install-banner-text">
                        <h4>Install Vasavi Matrimony</h4>
                        <p>Get the full app experience with offline access and notifications</p>
                    </div>
                    <div class="install-banner-actions">
                        <button class="btn btn-primary btn-sm" id="pwa-install-btn">Install</button>
                        <button class="btn btn-link btn-sm" id="pwa-dismiss-btn">×</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('afterbegin', bannerHTML);
        this.installBanner = document.getElementById('pwa-install-banner');
        
        // Add event listeners
        document.getElementById('pwa-install-btn').addEventListener('click', () => {
            this.showInstallModal();
        });
        
        document.getElementById('pwa-dismiss-btn').addEventListener('click', () => {
            this.dismissInstallPrompt();
        });
    }
    
    /**
     * Create floating install button
     */
    createInstallButton() {
        const buttonHTML = `
            <div id="pwa-floating-install" class="pwa-floating-install" style="display: none;">
                <button class="floating-install-btn" title="Install App">
                    <i class="fas fa-download"></i>
                </button>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', buttonHTML);
        
        document.querySelector('.floating-install-btn').addEventListener('click', () => {
            this.showInstallModal();
        });
    }
    
    /**
     * Create install modal
     */
    createInstallModal() {
        const modalHTML = `
            <div id="pwa-install-modal" class="modal fade" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <img src="img/pwa/icon-72x72.png" alt="App Icon" class="modal-app-icon">
                                Install Vasavi Matrimony
                            </h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="install-benefits">
                                <div class="benefit-item">
                                    <i class="fas fa-bolt text-warning"></i>
                                    <span>Faster loading and better performance</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-wifi text-info"></i>
                                    <span>Works offline with cached content</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-bell text-success"></i>
                                    <span>Push notifications for new matches</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-mobile-alt text-primary"></i>
                                    <span>Native app-like experience</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-shield-alt text-danger"></i>
                                    <span>Secure and private</span>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Maybe Later</button>
                            <button type="button" class="btn btn-primary" id="pwa-confirm-install">Install Now</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        document.getElementById('pwa-confirm-install').addEventListener('click', () => {
            this.installApp();
        });
    }
    
    /**
     * Show install prompt
     */
    showInstallPrompt() {
        if (this.isInstalled || !this.deferredPrompt) return;
        
        // Check if user has dismissed the prompt recently
        const dismissedTime = localStorage.getItem('pwa-install-dismissed');
        if (dismissedTime) {
            const daysSinceDismissed = (Date.now() - parseInt(dismissedTime)) / (1000 * 60 * 60 * 24);
            if (daysSinceDismissed < 7) { // Don't show for 7 days after dismissal
                return;
            }
        }
        
        // Show banner after a delay
        setTimeout(() => {
            if (this.installBanner) {
                this.installBanner.style.display = 'block';
                this.installBanner.classList.add('slide-down');
            }
        }, 3000);
        
        // Show floating button after more delay
        setTimeout(() => {
            const floatingBtn = document.getElementById('pwa-floating-install');
            if (floatingBtn) {
                floatingBtn.style.display = 'block';
                floatingBtn.classList.add('bounce-in');
            }
        }, 10000);
        
        this.trackInstallEvent('prompt_shown');
    }
    
    /**
     * Show install modal
     */
    showInstallModal() {
        $('#pwa-install-modal').modal('show');
        this.trackInstallEvent('modal_opened');
    }
    
    /**
     * Install the app
     */
    async installApp() {
        if (!this.deferredPrompt) {
            console.log('PWA: No deferred prompt available');
            return;
        }
        
        try {
            // Show the install prompt
            this.deferredPrompt.prompt();
            
            // Wait for the user to respond
            const { outcome } = await this.deferredPrompt.userChoice;
            
            console.log(`PWA: User response to install prompt: ${outcome}`);
            
            if (outcome === 'accepted') {
                this.trackInstallEvent('accepted');
                this.hideInstallPrompts();
            } else {
                this.trackInstallEvent('dismissed');
            }
            
            // Clear the deferred prompt
            this.deferredPrompt = null;
            
            // Hide modal
            $('#pwa-install-modal').modal('hide');
            
        } catch (error) {
            console.error('PWA: Error during installation:', error);
            this.trackInstallEvent('error');
        }
    }
    
    /**
     * Dismiss install prompt
     */
    dismissInstallPrompt() {
        this.hideInstallPrompts();
        localStorage.setItem('pwa-install-dismissed', Date.now().toString());
        this.trackInstallEvent('banner_dismissed');
    }
    
    /**
     * Hide all install prompts
     */
    hideInstallPrompts() {
        if (this.installBanner) {
            this.installBanner.style.display = 'none';
        }
        
        const floatingBtn = document.getElementById('pwa-floating-install');
        if (floatingBtn) {
            floatingBtn.style.display = 'none';
        }
    }
    
    /**
     * Show install success message
     */
    showInstallSuccessMessage() {
        const successHTML = `
            <div class="alert alert-success alert-dismissible fade show pwa-success-alert" role="alert">
                <i class="fas fa-check-circle"></i>
                <strong>App Installed Successfully!</strong> 
                You can now access Vasavi Matrimony from your home screen.
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;
        
        document.body.insertAdjacentHTML('afterbegin', successHTML);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            const alert = document.querySelector('.pwa-success-alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
    
    /**
     * Track install metrics
     */
    trackInstallMetrics() {
        // Track page views in standalone mode
        if (this.isInstalled) {
            this.trackInstallEvent('standalone_pageview');
        }
        
        // Track user engagement
        let engagementTime = 0;
        const startTime = Date.now();
        
        window.addEventListener('beforeunload', () => {
            engagementTime = Date.now() - startTime;
            if (engagementTime > 30000) { // More than 30 seconds
                this.trackInstallEvent('engaged_user');
            }
        });
    }
    
    /**
     * Track install events
     */
    trackInstallEvent(event) {
        // Send to analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'pwa_install', {
                event_category: 'PWA',
                event_label: event,
                value: 1
            });
        }
        
        // Send to your own analytics endpoint
        fetch('/api/track_pwa_event.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                event: event,
                timestamp: Date.now(),
                user_agent: navigator.userAgent,
                is_mobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
            })
        }).catch(error => {
            console.log('PWA: Analytics tracking failed', error);
        });
        
        console.log(`PWA: Event tracked - ${event}`);
    }
    
    /**
     * Check if device supports PWA installation
     */
    static isInstallSupported() {
        return 'serviceWorker' in navigator && 
               'PushManager' in window &&
               'Notification' in window;
    }
    
    /**
     * Get install prompt availability
     */
    isInstallAvailable() {
        return !this.isInstalled && this.deferredPrompt !== null;
    }
}

// CSS for PWA install elements
const pwaInstallCSS = `
.pwa-install-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #549a11, #4a8a0f);
    color: white;
    z-index: 9999;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.pwa-install-banner.slide-down {
    transform: translateY(0);
}

.install-banner-content {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.install-banner-icon {
    margin-right: 15px;
}

.banner-app-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

.install-banner-text {
    flex: 1;
}

.install-banner-text h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
}

.install-banner-text p {
    margin: 0;
    font-size: 13px;
    opacity: 0.9;
}

.install-banner-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.pwa-floating-install {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9998;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.pwa-floating-install.bounce-in {
    opacity: 1;
    transform: scale(1);
    animation: bounceIn 0.6s ease;
}

.floating-install-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: #549a11;
    color: white;
    border: none;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.floating-install-btn:hover {
    background: #4a8a0f;
    transform: scale(1.1);
}

.modal-app-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    margin-right: 10px;
}

.install-benefits {
    margin: 20px 0;
}

.benefit-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
}

.benefit-item i {
    width: 24px;
    margin-right: 12px;
    text-align: center;
}

.pwa-success-alert {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    animation: slideInRight 0.3s ease;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@media (max-width: 768px) {
    .install-banner-content {
        padding: 10px 15px;
    }
    
    .install-banner-text h4 {
        font-size: 14px;
    }
    
    .install-banner-text p {
        font-size: 12px;
    }
    
    .pwa-floating-install {
        bottom: 15px;
        right: 15px;
    }
    
    .floating-install-btn {
        width: 48px;
        height: 48px;
        font-size: 18px;
    }
}
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = pwaInstallCSS;
document.head.appendChild(style);

// Initialize PWA Install Manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (PWAInstallManager.isInstallSupported()) {
        window.pwaInstallManager = new PWAInstallManager();
    } else {
        console.log('PWA: Installation not supported on this device');
    }
});
