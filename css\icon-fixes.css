/* Icon Fixes for Matrimony Site */

/* ========================================
   GLYPHICON REPLACEMENT WITH BOOTSTRAP ICONS
   ======================================== */

/* Replace missing Glyphicons with Bootstrap Icons */
.gi, .glyphicon {
    font-family: "bootstrap-icons" !important;
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Common Glyphicon replacements */
.gi-user:before, .glyphicon-user:before { content: "\f4da"; }
.gi-envelope:before, .glyphicon-envelope:before { content: "\f32f"; }
.gi-lock:before, .glyphicon-lock:before { content: "\f3f1"; }
.gi-phone:before, .glyphicon-phone:before { content: "\f4b2"; }
.gi-calendar:before, .glyphicon-calendar:before { content: "\f1ec"; }
.gi-heart:before, .glyphicon-heart:before { content: "\f33e"; }
.gi-search:before, .glyphicon-search:before { content: "\f52a"; }
.gi-home:before, .glyphicon-home:before { content: "\f32d"; }
.gi-star:before, .glyphicon-star:before { content: "\f588"; }
.gi-star-empty:before, .glyphicon-star-empty:before { content: "\f586"; }
.gi-edit:before, .glyphicon-edit:before { content: "\f4ca"; }
.gi-trash:before, .glyphicon-trash:before { content: "\f5de"; }
.gi-plus:before, .glyphicon-plus:before { content: "\f4fe"; }
.gi-minus:before, .glyphicon-minus:before { content: "\f3f8"; }
.gi-ok:before, .glyphicon-ok:before { content: "\f26a"; }
.gi-remove:before, .glyphicon-remove:before { content: "\f659"; }
.gi-refresh:before, .glyphicon-refresh:before { content: "\f3f7"; }
.gi-download:before, .glyphicon-download:before { content: "\f2c6"; }
.gi-upload:before, .glyphicon-upload:before { content: "\f2c7"; }
.gi-camera:before, .glyphicon-camera:before { content: "\f1f5"; }
.gi-picture:before, .glyphicon-picture:before { content: "\f4b6"; }
.gi-map-marker:before, .glyphicon-map-marker:before { content: "\f3c5"; }
.gi-time:before, .glyphicon-time:before { content: "\f292"; }
.gi-eye-open:before, .glyphicon-eye-open:before { content: "\f341"; }
.gi-eye-close:before, .glyphicon-eye-close:before { content: "\f33f"; }
.gi-warning-sign:before, .glyphicon-warning-sign:before { content: "\f33a"; }
.gi-info-sign:before, .glyphicon-info-sign:before { content: "\f431"; }
.gi-question-sign:before, .glyphicon-question-sign:before { content: "\f4a6"; }
.gi-exclamation-sign:before, .glyphicon-exclamation-sign:before { content: "\f33a"; }

/* Loading and spinner icons */
.gi-loader:before, .glyphicon-loader:before { content: "\f3f7"; }
.gi-spin, .glyphicon-spin {
    animation: gi-spin 1s linear infinite;
}

@keyframes gi-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ========================================
   FONT AWESOME ICON FIXES
   ======================================== */

/* Fix for Font Awesome 6 compatibility */
.fa-mobile-alt:before { content: "\f3cd"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-user-circle:before { content: "\f2bd"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-walking:before { content: "\f554"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-graduation-cap:before { content: "\f19d"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-briefcase:before { content: "\f0b1"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-home:before { content: "\f015"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-heart:before { content: "\f004"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-envelope:before { content: "\f0e0"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-phone:before { content: "\f095"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-camera:before { content: "\f030"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-upload:before { content: "\f093"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-download:before { content: "\f019"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-search:before { content: "\f002"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-filter:before { content: "\f0b0"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-cog:before { content: "\f013"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-edit:before { content: "\f044"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-trash:before { content: "\f1f8"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-plus:before { content: "\f067"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-minus:before { content: "\f068"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-check:before { content: "\f00c"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-times:before { content: "\f00d"; font-family: "Font Awesome 6 Free"; font-weight: 900; }

/* PhonePe specific icons */
.fa-crown:before { content: "\f521"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-user:before { content: "\f007"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-shield-alt:before { content: "\f3ed"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-mobile-alt:before { content: "\f3cd"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-clock:before { content: "\f017"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-headset:before { content: "\f590"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-lock:before { content: "\f023"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-credit-card:before { content: "\f09d"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-arrow-left:before { content: "\f060"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-spinner:before { content: "\f110"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-exclamation-triangle:before { content: "\f071"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-check-circle:before { content: "\f058"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-times-circle:before { content: "\f057"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-info-circle:before { content: "\f05a"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-refresh:before { content: "\f021"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-retry:before { content: "\f021"; font-family: "Font Awesome 6 Free"; font-weight: 900; }

/* Payment and financial icons */
.fa-rupee-sign:before { content: "\f156"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-money-bill:before { content: "\f0d6"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-wallet:before { content: "\f555"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-receipt:before { content: "\f543"; font-family: "Font Awesome 6 Free"; font-weight: 900; }

/* Animation support */
.fa-spin {
    animation: fa-spin 1s linear infinite;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Social media icons - Font Awesome 6 Brand Icons */
.fa-facebook:before, .fab.fa-facebook:before { content: "\f09a"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-facebook-square:before, .fab.fa-facebook-square:before { content: "\f082"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-twitter:before, .fab.fa-twitter:before { content: "\f099"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-twitter-square:before, .fab.fa-twitter-square:before { content: "\f081"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-instagram:before, .fab.fa-instagram:before { content: "\f16d"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-linkedin:before, .fab.fa-linkedin:before { content: "\f08c"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-linkedin-square:before, .fab.fa-linkedin-square:before { content: "\f08c"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-pinterest:before, .fab.fa-pinterest:before { content: "\f0d2"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-pinterest-square:before, .fab.fa-pinterest-square:before { content: "\f0d3"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-youtube:before, .fab.fa-youtube:before { content: "\f167"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-whatsapp:before, .fab.fa-whatsapp:before { content: "\f232"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-google:before, .fab.fa-google:before { content: "\f1a0"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-google-plus:before, .fab.fa-google-plus:before { content: "\f2b3"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }

/* Font Awesome Brand Class Fix */
.fab {
    font-family: "Font Awesome 6 Brands" !important;
    font-weight: 400 !important;
    font-style: normal;
}

/* Ensure all brand icons use correct font family */
.fab.fa-facebook-square,
.fab.fa-twitter-square,
.fab.fa-linkedin,
.fab.fa-pinterest-square {
    font-family: "Font Awesome 6 Brands" !important;
    font-weight: 400 !important;
}

/* Legacy support for old class names */
.fa.fa-facebook-square { font-family: "Font Awesome 6 Brands" !important; font-weight: 400 !important; }
.fa.fa-twitter-square { font-family: "Font Awesome 6 Brands" !important; font-weight: 400 !important; }
.fa.fa-linkedin { font-family: "Font Awesome 6 Brands" !important; font-weight: 400 !important; }
.fa.fa-pinterest-square { font-family: "Font Awesome 6 Brands" !important; font-weight: 400 !important; }

/* ========================================
   FONT AWESOME FALLBACK SYSTEM
   ======================================== */

/* Ensure Font Awesome icons load correctly */
.fa, .fas, .far, .fal, .fab {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Brand icons specific font family */
.fab {
    font-family: "Font Awesome 6 Brands" !important;
    font-weight: 400 !important;
}

/* Light icons */
.fal {
    font-weight: 300 !important;
}

/* Regular icons */
.far {
    font-weight: 400 !important;
}

/* Solid icons (default) */
.fas, .fa {
    font-weight: 900 !important;
}

/* Force Font Awesome to load if kit fails */
@font-face {
    font-family: "Font Awesome 6 Free";
    src: url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2") format("woff2");
    font-weight: 900;
    font-style: normal;
}

@font-face {
    font-family: "Font Awesome 6 Brands";
    src: url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-brands-400.woff2") format("woff2");
    font-weight: 400;
    font-style: normal;
}

/* ========================================
   MODERN FORM ICON STYLES
   ======================================== */

/* Modern Form Input Icons */
.modern-form-group {
    position: relative;
    margin-bottom: 20px;
}

.modern-form-group .form-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff6b35;
    font-size: 18px;
    z-index: 2;
    width: 20px;
    text-align: center;
}

.modern-form-group .form-control,
.modern-form-group .gt-form-control {
    padding-left: 50px !important;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    height: 50px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fff;
}

.modern-form-group .form-control:focus,
.modern-form-group .gt-form-control:focus {
    border-color: #ff6b35;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    outline: none;
}

.modern-form-group select.form-control,
.modern-form-group select.gt-form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Modern Form Labels */
.modern-form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.modern-form-label .required {
    color: #ef4444;
    margin-right: 4px;
}

/* Modern Registration Card */
.modern-register-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    max-width: 500px;
}

.modern-register-form {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modern-register-title {
    text-align: center;
    color: #ff6b35;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 30px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Modern Submit Button */
.modern-submit-btn {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    padding: 15px 30px;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.modern-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(34, 197, 94, 0.3);
}

/* Modern Checkbox */
.modern-checkbox {
    display: flex;
    align-items: center;
    margin: 20px 0;
    font-size: 14px;
    color: #6b7280;
}

.modern-checkbox input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
    accent-color: #ff6b35;
}

/* Date Picker Row */
.date-picker-row {
    display: flex;
    gap: 10px;
}

.date-picker-row .modern-form-group {
    flex: 1;
}

/* Gender Selection */
.gender-selection {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.gender-option {
    flex: 1;
    position: relative;
}

.gender-option input[type="radio"] {
    display: none;
}

.gender-option label {
    display: block;
    padding: 12px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    background: white;
}

.gender-option input[type="radio"]:checked + label {
    border-color: #ff6b35;
    background: #ff6b35;
    color: white;
}

/* Phone Number Input */
.phone-input-group {
    display: flex;
    gap: 10px;
}

.phone-input-group .country-code {
    flex: 0 0 80px;
}

.phone-input-group .phone-number {
    flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-register-card {
        margin: 10px;
        padding: 20px;
    }

    .modern-register-form {
        padding: 20px;
    }

    .date-picker-row {
        flex-direction: column;
        gap: 0;
    }

    .gender-selection {
        flex-direction: column;
        gap: 10px;
    }

    .phone-input-group {
        flex-direction: column;
        gap: 0;
    }
}

/* ========================================
   ENHANCED FORM STYLING (PELLIPUS IMPROVEMENTS)
   ======================================== */

/* Modern Form Styling */
.gt-slideup-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 20px !important;
    padding: 30px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
    border: none !important;
}

.gt-slideUp-form-head {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 25px !important;
    backdrop-filter: blur(10px) !important;
}

.gt-slideUp-form-head h4 {
    color: white !important;
    font-weight: 700 !important;
    text-align: center !important;
    margin: 0 !important;
    font-size: 24px !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
}

.gt-slideUp-form-body {
    background: white !important;
    border-radius: 15px !important;
    padding: 25px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
}

/* Enhanced Input Group Styling */
.input-group-addon {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
    border: none !important;
    color: white !important;
    border-radius: 8px 0 0 8px !important;
    padding: 12px 15px !important;
    min-width: 45px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3) !important;
}

.input-group-addon i {
    font-size: 16px !important;
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Enhanced Form Controls */
.gt-form-control {
    border: 2px solid #e5e7eb !important;
    border-left: none !important;
    border-radius: 0 8px 8px 0 !important;
    padding: 12px 15px !important;
    font-size: 15px !important;
    transition: all 0.3s ease !important;
    background: #fafafa !important;
    height: auto !important;
    min-height: 45px !important;
}

.gt-form-control:focus {
    border-color: #ff6b35 !important;
    background: white !important;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1) !important;
    outline: none !important;
}

.gt-form-control::placeholder {
    color: #9ca3af !important;
    font-size: 14px !important;
}

/* Enhanced Input Groups */
.input-group {
    display: flex !important;
    width: 100% !important;
    margin-bottom: 15px !important;
}

.input-group .gt-form-control {
    flex: 1 !important;
}

/* Form Group Spacing */
.form-group {
    margin-bottom: 20px !important;
}

.gt-index-collab .input-group {
    display: flex !important;
    gap: 8px !important;
}

.gt-index-collab .form-1,
.gt-index-collab .form-2 {
    flex: 1 !important;
    border-radius: 8px !important;
    border: 2px solid #e5e7eb !important;
}

/* Enhanced Button Styling */
.inIndexRegBtn {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    color: white !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    padding: 15px 30px !important;
    width: 100% !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3) !important;
}

.inIndexRegBtn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4) !important;
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%) !important;
}

.inIndexRegBtn:active {
    transform: translateY(0) !important;
}

/* Terms and Conditions Styling */
.inTerms {
    display: flex !important;
    align-items: center !important;
    font-size: 14px !important;
    color: #6b7280 !important;
    margin: 20px 0 !important;
}

.inTerms input[type="checkbox"] {
    margin-right: 12px !important;
    transform: scale(1.3) !important;
    accent-color: #ff6b35 !important;
}

.inTerms a {
    color: #ff6b35 !important;
    text-decoration: none !important;
    font-weight: 600 !important;
}

.inTerms a:hover {
    text-decoration: underline !important;
}

/* ========================================
   CUSTOM ICON STYLES
   ======================================== */

/* Registration form icons */
.gtRegTitle i {
    color: #e47203;
    margin-right: 10px;
    font-size: 1.2em;
}

/* Form field icons */
.input-group-addon i {
    color: #499202;
    font-size: 16px;
}

/* Button icons */
.btn i {
    margin-right: 8px;
}

/* Panel header icons */
.gt-panel-head i {
    margin-right: 8px;
    color: inherit;
}

/* Navigation icons */
.navbar-nav i {
    margin-right: 5px;
}

/* Footer social icons */
.gt-footer-social i {
    font-size: 24px;
    transition: all 0.3s ease;
}

.gt-footer-social i:hover {
    transform: scale(1.1);
}

/* ========================================
   ICON SIZE CLASSES
   ======================================== */

.icon-xs { font-size: 0.75em; }
.icon-sm { font-size: 0.875em; }
.icon-lg { font-size: 1.25em; }
.icon-xl { font-size: 1.5em; }
.icon-2x { font-size: 2em; }
.icon-3x { font-size: 3em; }
.icon-4x { font-size: 4em; }
.icon-5x { font-size: 5em; }

/* ========================================
   ICON ANIMATIONS
   ======================================== */

.icon-spin {
    animation: icon-spin 1s linear infinite;
}

.icon-pulse {
    animation: icon-pulse 1s ease-in-out infinite alternate;
}

.icon-bounce {
    animation: icon-bounce 1s ease-in-out infinite;
}

@keyframes icon-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes icon-pulse {
    0% { opacity: 1; }
    100% { opacity: 0.5; }
}

@keyframes icon-bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* ========================================
   ICON COLOR THEMES
   ======================================== */

.icon-primary { color: #499202; }
.icon-secondary { color: #e47203; }
.icon-success { color: #5cb85c; }
.icon-info { color: #5bc0de; }
.icon-warning { color: #f0ad4e; }
.icon-danger { color: #d9534f; }
.icon-muted { color: #777; }
.icon-white { color: #fff; }

/* ========================================
   RESPONSIVE ICON ADJUSTMENTS
   ======================================== */

@media (max-width: 767px) {
    .gtRegTitle i {
        font-size: 1.1em;
        margin-right: 8px;
    }
    
    .gt-footer-social i {
        font-size: 20px;
    }
    
    .btn i {
        margin-right: 6px;
    }
}

@media (max-width: 480px) {
    .gtRegTitle i {
        font-size: 1em;
        margin-right: 6px;
    }
    
    .gt-footer-social i {
        font-size: 18px;
    }
}
