@charset "utf-8";
/* CSS Document */

@media (min-width: 768px) {
  .lead {
    font-size: 21px;
  }
}
@media (min-width: 768px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    overflow: hidden;
    clear: left;
    text-align: right;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .dl-horizontal dd {
    margin-left: 180px;
  }
}


.container {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 768px) {
  .container {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .container {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .container {
    width: 1170px;
  }
}
.container-fluid {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
.row {
  margin-right: -15px;
  margin-left: -15px;
}
.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xl-1, .col-xxl-1,
.col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xl-2, .col-xxl-2,
.col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xl-3, .col-xxl-3,
.col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xl-4, .col-xxl-4,
.col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xl-5, .col-xxl-5,
.col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xl-6, .col-xxl-6,
.col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xl-7, .col-xxl-7,
.col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xl-8, .col-xxl-8,
.col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xl-9, .col-xxl-9,
.col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xl-10, .col-xxl-10,
.col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xl-11, .col-xxl-11,
.col-xs-12, .col-sm-12, .col-md-12, .col-lg-12, .col-xl-12, .col-xxl-12,
.col-xs-13, .col-sm-13, .col-md-13, .col-lg-13, .col-xl-13, .col-xxl-13,
.col-xs-14, .col-sm-14, .col-md-14, .col-lg-14, .col-xl-14, .col-xxl-14,
.col-xs-15, .col-sm-15, .col-md-15, .col-lg-15, .col-xl-15, .col-xxl-15,
.col-xs-16, .col-sm-16, .col-md-16, .col-lg-16, .col-xl-16, .col-xxl-16 {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}
.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-13, .col-xs-14, .col-xs-15, .col-xs-16{
  float: left;
}
.col-xs-16 {
  width: 100%;
}
.col-xs-15 {
  width: 93.75%;
}
.col-xs-14 {
  width: 87.5%;
}
.col-xs-13 {
  width: 81.25%;
}
.col-xs-12 {
  width: 75%;
}
.col-xs-11 {
  width: 68.75%;
}
.col-xs-10 {
  width: 62.5%;
}
.col-xs-9 {
  width: 56.25%;
}
.col-xs-8 {
  width: 50%;
}
.col-xs-7 {
  width: 43.75%;
}
.col-xs-6 {
  width: 37.5%;
}
.col-xs-5 {
  width: 31.25%;
}
.col-xs-4 {
  width: 25%;
}
.col-xs-3 {
  width: 18.75%;
}
.col-xs-2 {
  width: 12.5%;
}
.col-xs-1 {
  width: 6.25%;
}

.col-xs-pull-16 {
  right: 100%;
}
.col-xs-pull-15 {
  right: 93.75%;
}
.col-xs-pull-14 {
  right: 87.5%;
}
.col-xs-pull-13 {
  right: 81.25%;
}
.col-xs-pull-12 {
  right: 75%;
}
.col-xs-pull-11 {
  right: 68.75%;
}
.col-xs-pull-10 {
  right: 62.5%;
}
.col-xs-pull-9 {
  right: 56.25%;
}
.col-xs-pull-8 {
  right: 50%;
}
.col-xs-pull-7 {
  right: 43.75%;
}
.col-xs-pull-6 {
  right: 37.5%;
}
.col-xs-pull-5 {
  right: 31.25%;
}
.col-xs-pull-4 {
  right: 25%;
}
.col-xs-pull-3 {
  right: 18.75%;
}
.col-xs-pull-2 {
  right: 12.5%;
}
.col-xs-pull-1 {
  right: 6.25%;
}
.col-xs-pull-0 {
  right: auto;
}

.col-xs-push-16 {
  left: 100%;
}
.col-xs-push-15 {
  left: 93.75%;
}
.col-xs-push-14 {
  left: 87.5%;
}
.col-xs-push-13 {
  left: 81.25%;
}
.col-xs-push-12 {
  left: 75%;
}
.col-xs-push-11 {
  left: 68.75%;
}
.col-xs-push-10 {
  left: 62.5%;
}
.col-xs-push-9 {
  left: 56.25%;
}
.col-xs-push-8 {
  left: 50%;
}
.col-xs-push-7 {
  left: 43.75%;
}
.col-xs-push-6 {
  left: 37.5%;
}
.col-xs-push-5 {
  left: 31.25%;
}
.col-xs-push-4 {
  left: 25%;
}
.col-xs-push-3 {
  left: 18.75%;
}
.col-xs-push-2 {
  left: 12.5%;
}
.col-xs-push-1 {
  left: 6.25%;
}
.col-xs-push-0 {
  left: auto;
}

.col-xs-offset-16 {
  margin-left: 100%;
}
.col-xs-offset-15 {
  margin-left: 93.75%;
}
.col-xs-offset-14 {
  margin-left: 87.5%;
}
.col-xs-offset-13 {
  margin-left: 81.25%;
}
.col-xs-offset-12 {
  margin-left: 75%;
}
.col-xs-offset-11 {
  margin-left: 68.75%;
}
.col-xs-offset-10 {
  margin-left: 62.5%;
}
.col-xs-offset-9 {
  margin-left: 56.25%;
}
.col-xs-offset-8 {
  margin-left: 50%;
}
.col-xs-offset-7 {
  margin-left: 43.75%;
}
.col-xs-offset-6 {
  margin-left: 37.5%;
}
.col-xs-offset-5 {
  margin-left: 31.25%;
}
.col-xs-offset-4 {
  margin-left: 25%;
}
.col-xs-offset-3 {
  margin-left: 18.75%;
}
.col-xs-offset-2 {
  margin-left: 12.5%;
}
.col-xs-offset-1 {
  margin-left: 6.25%;
}
.col-xs-offset-0 {
  margin-left: 0;
}

@media (min-width: 320px) {
 .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-13, .col-sm-14, .col-sm-15, .col-sm-16{
  float: left;
}
.col-sm-16 {
  width: 100%;
}
.col-sm-15 {
  width: 93.75%;
}
.col-sm-14 {
  width: 87.5%;
}
.col-sm-13 {
  width: 81.25%;
}
.col-sm-12 {
  width: 75%;
}
.col-sm-11 {
  width: 68.75%;
}
.col-sm-10 {
  width: 62.5%;
}
.col-sm-9 {
  width: 56.25%;
}
.col-sm-8 {
  width: 50%;
}
.col-sm-7 {
  width: 43.75%;
}
.col-sm-6 {
  width: 37.5%;
}
.col-sm-5 {
  width: 31.25%;
}
.col-sm-4 {
  width: 25%;
}
.col-sm-3 {
  width: 18.75%;
}
.col-sm-2 {
  width: 12.5%;
}
.col-sm-1 {
  width: 6.25%;
}

.col-sm-pull-16 {
  right: 100%;
}
.col-sm-pull-15 {
  right: 93.75%;
}
.col-sm-pull-14 {
  right: 87.5%;
}
.col-sm-pull-13 {
  right: 81.25%;
}
.col-sm-pull-12 {
  right: 75%;
}
.col-sm-pull-11 {
  right: 68.75%;
}
.col-sm-pull-10 {
  right: 62.5%;
}
.col-sm-pull-9 {
  right: 56.25%;
}
.col-sm-pull-8 {
  right: 50%;
}
.col-sm-pull-7 {
  right: 43.75%;
}
.col-sm-pull-6 {
  right: 37.5%;
}
.col-sm-pull-5 {
  right: 31.25%;
}
.col-sm-pull-4 {
  right: 25%;
}
.col-sm-pull-3 {
  right: 18.75%;
}
.col-sm-pull-2 {
  right: 12.5%;
}
.col-sm-pull-1 {
  right: 6.25%;
}
.col-sm-pull-0 {
  right: auto;
}

.col-sm-push-16 {
  left: 100%;
}
.col-sm-push-15 {
  left: 93.75%;
}
.col-sm-push-14 {
  left: 87.5%;
}
.col-sm-push-13 {
  left: 81.25%;
}
.col-sm-push-12 {
  left: 75%;
}
.col-sm-push-11 {
  left: 68.75%;
}
.col-sm-push-10 {
  left: 62.5%;
}
.col-sm-push-9 {
  left: 56.25%;
}
.col-sm-push-8 {
  left: 50%;
}
.col-sm-push-7 {
  left: 43.75%;
}
.col-sm-push-6 {
  left: 37.5%;
}
.col-sm-push-5 {
  left: 31.25%;
}
.col-sm-push-4 {
  left: 25%;
}
.col-sm-push-3 {
  left: 18.75%;
}
.col-sm-push-2 {
  left: 12.5%;
}
.col-sm-push-1 {
  left: 6.25%;
}
.col-sm-push-0 {
  left: auto;
}

.col-sm-offset-16 {
  margin-left: 100%;
}
.col-sm-offset-15 {
  margin-left: 93.75%;
}
.col-sm-offset-14 {
  margin-left: 87.5%;
}
.col-sm-offset-13 {
  margin-left: 81.25%;
}
.col-sm-offset-12 {
  margin-left: 75%;
}
.col-sm-offset-11 {
  margin-left: 68.75%;
}
.col-sm-offset-10 {
  margin-left: 62.5%;
}
.col-sm-offset-9 {
  margin-left: 56.25%;
}
.col-sm-offset-8 {
  margin-left: 50%;
}
.col-sm-offset-7 {
  margin-left: 43.75%;
}
.col-sm-offset-6 {
  margin-left: 37.5%;
}
.col-sm-offset-5 {
  margin-left: 31.25%;
}
.col-sm-offset-4 {
  margin-left: 25%;
}
.col-sm-offset-3 {
  margin-left: 18.75%;
}
.col-sm-offset-2 {
  margin-left: 12.5%;
}
.col-sm-offset-1 {
  margin-left: 6.25%;
}
.col-sm-offset-0 {
  margin-left: 0;
}
}
@media (min-width: 480px) {
 .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md-13, .col-md-14, .col-md-15, .col-md-16{
  float: left;
}
.col-md-16 {
  width: 100%;
}
.col-md-15 {
  width: 93.75%;
}
.col-md-14 {
  width: 87.5%;
}
.col-md-13 {
  width: 81.25%;
}
.col-md-12 {
  width: 75%;
}
.col-md-11 {
  width: 68.75%;
}
.col-md-10 {
  width: 62.5%;
}
.col-md-9 {
  width: 56.25%;
}
.col-md-8 {
  width: 50%;
}
.col-md-7 {
  width: 43.75%;
}
.col-md-6 {
  width: 37.5%;
}
.col-md-5 {
  width: 31.25%;
}
.col-md-4 {
  width: 25%;
}
.col-md-3 {
  width: 18.75%;
}
.col-md-2 {
  width: 12.5%;
}
.col-md-1 {
  width: 6.25%;
}

.col-md-pull-16 {
  right: 100%;
}
.col-md-pull-15 {
  right: 93.75%;
}
.col-md-pull-14 {
  right: 87.5%;
}
.col-md-pull-13 {
  right: 81.25%;
}
.col-md-pull-12 {
  right: 75%;
}
.col-md-pull-11 {
  right: 68.75%;
}
.col-md-pull-10 {
  right: 62.5%;
}
.col-md-pull-9 {
  right: 56.25%;
}
.col-md-pull-8 {
  right: 50%;
}
.col-md-pull-7 {
  right: 43.75%;
}
.col-md-pull-6 {
  right: 37.5%;
}
.col-md-pull-5 {
  right: 31.25%;
}
.col-md-pull-4 {
  right: 25%;
}
.col-md-pull-3 {
  right: 18.75%;
}
.col-md-pull-2 {
  right: 12.5%;
}
.col-md-pull-1 {
  right: 6.25%;
}
.col-md-pull-0 {
  right: auto;
}

.col-md-push-16 {
  left: 100%;
}
.col-md-push-15 {
  left: 93.75%;
}
.col-md-push-14 {
  left: 87.5%;
}
.col-md-push-13 {
  left: 81.25%;
}
.col-md-push-12 {
  left: 75%;
}
.col-md-push-11 {
  left: 68.75%;
}
.col-md-push-10 {
  left: 62.5%;
}
.col-md-push-9 {
  left: 56.25%;
}
.col-md-push-8 {
  left: 50%;
}
.col-md-push-7 {
  left: 43.75%;
}
.col-md-push-6 {
  left: 37.5%;
}
.col-md-push-5 {
  left: 31.25%;
}
.col-md-push-4 {
  left: 25%;
}
.col-md-push-3 {
  left: 18.75%;
}
.col-md-push-2 {
  left: 12.5%;
}
.col-md-push-1 {
  left: 6.25%;
}
.col-md-push-0 {
  left: auto;
}

.col-md-offset-16 {
  margin-left: 100%;
}
.col-md-offset-15 {
  margin-left: 93.75%;
}
.col-md-offset-14 {
  margin-left: 87.5%;
}
.col-md-offset-13 {
  margin-left: 81.25%;
}
.col-md-offset-12 {
  margin-left: 75%;
}
.col-md-offset-11 {
  margin-left: 68.75%;
}
.col-md-offset-10 {
  margin-left: 62.5%;
}
.col-md-offset-9 {
  margin-left: 56.25%;
}
.col-md-offset-8 {
  margin-left: 50%;
}
.col-md-offset-7 {
  margin-left: 43.75%;
}
.col-md-offset-6 {
  margin-left: 37.5%;
}
.col-md-offset-5 {
  margin-left: 31.25%;
}
.col-md-offset-4 {
  margin-left: 25%;
}
.col-md-offset-3 {
  margin-left: 18.75%;
}
.col-md-offset-2 {
  margin-left: 12.5%;
}
.col-md-offset-1 {
  margin-left: 6.25%;
}
.col-md-offset-0 {
  margin-left: 0;
}
}
@media (min-width: 768px) {
 .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-13, .col-lg-14, .col-lg-15, .col-lg-16{
  float: left;
}
.col-lg-16 {
  width: 100%;
}
.col-lg-15 {
  width: 93.75%;
}
.col-lg-14 {
  width: 87.5%;
}
.col-lg-13 {
  width: 81.25%;
}
.col-lg-12 {
  width: 75%;
}
.col-lg-11 {
  width: 68.75%;
}
.col-lg-10 {
  width: 62.5%;
}
.col-lg-9 {
  width: 56.25%;
}
.col-lg-8 {
  width: 50%;
}
.col-lg-7 {
  width: 43.75%;
}
.col-lg-6 {
  width: 37.5%;
}
.col-lg-5 {
  width: 31.25%;
}
.col-lg-4 {
  width: 25%;
}
.col-lg-3 {
  width: 18.75%;
}
.col-lg-2 {
  width: 12.5%;
}
.col-lg-1 {
  width: 6.25%;
}

.col-lg-pull-16 {
  right: 100%;
}
.col-lg-pull-15 {
  right: 93.75%;
}
.col-lg-pull-14 {
  right: 87.5%;
}
.col-lg-pull-13 {
  right: 81.25%;
}
.col-lg-pull-12 {
  right: 75%;
}
.col-lg-pull-11 {
  right: 68.75%;
}
.col-lg-pull-10 {
  right: 62.5%;
}
.col-lg-pull-9 {
  right: 56.25%;
}
.col-lg-pull-8 {
  right: 50%;
}
.col-lg-pull-7 {
  right: 43.75%;
}
.col-lg-pull-6 {
  right: 37.5%;
}
.col-lg-pull-5 {
  right: 31.25%;
}
.col-lg-pull-4 {
  right: 25%;
}
.col-lg-pull-3 {
  right: 18.75%;
}
.col-lg-pull-2 {
  right: 12.5%;
}
.col-lg-pull-1 {
  right: 6.25%;
}
.col-lg-pull-0 {
  right: auto;
}

.col-lg-push-16 {
  left: 100%;
}
.col-lg-push-15 {
  left: 93.75%;
}
.col-lg-push-14 {
  left: 87.5%;
}
.col-lg-push-13 {
  left: 81.25%;
}
.col-lg-push-12 {
  left: 75%;
}
.col-lg-push-11 {
  left: 68.75%;
}
.col-lg-push-10 {
  left: 62.5%;
}
.col-lg-push-9 {
  left: 56.25%;
}
.col-lg-push-8 {
  left: 50%;
}
.col-lg-push-7 {
  left: 43.75%;
}
.col-lg-push-6 {
  left: 37.5%;
}
.col-lg-push-5 {
  left: 31.25%;
}
.col-lg-push-4 {
  left: 25%;
}
.col-lg-push-3 {
  left: 18.75%;
}
.col-lg-push-2 {
  left: 12.5%;
}
.col-lg-push-1 {
  left: 6.25%;
}
.col-lg-push-0 {
  left: auto;
}

.col-lg-offset-16 {
  margin-left: 100%;
}
.col-lg-offset-15 {
  margin-left: 93.75%;
}
.col-lg-offset-14 {
  margin-left: 87.5%;
}
.col-lg-offset-13 {
  margin-left: 81.25%;
}
.col-lg-offset-12 {
  margin-left: 75%;
}
.col-lg-offset-11 {
  margin-left: 68.75%;
}
.col-lg-offset-10 {
  margin-left: 62.5%;
}
.col-lg-offset-9 {
  margin-left: 56.25%;
}
.col-lg-offset-8 {
  margin-left: 50%;
}
.col-lg-offset-7 {
  margin-left: 43.75%;
}
.col-lg-offset-6 {
  margin-left: 37.5%;
}
.col-lg-offset-5 {
  margin-left: 31.25%;
}
.col-lg-offset-4 {
  margin-left: 25%;
}
.col-lg-offset-3 {
  margin-left: 18.75%;
}
.col-lg-offset-2 {
  margin-left: 12.5%;
}
.col-lg-offset-1 {
  margin-left: 6.25%;
}
.col-lg-offset-0 {
  margin-left: 0;
}
}
@media (min-width: 992px) {
  .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-13, .col-xl-14, .col-xl-15, .col-xl-16{
  float: left;
}
.col-xl-16 {
  width: 100%;
}
.col-xl-15 {
  width: 93.75%;
}
.col-xl-14 {
  width: 87.5%;
}
.col-xl-13 {
  width: 81.25%;
}
.col-xl-12 {
  width: 75%;
}
.col-xl-11 {
  width: 68.75%;
}
.col-xl-10 {
  width: 62.5%;
}
.col-xl-9 {
  width: 56.25%;
}
.col-xl-8 {
  width: 50%;
}
.col-xl-7 {
  width: 43.75%;
}
.col-xl-6 {
  width: 37.5%;
}
.col-xl-5 {
  width: 31.25%;
}
.col-xl-4 {
  width: 25%;
}
.col-xl-3 {
  width: 18.75%;
}
.col-xl-2 {
  width: 12.5%;
}
.col-xl-1 {
  width: 6.25%;
}

.col-xl-pull-16 {
  right: 100%;
}
.col-xl-pull-15 {
  right: 93.75%;
}
.col-xl-pull-14 {
  right: 87.5%;
}
.col-xl-pull-13 {
  right: 81.25%;
}
.col-xl-pull-12 {
  right: 75%;
}
.col-xl-pull-11 {
  right: 68.75%;
}
.col-xl-pull-10 {
  right: 62.5%;
}
.col-xl-pull-9 {
  right: 56.25%;
}
.col-xl-pull-8 {
  right: 50%;
}
.col-xl-pull-7 {
  right: 43.75%;
}
.col-xl-pull-6 {
  right: 37.5%;
}
.col-xl-pull-5 {
  right: 31.25%;
}
.col-xl-pull-4 {
  right: 25%;
}
.col-xl-pull-3 {
  right: 18.75%;
}
.col-xl-pull-2 {
  right: 12.5%;
}
.col-xl-pull-1 {
  right: 6.25%;
}
.col-xl-pull-0 {
  right: auto;
}

.col-xl-push-16 {
  left: 100%;
}
.col-xl-push-15 {
  left: 93.75%;
}
.col-xl-push-14 {
  left: 87.5%;
}
.col-xl-push-13 {
  left: 81.25%;
}
.col-xl-push-12 {
  left: 75%;
}
.col-xl-push-11 {
  left: 68.75%;
}
.col-xl-push-10 {
  left: 62.5%;
}
.col-xl-push-9 {
  left: 56.25%;
}
.col-xl-push-8 {
  left: 50%;
}
.col-xl-push-7 {
  left: 43.75%;
}
.col-xl-push-6 {
  left: 37.5%;
}
.col-xl-push-5 {
  left: 31.25%;
}
.col-xl-push-4 {
  left: 25%;
}
.col-xl-push-3 {
  left: 18.75%;
}
.col-xl-push-2 {
  left: 12.5%;
}
.col-xl-push-1 {
  left: 6.25%;
}
.col-xl-push-0 {
  left: auto;
}

.col-xl-offset-16 {
  margin-left: 100%;
}
.col-xl-offset-15 {
  margin-left: 93.75%;
}
.col-xl-offset-14 {
  margin-left: 87.5%;
}
.col-xl-offset-13 {
  margin-left: 81.25%;
}
.col-xl-offset-12 {
  margin-left: 75%;
}
.col-xl-offset-11 {
  margin-left: 68.75%;
}
.col-xl-offset-10 {
  margin-left: 62.5%;
}
.col-xl-offset-9 {
  margin-left: 56.25%;
}
.col-xl-offset-8 {
  margin-left: 50%;
}
.col-xl-offset-7 {
  margin-left: 43.75%;
}
.col-xl-offset-6 {
  margin-left: 37.5%;
}
.col-xl-offset-5 {
  margin-left: 31.25%;
}
.col-xl-offset-4 {
  margin-left: 25%;
}
.col-xl-offset-3 {
  margin-left: 18.75%;
}
.col-xl-offset-2 {
  margin-left: 12.5%;
}
.col-xl-offset-1 {
  margin-left: 6.25%;
}
.col-xl-offset-0 {
  margin-left: 0;
}
}
@media (min-width: 1200px) {
  .col-xxl-1, .col-xxl-2, .col-xxl-3, .col-xxl-4, .col-xxl-5, .col-xxl-6, .col-xxl-7, .col-xxl-8, .col-xxl-9, .col-xxl-10, .col-xxl-11, .col-xxl-12, .col-xxl-13, .col-xxl-14, .col-xxl-15, .col-xxl-16{
  float: left;
}
.col-xxl-16 {
  width: 100%;
}
.col-xxl-15 {
  width: 93.75%;
}
.col-xxl-14 {
  width: 87.5%;
}
.col-xxl-13 {
  width: 81.25%;
}
.col-xxl-12 {
  width: 75%;
}
.col-xxl-11 {
  width: 68.75%;
}
.col-xxl-10 {
  width: 62.5%;
}
.col-xxl-9 {
  width: 56.25%;
}
.col-xxl-8 {
  width: 50%;
}
.col-xxl-7 {
  width: 43.75%;
}
.col-xxl-6 {
  width: 37.5%;
}
.col-xxl-5 {
  width: 31.25%;
}
.col-xxl-4 {
  width: 25%;
}
.col-xxl-3 {
  width: 18.75%;
}
.col-xxl-2 {
  width: 12.5%;
}
.col-xxl-1 {
  width: 6.25%;
}

.col-xxl-pull-16 {
  right: 100%;
}
.col-xxl-pull-15 {
  right: 93.75%;
}
.col-xxl-pull-14 {
  right: 87.5%;
}
.col-xxl-pull-13 {
  right: 81.25%;
}
.col-xxl-pull-12 {
  right: 75%;
}
.col-xxl-pull-11 {
  right: 68.75%;
}
.col-xxl-pull-10 {
  right: 62.5%;
}
.col-xxl-pull-9 {
  right: 56.25%;
}
.col-xxl-pull-8 {
  right: 50%;
}
.col-xxl-pull-7 {
  right: 43.75%;
}
.col-xxl-pull-6 {
  right: 37.5%;
}
.col-xxl-pull-5 {
  right: 31.25%;
}
.col-xxl-pull-4 {
  right: 25%;
}
.col-xxl-pull-3 {
  right: 18.75%;
}
.col-xxl-pull-2 {
  right: 12.5%;
}
.col-xxl-pull-1 {
  right: 6.25%;
}
.col-xxl-pull-0 {
  right: auto;
}

.col-xxl-push-16 {
  left: 100%;
}
.col-xxl-push-15 {
  left: 93.75%;
}
.col-xxl-push-14 {
  left: 87.5%;
}
.col-xxl-push-13 {
  left: 81.25%;
}
.col-xxl-push-12 {
  left: 75%;
}
.col-xxl-push-11 {
  left: 68.75%;
}
.col-xxl-push-10 {
  left: 62.5%;
}
.col-xxl-push-9 {
  left: 56.25%;
}
.col-xxl-push-8 {
  left: 50%;
}
.col-xxl-push-7 {
  left: 43.75%;
}
.col-xxl-push-6 {
  left: 37.5%;
}
.col-xxl-push-5 {
  left: 31.25%;
}
.col-xxl-push-4 {
  left: 25%;
}
.col-xxl-push-3 {
  left: 18.75%;
}
.col-xxl-push-2 {
  left: 12.5%;
}
.col-xxl-push-1 {
  left: 6.25%;
}
.col-xxl-push-0 {
  left: auto;
}

.col-xxl-offset-16 {
  margin-left: 100%;
}
.col-xxl-offset-15 {
  margin-left: 93.75%;
}
.col-xxl-offset-14 {
  margin-left: 87.5%;
}
.col-xxl-offset-13 {
  margin-left: 81.25%;
}
.col-xxl-offset-12 {
  margin-left: 75%;
}
.col-xxl-offset-11 {
  margin-left: 68.75%;
}
.col-xxl-offset-10 {
  margin-left: 62.5%;
}
.col-xxl-offset-9 {
  margin-left: 56.25%;
}
.col-xxl-offset-8 {
  margin-left: 50%;
}
.col-xxl-offset-7 {
  margin-left: 43.75%;
}
.col-xxl-offset-6 {
  margin-left: 37.5%;
}
.col-xxl-offset-5 {
  margin-left: 31.25%;
}
.col-xxl-offset-4 {
  margin-left: 25%;
}
.col-xxl-offset-3 {
  margin-left: 18.75%;
}
.col-xxl-offset-2 {
  margin-left: 12.5%;
}
.col-xxl-offset-1 {
  margin-left: 6.25%;
}
.col-xxl-offset-0 {
  margin-left: 0;
}
}
@media screen and (max-width: 767px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 15px;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #ddd;
  }
  .table-responsive > .table {
    margin-bottom: 0;
  }
  .table-responsive > .table > thead > tr > th,
  .table-responsive > .table > tbody > tr > th,
  .table-responsive > .table > tfoot > tr > th,
  .table-responsive > .table > thead > tr > td,
  .table-responsive > .table > tbody > tr > td,
  .table-responsive > .table > tfoot > tr > td {
    white-space: nowrap;
  }
  .table-responsive > .table-bordered {
    border: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:first-child,
  .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .table-responsive > .table-bordered > thead > tr > td:first-child,
  .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:last-child,
  .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .table-responsive > .table-bordered > thead > tr > td:last-child,
  .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
  }
  .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .table-responsive > .table-bordered > tfoot > tr:last-child > th,
  .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0;
  }
}
@media (min-width: 768px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-static {
    display: inline-block;
  }
  .form-inline .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .form-inline .input-group .input-group-addon,
  .form-inline .input-group .input-group-btn,
  .form-inline .input-group .form-control {
    width: auto;
  }
  .form-inline .input-group > .form-control {
    width: 100%;
  }
  .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio,
  .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio label,
  .form-inline .checkbox label {
    padding-left: 0;
  }
  .form-inline .radio input[type="radio"],
  .form-inline .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0;
  }
  .form-inline .has-feedback .form-control-feedback {
    top: 0;
  }
}
@media (min-width: 768px) {
  .form-horizontal .control-label {
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;
  }
}
.form-horizontal .has-feedback .form-control-feedback {
  right: 15px;
}
@media (min-width: 768px) {
  .form-horizontal .form-group-lg .control-label {
    padding-top: 14.333333px;
  }
}
@media (min-width: 768px) {
  .form-horizontal .form-group-sm .control-label {
    padding-top: 6px;
  }
}
@media (min-width: 768px) {
  .navbar-right .dropdown-menu {
    right: 0;
    left: auto;
  }
  .navbar-right .dropdown-menu-left {
    right: auto;
    left: 0;
  }
}
@media (min-width: 768px) {
  .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
  }
}
@media (min-width: 768px) {
  .nav-tabs.nav-justified > li > a {
    border-bottom: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
  }
  .nav-tabs.nav-justified > .active > a,
  .nav-tabs.nav-justified > .active > a:hover,
  .nav-tabs.nav-justified > .active > a:focus {
    border-bottom-color: #fff;
  }
}
@media (min-width: 768px) {
  .nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-justified > li > a {
    margin-bottom: 0;
  }
}
@media (min-width: 768px) {
  .nav-tabs-justified > li > a {
    border-bottom: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
  }
  .nav-tabs-justified > .active > a,
  .nav-tabs-justified > .active > a:hover,
  .nav-tabs-justified > .active > a:focus {
    border-bottom-color: #fff;
  }
}
@media (min-width: 768px) {
  .navbar {
    border-radius: 4px;
  }
}
@media (min-width: 768px) {
  .navbar-header {
    float: left;
  }
}
@media (min-width: 768px) {
  .navbar-collapse {
    width: auto;
    border-top: 0;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
  .navbar-collapse.collapse {
    display: block !important;
    height: auto !important;
    padding-bottom: 0;
    overflow: visible !important;
  }
  .navbar-collapse.in {
    overflow-y: visible;
  }
  .navbar-fixed-top .navbar-collapse,
  .navbar-static-top .navbar-collapse,
  .navbar-fixed-bottom .navbar-collapse {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (max-device-width: 480px) and (orientation: landscape) {
  .navbar-fixed-top .navbar-collapse,
  .navbar-fixed-bottom .navbar-collapse {
    max-height: 200px;
  }
}
@media (min-width: 768px) {
  .container > .navbar-header,
  .container-fluid > .navbar-header,
  .container > .navbar-collapse,
  .container-fluid > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
@media (min-width: 768px) {
  .navbar-static-top {
    border-radius: 0;
  }
}
@media (min-width: 768px) {
  .navbar-fixed-top,
  .navbar-fixed-bottom {
    border-radius: 0;
  }
}
@media (min-width: 768px) {
  .navbar > .container .navbar-brand,
  .navbar > .container-fluid .navbar-brand {
    margin-left: -15px;
  }
}
@media (min-width: 768px) {
  .navbar-toggle {
    display: none;
  }
}
@media (max-width: 767px) {
  .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
  .navbar-nav .open .dropdown-menu > li > a,
  .navbar-nav .open .dropdown-menu .dropdown-header {
    padding: 5px 15px 5px 25px;
  }
  .navbar-nav .open .dropdown-menu > li > a {
    line-height: 20px;
  }
  .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-nav .open .dropdown-menu > li > a:focus {
    background-image: none;
  }
}
@media (min-width: 768px) {
  .navbar-nav {
    float: left;
    margin: 0;
  }
  .navbar-nav > li {
    float: left;
  }
  .navbar-nav > li > a {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
@media (min-width: 768px) {
  .navbar-form .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .navbar-form .form-control-static {
    display: inline-block;
  }
  .navbar-form .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .navbar-form .input-group .input-group-addon,
  .navbar-form .input-group .input-group-btn,
  .navbar-form .input-group .form-control {
    width: auto;
  }
  .navbar-form .input-group > .form-control {
    width: 100%;
  }
  .navbar-form .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .radio,
  .navbar-form .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .radio label,
  .navbar-form .checkbox label {
    padding-left: 0;
  }
  .navbar-form .radio input[type="radio"],
  .navbar-form .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0;
  }
  .navbar-form .has-feedback .form-control-feedback {
    top: 0;
  }
}
@media (max-width: 767px) {
  .navbar-form .form-group {
    margin-bottom: 5px;
  }
  .navbar-form .form-group:last-child {
    margin-bottom: 0;
  }
}
@media (min-width: 768px) {
  .navbar-form {
    width: auto;
    padding-top: 0;
    padding-bottom: 0;
    margin-right: 0;
    margin-left: 0;
    border: 0;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
}
@media (min-width: 768px) {
  .navbar-text {
    float: left;
    margin-right: 15px;
    margin-left: 15px;
  }
}
@media (min-width: 768px) {
  .navbar-left {
    float: left !important;
  }
  .navbar-right {
    float: right !important;
    margin-right: -15px;
  }
  .navbar-right ~ .navbar-right {
    margin-right: 0;
  }
}
@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: #777;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #333;
    background-color: transparent;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #555;
    background-color: #e7e7e7;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #ccc;
    background-color: transparent;
  }
}
@media (max-width: 767px) {
  .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
    border-color: #080808;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
    background-color: #080808;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: #9d9d9d;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #fff;
    background-color: transparent;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #fff;
    background-color: #080808;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #444;
    background-color: transparent;
  }
}
@media screen and (min-width: 768px) {
  .jumbotron {
    padding: 48px 0;
  }
  .container .jumbotron,
  .container-fluid .jumbotron {
    padding-right: 60px;
    padding-left: 60px;
  }
  .jumbotron h1,
  .jumbotron .h1 {
    font-size: 63px;
  }
}
@media (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto;
  }
  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
            box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
  }
  .modal-sm {
    width: 300px;
  }
}

@media (min-width: 992px) {
  .modal-lg {
    width: 900px;
  }
}
@media screen and (min-width: 768px) {
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-prev,
  .carousel-control .icon-next {
    width: 30px;
    height: 30px;
    margin-top: -15px;
    font-size: 30px;
  }
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .icon-prev {
    margin-left: -15px;
  }
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-next {
    margin-right: -15px;
  }
  .carousel-caption {
    right: 20%;
    left: 20%;
    padding-bottom: 30px;
  }
  .carousel-indicators {
    bottom: 20px;
  }
}

@media (max-width: 319px) {
  .visible-xs {
    display: block !important;
  }
  table.visible-xs {
    display: table;
  }
  tr.visible-xs {
    display: table-row !important;
  }
  th.visible-xs,
  td.visible-xs {
    display: table-cell !important;
  }
}
@media (max-width: 319px) {
  .visible-xs-block {
    display: block !important;
  }
}
@media (max-width: 319px) {
  .visible-xs-inline {
    display: inline !important;
  }
}
@media (max-width: 319px) {
  .visible-xs-inline-block {
    display: inline-block !important;
  }
}


@media (min-width:320px) and (max-width: 479px) {
  .visible-sm {
    display: block !important;
  }
  table.visible-sm {
    display: table;
  }
  tr.visible-sm {
    display: table-row !important;
  }
  th.visible-sm,
  td.visible-sm {
    display: table-cell !important;
  }
}
@media (min-width:320px) and (max-width: 479px)  {
  .visible-sm-block {
    display: block !important;
  }
}
@media (min-width:320px) and (max-width: 479px)  {
  .visible-sm-inline {
    display: inline !important;
  }
}
@media (min-width:320px) and (max-width: 479px)  {
  .visible-sm-inline-block {
    display: inline-block !important;
  }
}



@media (min-width:480px) and (max-width: 767px) {
  .visible-md {
    display: block !important;
  }
  table.visible-md {
    display: table;
  }
  tr.visible-md {
    display: table-row !important;
  }
  th.visible-md,
  td.visible-md {
    display: table-cell !important;
  }
}
@media (min-width:480px) and (max-width: 767px) {
  .visible-md-block {
    display: block !important;
  }
}
@media (min-width:480px) and (max-width: 767px) {
  .visible-md-inline {
    display: inline !important;
  }
}
@media (min-width:480px) and (max-width: 767px) {
  .visible-md-inline-block {
    display: inline-block !important;
  }
}


@media (min-width: 768px) and (max-width: 991px) {
  .visible-lg {
    display: block !important;
  }
  table.visible-lg {
    display: table;
  }
  tr.visible-lg {
    display: table-row !important;
  }
  th.visible-lg,
  td.visible-lg {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-lg-block {
    display: block !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-lg-inline {
    display: inline !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-lg-inline-block {
    display: inline-block !important;
  }
}



@media (min-width: 992px) and (max-width: 1199px) {
  .visible-xl {
    display: block !important;
  }
  table.visible-xl {
    display: table;
  }
  tr.visible-xl {
    display: table-row !important;
  }
  th.visible-xl,
  td.visible-xl {
    display: table-cell !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-xl-block {
    display: block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-xl-inline {
    display: inline !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-xl-inline-block {
    display: inline-block !important;
  }
}



@media (min-width: 1200px) {
  .visible-xxl {
    display: block !important;
  }
  table.visible-xxl {
    display: table;
  }
  tr.visible-xxl {
    display: table-row !important;
  }
  th.visible-xxl,
  td.visible-xxl {
    display: table-cell !important;
  }
}
@media (min-width: 1200px) {
  .visible-xxl-block {
    display: block !important;
  }
}
@media (min-width: 1200px) {
  .visible-xxl-inline {
    display: inline !important;
  }
}
@media (min-width: 1200px) {
  .visible-xxl-inline-block {
    display: inline-block !important;
  }
}

@media (max-width: 319px) {
  .hidden-xs {
    display: none !important;
  }
}
@media (min-width: 320px) and (max-width: 479px) {
  .hidden-sm {
    display: none !important;
  }
}
@media (min-width: 480px) and (max-width: 767px) {
  .hidden-md {
    display: none !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .hidden-lg {
    display: none !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-xl {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .hidden-xxl {
    display: none !important;
  }
}

.visible-print {
  display: none !important;
}
@media print {
  .visible-print {
    display: block !important;
  }
  table.visible-print {
    display: table;
  }
  tr.visible-print {
    display: table-row !important;
  }
  th.visible-print,
  td.visible-print {
    display: table-cell !important;
  }
}
.visible-print-block {
  display: none !important;
}
@media print {
  .visible-print-block {
    display: block !important;
  }
}
.visible-print-inline {
  display: none !important;
}
@media print {
  .visible-print-inline {
    display: inline !important;
  }
}
.visible-print-inline-block {
  display: none !important;
}
@media print {
  .visible-print-inline-block {
    display: inline-block !important;
  }
}
@media print {
  .hidden-print {
    display: none !important;
  }
}