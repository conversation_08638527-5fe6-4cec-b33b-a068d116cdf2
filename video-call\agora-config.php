<?php
/**
 * Agora Video Calling Configuration
 * Matrimony Platform - Video Call Integration
 */

class AgoraConfig {
    
    // Agora App Configuration
    private static $APP_ID = 'YOUR_AGORA_APP_ID'; // Replace with your Agora App ID
    private static $APP_CERTIFICATE = 'YOUR_AGORA_APP_CERTIFICATE'; // Replace with your App Certificate
    
    // Token expiration time (24 hours)
    private static $TOKEN_EXPIRATION_TIME = 86400;
    
    // Video call settings
    private static $MAX_CALL_DURATION_FREE = 300; // 5 minutes for free users
    private static $MAX_CALL_DURATION_PAID = 3600; // 60 minutes for paid users
    
    /**
     * Get Agora App ID
     */
    public static function getAppId() {
        return self::$APP_ID;
    }
    
    /**
     * Get App Certificate
     */
    public static function getAppCertificate() {
        return self::$APP_CERTIFICATE;
    }
    
    /**
     * Get token expiration time
     */
    public static function getTokenExpirationTime() {
        return self::$TOKEN_EXPIRATION_TIME;
    }
    
    /**
     * Get max call duration based on user type
     */
    public static function getMaxCallDuration($userType = 'free') {
        return $userType === 'paid' ? self::$MAX_CALL_DURATION_PAID : self::$MAX_CALL_DURATION_FREE;
    }
    
    /**
     * Generate unique channel name
     */
    public static function generateChannelName($callerId, $receiverId) {
        return 'matrimony_' . min($callerId, $receiverId) . '_' . max($callerId, $receiverId) . '_' . time();
    }
    
    /**
     * Validate user permissions for video calling
     */
    public static function canMakeVideoCall($userId, $targetUserId) {
        include_once '../databaseConn.php';
        $DatabaseCo = new DatabaseConn();
        
        // Check if user has video calling permission
        $userQuery = $DatabaseCo->dbLink->query("
            SELECT r.status, p.chat, p.profile 
            FROM register r 
            LEFT JOIN payments p ON r.matri_id = p.pmatri_id 
            WHERE r.matri_id = '$userId'
        ");
        
        if ($userQuery && $userRow = mysqli_fetch_object($userQuery)) {
            // Free users have limited video calling
            if ($userRow->status === 'Active') {
                return ['allowed' => true, 'type' => 'free', 'duration' => self::$MAX_CALL_DURATION_FREE];
            }
            
            // Paid users have full access
            if ($userRow->status === 'Paid' && $userRow->chat === 'Yes') {
                return ['allowed' => true, 'type' => 'paid', 'duration' => self::$MAX_CALL_DURATION_PAID];
            }
        }
        
        return ['allowed' => false, 'reason' => 'Upgrade to premium for video calling'];
    }
    
    /**
     * Check if users are not blocked
     */
    public static function areUsersBlocked($userId, $targetUserId) {
        include_once '../databaseConn.php';
        $DatabaseCo = new DatabaseConn();
        
        $blockQuery = $DatabaseCo->dbLink->query("
            SELECT COUNT(*) as blocked_count 
            FROM block_profile 
            WHERE (block_by = '$userId' AND block_to = '$targetUserId') 
            OR (block_by = '$targetUserId' AND block_to = '$userId')
        ");
        
        if ($blockQuery && $blockRow = mysqli_fetch_object($blockQuery)) {
            return $blockRow->blocked_count > 0;
        }
        
        return false;
    }
    
    /**
     * Log video call attempt
     */
    public static function logCallAttempt($callerId, $receiverId, $channelName, $status = 'initiated') {
        include_once '../databaseConn.php';
        $DatabaseCo = new DatabaseConn();
        
        $DatabaseCo->dbLink->query("
            INSERT INTO video_calls (caller_id, receiver_id, agora_channel, call_status, created_at) 
            VALUES ('$callerId', '$receiverId', '$channelName', '$status', NOW())
        ");
        
        return mysqli_insert_id($DatabaseCo->dbLink);
    }
    
    /**
     * Update call status
     */
    public static function updateCallStatus($callId, $status, $duration = 0) {
        include_once '../databaseConn.php';
        $DatabaseCo = new DatabaseConn();
        
        $endTime = $status === 'ended' ? ', ended_at = NOW()' : '';
        $durationUpdate = $duration > 0 ? ", call_duration = $duration" : '';
        
        $DatabaseCo->dbLink->query("
            UPDATE video_calls 
            SET call_status = '$status' $endTime $durationUpdate 
            WHERE call_id = $callId
        ");
    }
    
    /**
     * Get call history for user
     */
    public static function getCallHistory($userId, $limit = 20) {
        include_once '../databaseConn.php';
        $DatabaseCo = new DatabaseConn();
        
        $query = $DatabaseCo->dbLink->query("
            SELECT vc.*, 
                   CASE 
                       WHEN vc.caller_id = '$userId' THEN r2.username 
                       ELSE r1.username 
                   END as other_user_name,
                   CASE 
                       WHEN vc.caller_id = '$userId' THEN r2.photo1 
                       ELSE r1.photo1 
                   END as other_user_photo,
                   CASE 
                       WHEN vc.caller_id = '$userId' THEN 'outgoing' 
                       ELSE 'incoming' 
                   END as call_direction
            FROM video_calls vc
            LEFT JOIN register r1 ON vc.caller_id = r1.matri_id
            LEFT JOIN register r2 ON vc.receiver_id = r2.matri_id
            WHERE vc.caller_id = '$userId' OR vc.receiver_id = '$userId'
            ORDER BY vc.created_at DESC
            LIMIT $limit
        ");
        
        $history = [];
        if ($query) {
            while ($row = mysqli_fetch_object($query)) {
                $history[] = $row;
            }
        }
        
        return $history;
    }
    
    /**
     * Check daily call limit for free users
     */
    public static function checkDailyCallLimit($userId) {
        include_once '../databaseConn.php';
        $DatabaseCo = new DatabaseConn();
        
        // Check user type
        $userQuery = $DatabaseCo->dbLink->query("
            SELECT status FROM register WHERE matri_id = '$userId'
        ");
        
        if ($userQuery && $userRow = mysqli_fetch_object($userQuery)) {
            if ($userRow->status === 'Paid') {
                return ['allowed' => true, 'remaining' => 'unlimited'];
            }
            
            // For free users, check daily limit (e.g., 3 calls per day)
            $today = date('Y-m-d');
            $callCountQuery = $DatabaseCo->dbLink->query("
                SELECT COUNT(*) as call_count 
                FROM video_calls 
                WHERE caller_id = '$userId' 
                AND DATE(created_at) = '$today'
                AND call_status IN ('accepted', 'ended')
            ");
            
            if ($callCountQuery && $countRow = mysqli_fetch_object($callCountQuery)) {
                $dailyLimit = 3; // Free users get 3 calls per day
                $remaining = $dailyLimit - $countRow->call_count;
                
                return [
                    'allowed' => $remaining > 0,
                    'remaining' => max(0, $remaining),
                    'limit' => $dailyLimit
                ];
            }
        }
        
        return ['allowed' => false, 'remaining' => 0];
    }
}

/**
 * Agora Token Generator
 * Based on Agora's token generation algorithm
 */
class AgoraTokenGenerator {
    
    /**
     * Generate RTC Token for video calling
     */
    public static function generateRtcToken($channelName, $uid, $role = 'publisher') {
        $appId = AgoraConfig::getAppId();
        $appCertificate = AgoraConfig::getAppCertificate();
        $expireTime = time() + AgoraConfig::getTokenExpirationTime();
        
        // This is a simplified token generation
        // In production, use Agora's official token generation library
        $token = self::buildToken($appId, $appCertificate, $channelName, $uid, $expireTime, $role);
        
        return $token;
    }
    
    /**
     * Build token (simplified version)
     * Note: Use Agora's official SDK for production
     */
    private static function buildToken($appId, $appCertificate, $channelName, $uid, $expireTime, $role) {
        // This is a placeholder implementation
        // In production, integrate with Agora's official token generation library
        $signature = hash('sha256', $appId . $appCertificate . $channelName . $uid . $expireTime . $role);
        return base64_encode($appId . ':' . $signature . ':' . $expireTime);
    }
}
?>
