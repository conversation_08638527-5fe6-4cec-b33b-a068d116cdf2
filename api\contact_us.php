<?php
include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();
$site_que = $DatabaseCo->dbLink->query("select * from site_config");
$site_data = mysqli_fetch_object($site_que);
$sel_own_data=$DatabaseCo->dbLink->query("select * from cms_pages where cms_id='9'");
if (mysqli_num_rows($sel_own_data) > 0) {
	while ($contact_res = mysqli_fetch_object($sel_own_data)) {
		$string=$contact_res->cms_content;
		//$str=strip_tags($string);
		$aa= html_entity_decode($string, ENT_COMPAT, 'UTF-8'); 
		//$a=strip_tags($aa);
		$response['responseData'] = array('cms_id' => $contact_res->cms_id,'page_name' => $contact_res->page_name,'cms_title' => $contact_res->cms_title,'cms_content' =>  $aa,'status'=>"1");
}

$name=trim(ucwords($_POST['txt_name']));
        $name=$_POST['txt_name'];
            $to=$_POST['txt_email'];	  
            $mobile=$_POST['phone_no'];
            $subject1=$_POST['subject'];
            $description=$_POST['description'];
            $date = date('Y-m-d H:i:s');
            $SQL_CONTACT=$DatabaseCo->dbLink->query("INSERT INTO `contactus` (`name`, `email`, `mobile`, `subject`, `description`, `date`) VALUES ('".$name."','".$to."','".$mobile."','".$subject1."','".$description."','".$date."')");
      

	$response['status'] = "1";
	$response['message'] = "Successfully";
	echo json_encode($response);
	exit;
}
else
{
	$response['status'] = "0";
	$response['message'] = "No Data Found";
	echo json_encode($response);
	
}

?>