.component {
position: relative;
background: url(../../img/gridme.png) repeat center center;
padding: 4em;
height: 800px;
border: 3px solid #e47203;
max-width: 901px;
overflow: hidden;
margin: 0 auto;
}
.resize-container {
cursor: move;
display: inline-block;
margin-left: 9%;
margin-top: -6%;
position: relative;
max-width:1000px;
max-height:1000px;
}
.resize-container img {
display: block;

}
/*max-width:500px;
max-height:500px;*/
.resize-container:hover img,
.resize-container:active img {
outline: 2px dashed #e47203;
}
.resize-handle-ne,
.resize-handle-se,
.resize-handle-nw,
.resize-handle-sw {
position: absolute;
display: block;
width: 10px;
height: 10px;
background: #e47203;
z-index: 999;
}
.resize-handle-nw {
top: -5px;
left: -5px;
cursor: nw-resize;
}
.resize-handle-sw {
bottom: -5px;
left: -5px;
cursor: sw-resize;
}
.resize-handle-ne {
top: -5px;
right: -5px;
cursor: ne-resize;
}
.resize-handle-se {
bottom: -5px;
right: -5px;
cursor: se-resize;
}
.overlay {
position: absolute;
left: 45%;
top: 38%;
margin-left: -100px;
margin-top: -100px;
z-index: 999;
width: 270px;
height: 360px;
border: solid 2px #e47203;
box-sizing: content-box;
pointer-events: none;
}
/*width: 180px;
height: 240px;*/
.overlay:after,
.overlay:before {
content: '';
position: absolute;
display: block;
width: 184px;
height: 40px;
border-left: dashed 2px rgba(222,60,80,.9);
border-right: dashed 2px rgba(222,60,80,.9);
}
.overlay:before {
top: 0;
margin-left: -2px;
margin-top: -40px;
}
.overlay:after {
bottom: 0;
margin-left: -2px;
margin-bottom: -40px;
}
.overlay-inner:after,
.overlay-inner:before {
content: '';
position: absolute;
display: block;
width: 40px;
height: 244px;
border-top: dashed 2px rgba(222,60,80,.9);
border-bottom: dashed 2px rgba(222,60,80,.9);
}
.overlay-inner:before {
left: 0;
margin-left: -40px;
margin-top: -2px;
}
.overlay-inner:after{
right: 0;
margin-right: -40px;
margin-top: -2px;
}
.btn-crop {
position: absolute;
vertical-align: bottom;
right: 5px;
bottom: 5px;
padding: 6px 10px;
z-index: 999;
background: #e47203;
border: none;
border-radius: 5px;
color: #FFF;
}
.btn-crop img {
vertical-align: middle;
margin-left: 8px;
}
