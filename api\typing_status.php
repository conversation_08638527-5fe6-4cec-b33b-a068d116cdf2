<?php
/**
 * Typing Status API - Enhanced Chat System
 * Handles typing indicators for real-time chat
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
include_once '../databaseConn.php';

$DatabaseCo = new DatabaseConn();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Only POST method allowed');
}

$input = json_decode(file_get_contents('php://input'), true);
$userId = $_SESSION['user_id'] ?? '';
$receiverId = $input['receiver_id'] ?? '';
$isTyping = $input['is_typing'] ?? false;

// Validate inputs
if (empty($userId)) {
    sendResponse(false, 'User not logged in');
}

if (empty($receiverId)) {
    sendResponse(false, 'Receiver ID is required');
}

try {
    // Create typing_status table if it doesn't exist
    $createTableQuery = "
        CREATE TABLE IF NOT EXISTS typing_status (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            receiver_id VARCHAR(50) NOT NULL,
            is_typing BOOLEAN DEFAULT FALSE,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_typing (user_id, receiver_id),
            INDEX idx_receiver_typing (receiver_id, is_typing),
            INDEX idx_last_updated (last_updated)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ";
    
    $DatabaseCo->dbLink->query($createTableQuery);
    
    if ($isTyping) {
        // Set typing status
        $updateQuery = $DatabaseCo->dbLink->query("
            INSERT INTO typing_status (user_id, receiver_id, is_typing, last_updated) 
            VALUES ('$userId', '$receiverId', TRUE, NOW())
            ON DUPLICATE KEY UPDATE 
            is_typing = TRUE, 
            last_updated = NOW()
        ");
    } else {
        // Clear typing status
        $updateQuery = $DatabaseCo->dbLink->query("
            UPDATE typing_status 
            SET is_typing = FALSE, last_updated = NOW() 
            WHERE user_id = '$userId' AND receiver_id = '$receiverId'
        ");
    }
    
    if (!$updateQuery) {
        sendResponse(false, 'Failed to update typing status: ' . mysqli_error($DatabaseCo->dbLink));
    }
    
    // Clean up old typing statuses (older than 10 seconds)
    $cleanupQuery = $DatabaseCo->dbLink->query("
        UPDATE typing_status 
        SET is_typing = FALSE 
        WHERE last_updated < DATE_SUB(NOW(), INTERVAL 10 SECOND) 
        AND is_typing = TRUE
    ");
    
    sendResponse(true, 'Typing status updated successfully', [
        'user_id' => $userId,
        'receiver_id' => $receiverId,
        'is_typing' => $isTyping,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    error_log("Typing status error: " . $e->getMessage());
    sendResponse(false, 'Failed to update typing status');
}

/**
 * Send JSON response
 */
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => time()
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
    exit();
}
?>
