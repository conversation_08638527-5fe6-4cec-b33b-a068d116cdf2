<?php
/**
 * Get New Messages API - Enhanced Chat System
 * Retrieves new messages for real-time chat
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
include_once '../databaseConn.php';

$DatabaseCo = new DatabaseConn();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Only POST method allowed');
}

$input = json_decode(file_get_contents('php://input'), true);
$userId = $_SESSION['user_id'] ?? '';
$chatUserId = $input['chat_user_id'] ?? '';
$lastMessageTime = $input['last_message_time'] ?? 0;

// Validate inputs
if (empty($userId)) {
    sendResponse(false, 'User not logged in');
}

if (empty($chatUserId)) {
    sendResponse(false, 'Chat user ID is required');
}

try {
    // Convert timestamp to MySQL datetime
    $lastMessageDate = date('Y-m-d H:i:s', $lastMessageTime / 1000);
    
    // Get new messages since last check
    $messagesQuery = $DatabaseCo->dbLink->query("
        SELECT m.*, 
               r.username as sender_name, 
               r.photo1 as sender_photo
        FROM messages m
        LEFT JOIN register r ON m.from_id = r.matri_id
        WHERE ((m.from_id = '$userId' AND m.to_id = '$chatUserId') 
               OR (m.from_id = '$chatUserId' AND m.to_id = '$userId'))
        AND m.date > '$lastMessageDate'
        ORDER BY m.date ASC
    ");
    
    $messages = [];
    
    if ($messagesQuery && mysqli_num_rows($messagesQuery) > 0) {
        while ($message = mysqli_fetch_object($messagesQuery)) {
            // Determine message status
            $status = 'delivered';
            if ($message->seen === 'Yes') {
                $status = 'read';
            } elseif ($message->delivered === 'Yes') {
                $status = 'delivered';
            } else {
                $status = 'sent';
            }
            
            $messages[] = [
                'message_id' => $message->id,
                'sender_id' => $message->from_id,
                'receiver_id' => $message->to_id,
                'message' => $message->message,
                'message_type' => $message->message_type ?? 'text',
                'timestamp' => $message->date,
                'status' => $status,
                'sender_name' => $message->sender_name,
                'sender_photo' => $message->sender_photo ?: 'default-avatar.png'
            ];
        }
        
        // Mark received messages as delivered
        if (!empty($messages)) {
            $receivedMessageIds = [];
            foreach ($messages as $msg) {
                if ($msg['receiver_id'] === $userId) {
                    $receivedMessageIds[] = $msg['message_id'];
                }
            }
            
            if (!empty($receivedMessageIds)) {
                $messageIds = implode(',', $receivedMessageIds);
                $DatabaseCo->dbLink->query("
                    UPDATE messages 
                    SET delivered = 'Yes' 
                    WHERE id IN ($messageIds) AND delivered = 'No'
                ");
            }
        }
    }
    
    // Get unread message count
    $unreadQuery = $DatabaseCo->dbLink->query("
        SELECT COUNT(*) as unread_count 
        FROM messages 
        WHERE to_id = '$userId' 
        AND from_id = '$chatUserId' 
        AND seen = 'No'
    ");
    
    $unreadCount = 0;
    if ($unreadQuery && ($unreadData = mysqli_fetch_object($unreadQuery))) {
        $unreadCount = $unreadData->unread_count;
    }
    
    sendResponse(true, 'Messages retrieved successfully', [
        'messages' => $messages,
        'unread_count' => $unreadCount,
        'last_check' => time() * 1000 // Return current timestamp in milliseconds
    ]);
    
} catch (Exception $e) {
    error_log("Get new messages error: " . $e->getMessage());
    sendResponse(false, 'Failed to retrieve messages');
}

/**
 * Send JSON response
 */
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => time()
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
    exit();
}
?>
