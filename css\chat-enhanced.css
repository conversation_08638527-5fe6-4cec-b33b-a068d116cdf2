/**
 * Enhanced Chat System CSS
 * Modern messaging interface with real-time features
 */

/* Chat Container */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 600px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chat-header {
    background: linear-gradient(135deg, #549a11, #4a8a0f);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chat-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
}

.chat-user-details h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chat-user-status {
    font-size: 12px;
    opacity: 0.9;
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.chat-action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Messages Area */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    scroll-behavior: smooth;
}

.chat-message {
    margin-bottom: 15px;
    display: flex;
    animation: fadeInUp 0.3s ease;
}

.message-sent {
    justify-content: flex-end;
}

.message-received {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    background: white;
    border-radius: 18px;
    padding: 12px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
}

.message-sent .message-content {
    background: linear-gradient(135deg, #549a11, #4a8a0f);
    color: white;
    border-bottom-right-radius: 6px;
}

.message-received .message-content {
    background: white;
    border-bottom-left-radius: 6px;
    border: 1px solid #e0e0e0;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

.message-text a {
    color: inherit;
    text-decoration: underline;
}

.message-sent .message-text a {
    color: rgba(255, 255, 255, 0.9);
}

.message-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 6px;
    font-size: 11px;
    opacity: 0.7;
}

.message-time {
    font-size: 11px;
}

.message-status {
    margin-left: 8px;
}

/* Message Types */
.message-image {
    margin-bottom: 8px;
}

.chat-image {
    max-width: 200px;
    max-height: 200px;
    border-radius: 12px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.chat-image:hover {
    transform: scale(1.05);
}

.message-file {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    margin-bottom: 8px;
}

.message-file i {
    font-size: 20px;
    color: #549a11;
}

.message-file a {
    text-decoration: none;
    color: inherit;
    font-weight: 500;
}

.file-size {
    font-size: 11px;
    opacity: 0.7;
}

/* Chat Controls */
.chat-controls {
    background: white;
    border-top: 1px solid #e0e0e0;
    padding: 15px 20px;
}

.chat-input-container {
    position: relative;
}

.chat-input-container .input-group {
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chat-input-container .form-control {
    border: none;
    padding: 12px 16px;
    font-size: 14px;
    resize: none;
}

.chat-input-container .form-control:focus {
    box-shadow: none;
    border-color: transparent;
}

.chat-input-container .btn {
    border: none;
    padding: 12px 16px;
}

.emoji-btn, .file-btn {
    background: #f8f9fa;
    color: #6c757d;
}

.emoji-btn:hover, .file-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.send-btn {
    background: #549a11;
    color: white;
}

.send-btn:hover {
    background: #4a8a0f;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 0;
    font-size: 13px;
    color: #6c757d;
    animation: fadeIn 0.3s ease;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #6c757d;
    border-radius: 50%;
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

/* Emoji Picker */
.emoji-picker {
    position: absolute;
    bottom: 100%;
    left: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 15px;
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
}

.emoji-item {
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 6px;
    text-align: center;
    transition: background 0.2s ease;
}

.emoji-item:hover {
    background: #f0f0f0;
}

/* Upload Progress */
.upload-progress {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    animation: fadeInUp 0.3s ease;
}

.progress-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #6c757d;
}

.progress {
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
}

.progress-bar {
    background: linear-gradient(90deg, #549a11, #4a8a0f);
}

/* Image Modal */
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.image-modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.image-modal-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
}

.image-modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 30px;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-container {
        height: 500px;
        border-radius: 0;
    }
    
    .chat-header {
        padding: 12px 15px;
    }
    
    .chat-user-avatar {
        width: 35px;
        height: 35px;
    }
    
    .chat-user-details h5 {
        font-size: 14px;
    }
    
    .chat-messages {
        padding: 15px;
    }
    
    .message-content {
        max-width: 85%;
        padding: 10px 14px;
    }
    
    .chat-controls {
        padding: 12px 15px;
    }
    
    .emoji-grid {
        grid-template-columns: repeat(6, 1fr);
    }
    
    .emoji-item {
        font-size: 18px;
        padding: 4px;
    }
    
    .chat-image {
        max-width: 150px;
        max-height: 150px;
    }
}

@media (max-width: 480px) {
    .chat-container {
        height: 400px;
    }
    
    .message-content {
        max-width: 90%;
        padding: 8px 12px;
    }
    
    .message-text {
        font-size: 13px;
    }
    
    .emoji-grid {
        grid-template-columns: repeat(5, 1fr);
    }
    
    .chat-image {
        max-width: 120px;
        max-height: 120px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .chat-container {
        background: #1a1a1a;
        border-color: #333;
    }
    
    .chat-messages {
        background: #2d2d2d;
    }
    
    .message-received .message-content {
        background: #3a3a3a;
        color: white;
        border-color: #555;
    }
    
    .chat-controls {
        background: #1a1a1a;
        border-color: #333;
    }
    
    .emoji-picker {
        background: #2d2d2d;
        border-color: #555;
    }
    
    .emoji-item:hover {
        background: #3a3a3a;
    }
    
    .upload-progress {
        background: #2d2d2d;
        border-color: #555;
    }
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.emoji-picker::-webkit-scrollbar {
    width: 4px;
}

.emoji-picker::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.emoji-picker::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

/* Status Indicators */
.user-online {
    position: relative;
}

.user-online::after {
    content: '';
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #28a745;
    border: 2px solid white;
    border-radius: 50%;
}

.user-away::after {
    background: #ffc107;
}

.user-offline::after {
    background: #6c757d;
}

/* Message Reactions (Future Enhancement) */
.message-reactions {
    display: flex;
    gap: 4px;
    margin-top: 6px;
    flex-wrap: wrap;
}

.reaction-item {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 12px;
    padding: 2px 6px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 2px;
}

.reaction-count {
    font-size: 10px;
    opacity: 0.7;
}
