-- Video Calling System Database Schema
-- Matrimony Platform - Video Call Integration

-- Create video_calls table
CREATE TABLE IF NOT EXISTS `video_calls` (
    `call_id` INT(11) NOT NULL AUTO_INCREMENT,
    `caller_id` VARCHAR(50) NOT NULL,
    `receiver_id` VARCHAR(50) NOT NULL,
    `call_status` ENUM('initiated', 'ringing', 'accepted', 'rejected', 'ended', 'failed', 'timeout') DEFAULT 'initiated',
    `call_duration` INT(11) DEFAULT 0 COMMENT 'Duration in seconds',
    `agora_channel` VARCHAR(100) NOT NULL,
    `call_type` ENUM('video', 'audio') DEFAULT 'video',
    `call_quality` ENUM('excellent', 'good', 'fair', 'poor') DEFAULT NULL,
    `end_reason` VARCHAR(100) DEFAULT NULL COMMENT 'Reason for call ending',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `started_at` TIMESTAMP NULL DEFAULT NULL,
    `ended_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`call_id`),
    INDEX `idx_caller_id` (`caller_id`),
    INDEX `idx_receiver_id` (`receiver_id`),
    INDEX `idx_call_status` (`call_status`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`caller_id`) REFERENCES `register`(`matri_id`) ON DELETE CASCADE,
    FOREIGN KEY (`receiver_id`) REFERENCES `register`(`matri_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create call_participants table for group calls (future enhancement)
CREATE TABLE IF NOT EXISTS `call_participants` (
    `participant_id` INT(11) NOT NULL AUTO_INCREMENT,
    `call_id` INT(11) NOT NULL,
    `user_id` VARCHAR(50) NOT NULL,
    `joined_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `left_at` TIMESTAMP NULL DEFAULT NULL,
    `participant_role` ENUM('host', 'participant') DEFAULT 'participant',
    `audio_enabled` BOOLEAN DEFAULT TRUE,
    `video_enabled` BOOLEAN DEFAULT TRUE,
    PRIMARY KEY (`participant_id`),
    INDEX `idx_call_id` (`call_id`),
    INDEX `idx_user_id` (`user_id`),
    FOREIGN KEY (`call_id`) REFERENCES `video_calls`(`call_id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `register`(`matri_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create call_feedback table
CREATE TABLE IF NOT EXISTS `call_feedback` (
    `feedback_id` INT(11) NOT NULL AUTO_INCREMENT,
    `call_id` INT(11) NOT NULL,
    `user_id` VARCHAR(50) NOT NULL,
    `rating` TINYINT(1) CHECK (`rating` >= 1 AND `rating` <= 5),
    `feedback_text` TEXT DEFAULT NULL,
    `audio_quality` TINYINT(1) CHECK (`audio_quality` >= 1 AND `audio_quality` <= 5),
    `video_quality` TINYINT(1) CHECK (`video_quality` >= 1 AND `video_quality` <= 5),
    `connection_quality` TINYINT(1) CHECK (`connection_quality` >= 1 AND `connection_quality` <= 5),
    `would_recommend` BOOLEAN DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`feedback_id`),
    INDEX `idx_call_id` (`call_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_rating` (`rating`),
    FOREIGN KEY (`call_id`) REFERENCES `video_calls`(`call_id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `register`(`matri_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create call_settings table for user preferences
CREATE TABLE IF NOT EXISTS `call_settings` (
    `setting_id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` VARCHAR(50) NOT NULL UNIQUE,
    `auto_accept_calls` BOOLEAN DEFAULT FALSE,
    `call_notifications` BOOLEAN DEFAULT TRUE,
    `video_quality_preference` ENUM('auto', 'high', 'medium', 'low') DEFAULT 'auto',
    `audio_notifications` BOOLEAN DEFAULT TRUE,
    `do_not_disturb` BOOLEAN DEFAULT FALSE,
    `dnd_start_time` TIME DEFAULT NULL,
    `dnd_end_time` TIME DEFAULT NULL,
    `allow_calls_from` ENUM('everyone', 'premium_only', 'contacts_only') DEFAULT 'everyone',
    `max_call_duration` INT(11) DEFAULT NULL COMMENT 'Custom max duration in seconds',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`setting_id`),
    INDEX `idx_user_id` (`user_id`),
    FOREIGN KEY (`user_id`) REFERENCES `register`(`matri_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create call_statistics table for analytics
CREATE TABLE IF NOT EXISTS `call_statistics` (
    `stat_id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` VARCHAR(50) NOT NULL,
    `date` DATE NOT NULL,
    `total_calls_made` INT(11) DEFAULT 0,
    `total_calls_received` INT(11) DEFAULT 0,
    `total_calls_answered` INT(11) DEFAULT 0,
    `total_calls_missed` INT(11) DEFAULT 0,
    `total_call_duration` INT(11) DEFAULT 0 COMMENT 'Total duration in seconds',
    `average_call_duration` DECIMAL(10,2) DEFAULT 0,
    `longest_call_duration` INT(11) DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`stat_id`),
    UNIQUE KEY `unique_user_date` (`user_id`, `date`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_date` (`date`),
    FOREIGN KEY (`user_id`) REFERENCES `register`(`matri_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create call_recordings table (optional feature)
CREATE TABLE IF NOT EXISTS `call_recordings` (
    `recording_id` INT(11) NOT NULL AUTO_INCREMENT,
    `call_id` INT(11) NOT NULL,
    `recording_url` VARCHAR(500) NOT NULL,
    `recording_duration` INT(11) DEFAULT 0,
    `file_size` BIGINT DEFAULT 0 COMMENT 'File size in bytes',
    `recording_type` ENUM('audio', 'video', 'screen') DEFAULT 'video',
    `storage_provider` VARCHAR(50) DEFAULT 'local',
    `is_encrypted` BOOLEAN DEFAULT FALSE,
    `access_permissions` JSON DEFAULT NULL,
    `expires_at` TIMESTAMP NULL DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`recording_id`),
    INDEX `idx_call_id` (`call_id`),
    INDEX `idx_expires_at` (`expires_at`),
    FOREIGN KEY (`call_id`) REFERENCES `video_calls`(`call_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create call_tokens table for security
CREATE TABLE IF NOT EXISTS `call_tokens` (
    `token_id` INT(11) NOT NULL AUTO_INCREMENT,
    `call_id` INT(11) NOT NULL,
    `user_id` VARCHAR(50) NOT NULL,
    `agora_token` TEXT NOT NULL,
    `token_type` ENUM('rtc', 'rtm') DEFAULT 'rtc',
    `expires_at` TIMESTAMP NOT NULL,
    `is_used` BOOLEAN DEFAULT FALSE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`token_id`),
    INDEX `idx_call_id` (`call_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_expires_at` (`expires_at`),
    FOREIGN KEY (`call_id`) REFERENCES `video_calls`(`call_id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `register`(`matri_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Update existing notification table to support video call notifications
ALTER TABLE `notification` 
ADD COLUMN IF NOT EXISTS `call_id` INT(11) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `action_data` JSON DEFAULT NULL,
ADD INDEX `idx_call_id` (`call_id`);

-- Update membership_plan table to include video calling features
ALTER TABLE `membership_plan` 
ADD COLUMN IF NOT EXISTS `video_calls` ENUM('Yes', 'No') DEFAULT 'No',
ADD COLUMN IF NOT EXISTS `max_call_duration` INT(11) DEFAULT 300 COMMENT 'Max call duration in seconds',
ADD COLUMN IF NOT EXISTS `daily_call_limit` INT(11) DEFAULT 3 COMMENT 'Daily call limit for this plan';

-- Update payments table to track video call usage
ALTER TABLE `payments` 
ADD COLUMN IF NOT EXISTS `video_calls_used` INT(11) DEFAULT 0,
ADD COLUMN IF NOT EXISTS `video_call_minutes_used` INT(11) DEFAULT 0;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_video_calls_status_date` ON `video_calls` (`call_status`, `created_at`);
CREATE INDEX IF NOT EXISTS `idx_video_calls_duration` ON `video_calls` (`call_duration`);
CREATE INDEX IF NOT EXISTS `idx_call_feedback_rating` ON `call_feedback` (`rating`, `created_at`);

-- Create views for easier querying
CREATE OR REPLACE VIEW `call_history_view` AS
SELECT 
    vc.call_id,
    vc.caller_id,
    vc.receiver_id,
    vc.call_status,
    vc.call_duration,
    vc.call_type,
    vc.created_at,
    vc.ended_at,
    r1.username as caller_name,
    r1.photo1 as caller_photo,
    r2.username as receiver_name,
    r2.photo1 as receiver_photo,
    CASE 
        WHEN vc.call_duration > 0 THEN CONCAT(FLOOR(vc.call_duration / 60), 'm ', vc.call_duration % 60, 's')
        ELSE '0s'
    END as formatted_duration
FROM video_calls vc
LEFT JOIN register r1 ON vc.caller_id = r1.matri_id
LEFT JOIN register r2 ON vc.receiver_id = r2.matri_id;

-- Create view for user call statistics
CREATE OR REPLACE VIEW `user_call_stats_view` AS
SELECT 
    r.matri_id,
    r.username,
    COUNT(CASE WHEN vc.caller_id = r.matri_id THEN 1 END) as calls_made,
    COUNT(CASE WHEN vc.receiver_id = r.matri_id THEN 1 END) as calls_received,
    COUNT(CASE WHEN vc.receiver_id = r.matri_id AND vc.call_status = 'accepted' THEN 1 END) as calls_answered,
    COUNT(CASE WHEN vc.receiver_id = r.matri_id AND vc.call_status IN ('rejected', 'timeout') THEN 1 END) as calls_missed,
    SUM(CASE WHEN (vc.caller_id = r.matri_id OR vc.receiver_id = r.matri_id) AND vc.call_status = 'ended' THEN vc.call_duration ELSE 0 END) as total_call_time,
    AVG(CASE WHEN (vc.caller_id = r.matri_id OR vc.receiver_id = r.matri_id) AND vc.call_status = 'ended' AND vc.call_duration > 0 THEN vc.call_duration END) as avg_call_duration
FROM register r
LEFT JOIN video_calls vc ON (vc.caller_id = r.matri_id OR vc.receiver_id = r.matri_id)
GROUP BY r.matri_id, r.username;

-- Insert default call settings for existing users
INSERT IGNORE INTO call_settings (user_id, call_notifications, video_quality_preference, allow_calls_from)
SELECT matri_id, TRUE, 'auto', 'everyone' 
FROM register 
WHERE status IN ('Active', 'Paid');

-- Update existing membership plans to include video calling
UPDATE membership_plan 
SET video_calls = 'Yes', max_call_duration = 1800, daily_call_limit = 10 
WHERE plan_type = 'PAID';

UPDATE membership_plan 
SET video_calls = 'Yes', max_call_duration = 300, daily_call_limit = 3 
WHERE plan_type = 'FREE';

-- Create triggers for automatic statistics updates
DELIMITER //

CREATE TRIGGER IF NOT EXISTS `update_call_stats_after_call` 
AFTER UPDATE ON `video_calls`
FOR EACH ROW
BEGIN
    IF NEW.call_status = 'ended' AND OLD.call_status != 'ended' THEN
        -- Update daily statistics for caller
        INSERT INTO call_statistics (user_id, date, total_calls_made, total_call_duration)
        VALUES (NEW.caller_id, DATE(NEW.created_at), 1, NEW.call_duration)
        ON DUPLICATE KEY UPDATE 
            total_calls_made = total_calls_made + 1,
            total_call_duration = total_call_duration + NEW.call_duration,
            average_call_duration = total_call_duration / total_calls_made,
            longest_call_duration = GREATEST(longest_call_duration, NEW.call_duration);
            
        -- Update daily statistics for receiver
        INSERT INTO call_statistics (user_id, date, total_calls_received, total_calls_answered)
        VALUES (NEW.receiver_id, DATE(NEW.created_at), 1, 1)
        ON DUPLICATE KEY UPDATE 
            total_calls_received = total_calls_received + 1,
            total_calls_answered = total_calls_answered + 1;
    END IF;
    
    IF NEW.call_status IN ('rejected', 'timeout') AND OLD.call_status != NEW.call_status THEN
        -- Update missed calls for receiver
        INSERT INTO call_statistics (user_id, date, total_calls_received, total_calls_missed)
        VALUES (NEW.receiver_id, DATE(NEW.created_at), 1, 1)
        ON DUPLICATE KEY UPDATE 
            total_calls_received = total_calls_received + 1,
            total_calls_missed = total_calls_missed + 1;
    END IF;
END//

DELIMITER ;

-- Create procedure for cleaning up old call data
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS `cleanup_old_call_data`()
BEGIN
    -- Delete call recordings older than 30 days
    DELETE FROM call_recordings 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Delete expired tokens
    DELETE FROM call_tokens 
    WHERE expires_at < NOW();
    
    -- Archive old call data (older than 1 year)
    -- This can be customized based on requirements
    UPDATE video_calls 
    SET call_status = 'archived' 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR) 
    AND call_status NOT IN ('archived');
END//

DELIMITER ;

-- Create event scheduler for automatic cleanup (optional)
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS `daily_call_cleanup`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO CALL cleanup_old_call_data();
