<?php
include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();
$getStarQry = $DatabaseCo->dbLink->query("SELECT * FROM `star` WHERE status='APPROVED'");
if(mysqli_num_rows($getStarQry) > 0){
	$count=0;
	while($contact_res = mysqli_fetch_object($getStarQry)){
		$count++;
		$response = array('id' => $contact_res->star_id,'star' => $contact_res->star,'status'=>"1");
		$data["response"][] = $response; 
		
	}
	echo json_encode($data);
	exit;
}else{
	$data['status'] = "0";
	echo json_encode($data);
	exit;
}
?>
