<?php
include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();
$site_que = $DatabaseCo->dbLink->query("select * from site_config");
$site_data = mysqli_fetch_object($site_que);
$matri_id = $_POST['matri_id'];
$count=""; 
if ($matri_id == ""){
	$matri_id = "Please Enter Matri id";
	$count++;
}else{
	$matri_id = "";
}
if ($count == 0){
	$matri_id = $_POST['matri_id'];
	$_GET_VIEW_PROFILE = $DatabaseCo->dbLink->query("SELECT index_id,username,status,tokan,matri_id FROM register_view  WHERE matri_id='$matri_id'");
	if (mysqli_num_rows($_GET_VIEW_PROFILE) > 0){
		$count = 0;
		while ($contact_res = mysqli_fetch_object($_GET_VIEW_PROFILE)){
			$user_status = $contact_res->status;
            if($user_status == "Active" || $user_status == "Paid"){
                $user_status="Active";
            }else{
                $user_status="Inactive";
            }
			
			$count++;
			$response['responseData'][$count] = array(
				'user_id' => $contact_res->index_id,
				'matri_id' => $contact_res->matri_id,
				'username' => $contact_res->username,
                'user_status' => $user_status,
				'status' => "1"
			);
		}
		$response['status'] = "1";
		$response['message'] = "Success";
		echo json_encode($response);
		exit;
	}else{
		$response['status'] = "0";
		$response['message'] = "No Data Found";
		echo json_encode($response);
	}
}else{
	$response['status'] = "0";
	$response['message'] = "Please Enter Matri Id";
	echo json_encode($response);
}
?>