<?php
/**
 * Check Incoming Calls API
 * Checks for pending incoming video calls for a user
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Only POST method allowed');
}

$input = json_decode(file_get_contents('php://input'), true);
$userId = $input['user_id'] ?? '';

if (empty($userId)) {
    sendResponse(false, 'User ID is required');
}

try {
    // Check for incoming calls in the last 2 minutes that are still pending
    $incomingCallsQuery = $DatabaseCo->dbLink->query("
        SELECT vc.call_id, vc.caller_id, vc.receiver_id, vc.agora_channel, vc.call_status, vc.created_at,
               r.username as caller_name, r.photo1 as caller_photo, r.mobile as caller_mobile
        FROM video_calls vc
        LEFT JOIN register r ON vc.caller_id = r.matri_id
        WHERE vc.receiver_id = '$userId' 
        AND vc.call_status IN ('initiated', 'ringing')
        AND vc.created_at > DATE_SUB(NOW(), INTERVAL 2 MINUTE)
        ORDER BY vc.created_at DESC
    ");
    
    $incomingCalls = [];
    
    if ($incomingCallsQuery && mysqli_num_rows($incomingCallsQuery) > 0) {
        while ($call = mysqli_fetch_object($incomingCallsQuery)) {
            // Check if call is still valid (not timed out)
            $callAge = time() - strtotime($call->created_at);
            
            if ($callAge > 120) { // 2 minutes timeout
                // Mark call as timed out
                $DatabaseCo->dbLink->query("
                    UPDATE video_calls 
                    SET call_status = 'timeout', ended_at = NOW() 
                    WHERE call_id = {$call->call_id}
                ");
                continue;
            }
            
            // Update call status to ringing if it's still initiated
            if ($call->call_status === 'initiated') {
                $DatabaseCo->dbLink->query("
                    UPDATE video_calls 
                    SET call_status = 'ringing' 
                    WHERE call_id = {$call->call_id}
                ");
                $call->call_status = 'ringing';
            }
            
            $incomingCalls[] = [
                'call_id' => $call->call_id,
                'caller_id' => $call->caller_id,
                'caller_name' => $call->caller_name,
                'caller_photo' => $call->caller_photo ?: 'default-avatar.png',
                'caller_mobile' => $call->caller_mobile,
                'channel' => $call->agora_channel,
                'status' => $call->call_status,
                'created_at' => $call->created_at,
                'time_remaining' => max(0, 120 - $callAge) // Time remaining before timeout
            ];
        }
    }
    
    // Also check for any missed calls in the last 5 minutes that haven't been seen
    $missedCallsQuery = $DatabaseCo->dbLink->query("
        SELECT vc.call_id, vc.caller_id, vc.call_status, vc.created_at,
               r.username as caller_name, r.photo1 as caller_photo
        FROM video_calls vc
        LEFT JOIN register r ON vc.caller_id = r.matri_id
        WHERE vc.receiver_id = '$userId' 
        AND vc.call_status IN ('timeout', 'rejected')
        AND vc.created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        AND vc.call_id NOT IN (
            SELECT call_id FROM notification 
            WHERE receiver_id = '$userId' 
            AND notification_type = 'missed_call' 
            AND call_id = vc.call_id
        )
        ORDER BY vc.created_at DESC
        LIMIT 5
    ");
    
    $missedCalls = [];
    
    if ($missedCallsQuery && mysqli_num_rows($missedCallsQuery) > 0) {
        while ($call = mysqli_fetch_object($missedCallsQuery)) {
            $missedCalls[] = [
                'call_id' => $call->call_id,
                'caller_id' => $call->caller_id,
                'caller_name' => $call->caller_name,
                'caller_photo' => $call->caller_photo ?: 'default-avatar.png',
                'status' => $call->call_status,
                'created_at' => $call->created_at
            ];
            
            // Create notification for missed call
            $DatabaseCo->dbLink->query("
                INSERT INTO notification (sender_id, receiver_id, notification, notification_type, call_id, seen, date) 
                VALUES ('{$call->caller_id}', '$userId', 'Missed video call from {$call->caller_name}', 'missed_call', {$call->call_id}, 'No', NOW())
            ");
        }
    }
    
    // Check user's call settings for auto-accept
    $settingsQuery = $DatabaseCo->dbLink->query("
        SELECT auto_accept_calls, call_notifications, do_not_disturb 
        FROM call_settings 
        WHERE user_id = '$userId'
    ");
    
    $userSettings = [
        'auto_accept_calls' => false,
        'call_notifications' => true,
        'do_not_disturb' => false
    ];
    
    if ($settingsQuery && ($settings = mysqli_fetch_object($settingsQuery))) {
        $userSettings = [
            'auto_accept_calls' => $settings->auto_accept_calls == 1,
            'call_notifications' => $settings->call_notifications == 1,
            'do_not_disturb' => $settings->do_not_disturb == 1
        ];
    }
    
    // If user has DND enabled, reject all incoming calls
    if ($userSettings['do_not_disturb'] && !empty($incomingCalls)) {
        foreach ($incomingCalls as $call) {
            $DatabaseCo->dbLink->query("
                UPDATE video_calls 
                SET call_status = 'rejected', ended_at = NOW() 
                WHERE call_id = {$call['call_id']}
            ");
        }
        $incomingCalls = [];
    }
    
    sendResponse(true, 'Incoming calls checked', [
        'incoming_calls' => $incomingCalls,
        'missed_calls' => $missedCalls,
        'user_settings' => $userSettings,
        'total_incoming' => count($incomingCalls),
        'total_missed' => count($missedCalls)
    ]);
    
} catch (Exception $e) {
    error_log("Check incoming calls error: " . $e->getMessage());
    sendResponse(false, 'Unable to check incoming calls');
}

/**
 * Send JSON response
 */
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => time()
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
    exit();
}
?>
