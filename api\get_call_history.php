<?php
/**
 * Get Call History API
 * Retrieves video call history for a user
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Only POST method allowed');
}

$input = json_decode(file_get_contents('php://input'), true);
$userId = $input['user_id'] ?? '';
$limit = $input['limit'] ?? 20;
$offset = $input['offset'] ?? 0;
$filter = $input['filter'] ?? 'all'; // all, incoming, outgoing, missed

if (empty($userId)) {
    sendResponse(false, 'User ID is required');
}

// Validate limit and offset
$limit = max(1, min(100, intval($limit)));
$offset = max(0, intval($offset));

try {
    // Build filter conditions
    $filterCondition = '';
    switch ($filter) {
        case 'incoming':
            $filterCondition = "AND vc.receiver_id = '$userId'";
            break;
        case 'outgoing':
            $filterCondition = "AND vc.caller_id = '$userId'";
            break;
        case 'missed':
            $filterCondition = "AND vc.receiver_id = '$userId' AND vc.call_status IN ('timeout', 'rejected')";
            break;
        case 'answered':
            $filterCondition = "AND vc.call_status IN ('accepted', 'ended')";
            break;
        default:
            $filterCondition = '';
    }
    
    // Get call history
    $historyQuery = $DatabaseCo->dbLink->query("
        SELECT vc.call_id, vc.caller_id, vc.receiver_id, vc.call_status, vc.call_duration, 
               vc.call_type, vc.created_at, vc.started_at, vc.ended_at,
               CASE 
                   WHEN vc.caller_id = '$userId' THEN r2.username 
                   ELSE r1.username 
               END as other_user_name,
               CASE 
                   WHEN vc.caller_id = '$userId' THEN r2.photo1 
                   ELSE r1.photo1 
               END as other_user_photo,
               CASE 
                   WHEN vc.caller_id = '$userId' THEN r2.matri_id 
                   ELSE r1.matri_id 
               END as other_user_id,
               CASE 
                   WHEN vc.caller_id = '$userId' THEN 'outgoing' 
                   ELSE 'incoming' 
               END as call_direction,
               cf.rating as call_rating,
               cf.feedback_text as call_feedback
        FROM video_calls vc
        LEFT JOIN register r1 ON vc.caller_id = r1.matri_id
        LEFT JOIN register r2 ON vc.receiver_id = r2.matri_id
        LEFT JOIN call_feedback cf ON vc.call_id = cf.call_id AND cf.user_id = '$userId'
        WHERE (vc.caller_id = '$userId' OR vc.receiver_id = '$userId')
        $filterCondition
        ORDER BY vc.created_at DESC
        LIMIT $limit OFFSET $offset
    ");
    
    $callHistory = [];
    
    if ($historyQuery && mysqli_num_rows($historyQuery) > 0) {
        while ($call = mysqli_fetch_object($historyQuery)) {
            // Format duration
            $formattedDuration = '0s';
            if ($call->call_duration > 0) {
                $minutes = floor($call->call_duration / 60);
                $seconds = $call->call_duration % 60;
                if ($minutes > 0) {
                    $formattedDuration = $minutes . 'm ' . $seconds . 's';
                } else {
                    $formattedDuration = $seconds . 's';
                }
            }
            
            // Determine call status display
            $statusDisplay = ucfirst($call->call_status);
            $statusIcon = 'phone';
            $statusColor = 'secondary';
            
            switch ($call->call_status) {
                case 'ended':
                    $statusDisplay = 'Completed';
                    $statusIcon = 'phone';
                    $statusColor = 'success';
                    break;
                case 'rejected':
                    $statusDisplay = $call->call_direction === 'outgoing' ? 'Declined' : 'Rejected';
                    $statusIcon = 'phone-slash';
                    $statusColor = 'danger';
                    break;
                case 'timeout':
                    $statusDisplay = $call->call_direction === 'outgoing' ? 'No Answer' : 'Missed';
                    $statusIcon = 'phone-slash';
                    $statusColor = 'warning';
                    break;
                case 'accepted':
                    $statusDisplay = 'In Progress';
                    $statusIcon = 'phone';
                    $statusColor = 'primary';
                    break;
                case 'initiated':
                case 'ringing':
                    $statusDisplay = 'Calling...';
                    $statusIcon = 'phone';
                    $statusColor = 'info';
                    break;
            }
            
            $callHistory[] = [
                'call_id' => $call->call_id,
                'other_user' => [
                    'id' => $call->other_user_id,
                    'name' => $call->other_user_name,
                    'photo' => $call->other_user_photo ?: 'default-avatar.png'
                ],
                'direction' => $call->call_direction,
                'status' => $call->call_status,
                'status_display' => $statusDisplay,
                'status_icon' => $statusIcon,
                'status_color' => $statusColor,
                'duration' => $call->call_duration,
                'formatted_duration' => $formattedDuration,
                'call_type' => $call->call_type,
                'created_at' => $call->created_at,
                'started_at' => $call->started_at,
                'ended_at' => $call->ended_at,
                'time_ago' => timeAgo($call->created_at),
                'rating' => $call->call_rating,
                'feedback' => $call->call_feedback,
                'can_callback' => canCallback($call->call_status, $call->created_at)
            ];
        }
    }
    
    // Get total count for pagination
    $countQuery = $DatabaseCo->dbLink->query("
        SELECT COUNT(*) as total_calls
        FROM video_calls vc
        WHERE (vc.caller_id = '$userId' OR vc.receiver_id = '$userId')
        $filterCondition
    ");
    
    $totalCalls = 0;
    if ($countQuery && ($countData = mysqli_fetch_object($countQuery))) {
        $totalCalls = $countData->total_calls;
    }
    
    // Get call statistics
    $statsQuery = $DatabaseCo->dbLink->query("
        SELECT 
            COUNT(*) as total_calls,
            COUNT(CASE WHEN caller_id = '$userId' THEN 1 END) as outgoing_calls,
            COUNT(CASE WHEN receiver_id = '$userId' THEN 1 END) as incoming_calls,
            COUNT(CASE WHEN call_status = 'ended' THEN 1 END) as completed_calls,
            COUNT(CASE WHEN receiver_id = '$userId' AND call_status IN ('timeout', 'rejected') THEN 1 END) as missed_calls,
            SUM(CASE WHEN call_status = 'ended' THEN call_duration ELSE 0 END) as total_talk_time,
            AVG(CASE WHEN call_status = 'ended' AND call_duration > 0 THEN call_duration END) as avg_call_duration
        FROM video_calls 
        WHERE caller_id = '$userId' OR receiver_id = '$userId'
    ");
    
    $statistics = [
        'total_calls' => 0,
        'outgoing_calls' => 0,
        'incoming_calls' => 0,
        'completed_calls' => 0,
        'missed_calls' => 0,
        'total_talk_time' => 0,
        'avg_call_duration' => 0,
        'formatted_talk_time' => '0m'
    ];
    
    if ($statsQuery && ($stats = mysqli_fetch_object($statsQuery))) {
        $totalTalkMinutes = floor($stats->total_talk_time / 60);
        $totalTalkHours = floor($totalTalkMinutes / 60);
        $remainingMinutes = $totalTalkMinutes % 60;
        
        $formattedTalkTime = '';
        if ($totalTalkHours > 0) {
            $formattedTalkTime = $totalTalkHours . 'h ' . $remainingMinutes . 'm';
        } else {
            $formattedTalkTime = $totalTalkMinutes . 'm';
        }
        
        $statistics = [
            'total_calls' => intval($stats->total_calls),
            'outgoing_calls' => intval($stats->outgoing_calls),
            'incoming_calls' => intval($stats->incoming_calls),
            'completed_calls' => intval($stats->completed_calls),
            'missed_calls' => intval($stats->missed_calls),
            'total_talk_time' => intval($stats->total_talk_time),
            'avg_call_duration' => round($stats->avg_call_duration, 1),
            'formatted_talk_time' => $formattedTalkTime
        ];
    }
    
    sendResponse(true, 'Call history retrieved successfully', [
        'calls' => $callHistory,
        'pagination' => [
            'total' => $totalCalls,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $totalCalls
        ],
        'statistics' => $statistics,
        'filter' => $filter
    ]);
    
} catch (Exception $e) {
    error_log("Get call history error: " . $e->getMessage());
    sendResponse(false, 'Unable to retrieve call history');
}

/**
 * Calculate time ago
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'Just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    
    return floor($time/31536000) . ' years ago';
}

/**
 * Check if user can callback
 */
function canCallback($status, $createdAt) {
    // Can callback if call was missed, rejected, or ended more than 5 minutes ago
    if (in_array($status, ['timeout', 'rejected', 'ended'])) {
        $timeDiff = time() - strtotime($createdAt);
        return $timeDiff > 300; // 5 minutes
    }
    return false;
}

/**
 * Send JSON response
 */
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => time()
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
    exit();
}
?>
