/**
 * Enhanced Validation and Error Handling System
 * Pellipus Project Improvements
 */

(function() {
    'use strict';
    
    // Enhanced Validation System
    var EnhancedValidation = {
        
        // Configuration
        config: {
            showNotifications: true,
            realTimeValidation: true,
            animateErrors: true,
            autoHideNotifications: true,
            notificationDuration: 4000
        },
        
        // Initialize enhanced validation
        init: function() {
            console.log('🚀 Enhanced Validation System: Initializing...');
            
            this.createNotificationContainer();
            this.setupRealTimeValidation();
            this.enhanceFormValidation();
            this.setupErrorHandling();
            
            console.log('✅ Enhanced Validation System: Ready');
        },
        
        // Create notification container
        createNotificationContainer: function() {
            if (!document.getElementById('notification-container')) {
                var container = document.createElement('div');
                container.id = 'notification-container';
                container.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    max-width: 400px;
                    pointer-events: none;
                `;
                document.body.appendChild(container);
            }
        },
        
        // Show modern notification
        showNotification: function(message, type = 'error', duration = null) {
            if (!this.config.showNotifications) return;
            
            duration = duration || this.config.notificationDuration;
            
            var notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            
            var iconMap = {
                'error': 'fa-exclamation-circle',
                'success': 'fa-check-circle',
                'warning': 'fa-exclamation-triangle',
                'info': 'fa-info-circle'
            };
            
            var colorMap = {
                'error': '#ef4444',
                'success': '#22c55e',
                'warning': '#f59e0b',
                'info': '#3b82f6'
            };
            
            notification.innerHTML = `
                <div style="
                    background: white;
                    border-left: 4px solid ${colorMap[type]};
                    border-radius: 8px;
                    padding: 16px 20px;
                    margin-bottom: 10px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    display: flex;
                    align-items: center;
                    pointer-events: auto;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                    max-width: 380px;
                ">
                    <i class="fa ${iconMap[type]}" style="
                        color: ${colorMap[type]};
                        font-size: 18px;
                        margin-right: 12px;
                        flex-shrink: 0;
                    "></i>
                    <div style="
                        flex: 1;
                        color: #374151;
                        font-size: 14px;
                        line-height: 1.4;
                        font-weight: 500;
                    ">${message}</div>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: none;
                        border: none;
                        color: #9ca3af;
                        cursor: pointer;
                        font-size: 16px;
                        margin-left: 10px;
                        padding: 0;
                        width: 20px;
                        height: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">×</button>
                </div>
            `;
            
            var container = document.getElementById('notification-container');
            container.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.firstElementChild.style.transform = 'translateX(0)';
            }, 10);
            
            // Auto-hide
            if (this.config.autoHideNotifications) {
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.firstElementChild.style.transform = 'translateX(100%)';
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.remove();
                            }
                        }, 300);
                    }
                }, duration);
            }
        },
        
        // Enhanced error display for form fields
        showError: function(field, message) {
            this.clearError(field);
            
            var fieldElement = document.getElementById(field) || document.querySelector(`[name="${field}"]`);
            if (!fieldElement) return;
            
            // Add error class to field
            fieldElement.classList.add('error-field');
            fieldElement.style.borderColor = '#ef4444';
            fieldElement.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
            
            // Create error element
            var errorElement = document.createElement('div');
            errorElement.id = field + '_error';
            errorElement.className = 'field-error';
            errorElement.innerHTML = `
                <div style="
                    color: #ef4444;
                    font-size: 12px;
                    margin-top: 5px;
                    display: flex;
                    align-items: center;
                    animation: slideDown 0.3s ease;
                ">
                    <i class="fa fa-exclamation-circle" style="margin-right: 6px; font-size: 11px;"></i>
                    ${message}
                </div>
            `;
            
            // Insert error after field's parent
            var insertAfter = fieldElement.closest('.input-group') || fieldElement.closest('.form-group') || fieldElement;
            insertAfter.parentNode.insertBefore(errorElement, insertAfter.nextSibling);
            
            // Show notification
            this.showNotification(message, 'error');
        },
        
        // Clear field error
        clearError: function(field) {
            var fieldElement = document.getElementById(field) || document.querySelector(`[name="${field}"]`);
            if (fieldElement) {
                fieldElement.classList.remove('error-field');
                fieldElement.style.borderColor = '';
                fieldElement.style.boxShadow = '';
            }
            
            var errorElement = document.getElementById(field + '_error');
            if (errorElement) {
                errorElement.remove();
            }
        },
        
        // Setup real-time validation
        setupRealTimeValidation: function() {
            if (!this.config.realTimeValidation) return;
            
            var self = this;
            
            // Add event listeners to form fields
            document.addEventListener('input', function(e) {
                if (e.target.matches('input, select, textarea')) {
                    self.validateField(e.target);
                }
            });
            
            document.addEventListener('blur', function(e) {
                if (e.target.matches('input, select, textarea')) {
                    self.validateField(e.target);
                }
            });
        },
        
        // Validate individual field
        validateField: function(field) {
            var name = field.name;
            var value = field.value.trim();
            var isValid = true;
            var message = '';
            
            // Clear previous error
            this.clearError(name);
            
            // Required field validation
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                message = this.getFieldLabel(field) + ' is required';
            }
            
            // Email validation
            else if (field.type === 'email' && value) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    message = 'Please enter a valid email address';
                }
            }
            
            // Mobile number validation
            else if (name === 'mobile' && value) {
                var mobileRegex = /^[0-9]{10}$/;
                if (!mobileRegex.test(value)) {
                    isValid = false;
                    message = 'Mobile number must be 10 digits';
                }
            }
            
            // Name length validation
            else if ((name === 'nickname' || name === 'lastname') && value.length > 30) {
                isValid = false;
                message = 'Name is too long (maximum 30 characters)';
            }
            
            // Show error if validation failed
            if (!isValid) {
                this.showError(name, message);
            }
            
            return isValid;
        },
        
        // Get field label for error messages
        getFieldLabel: function(field) {
            var label = field.closest('.form-group')?.querySelector('label');
            if (label) {
                return label.textContent.replace('*', '').trim();
            }
            
            var placeholder = field.placeholder;
            if (placeholder) {
                return placeholder.replace('Enter ', '').replace('Select ', '');
            }
            
            return field.name.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
        },
        
        // Enhanced form validation
        enhanceFormValidation: function() {
            var self = this;
            
            // Override existing validateForm function
            window.validateFormEnhanced = function() {
                var form = document.getElementById('frm');
                if (!form) return true;
                
                var isValid = true;
                var firstErrorField = null;
                
                // Validate all required fields
                var requiredFields = form.querySelectorAll('[required]');
                requiredFields.forEach(function(field) {
                    if (!self.validateField(field)) {
                        isValid = false;
                        if (!firstErrorField) {
                            firstErrorField = field;
                        }
                    }
                });
                
                // Focus first error field
                if (!isValid && firstErrorField) {
                    firstErrorField.focus();
                    firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                
                return isValid;
            };
        },
        
        // Setup error handling
        setupErrorHandling: function() {
            var self = this;
            
            // Handle form submission
            document.addEventListener('submit', function(e) {
                if (e.target.id === 'frm') {
                    if (!self.validateForm()) {
                        e.preventDefault();
                        self.showNotification('Please fix the errors above before submitting', 'error');
                    }
                }
            });
        },
        
        // Validate entire form
        validateForm: function() {
            var form = document.getElementById('frm');
            if (!form) return true;
            
            var isValid = true;
            var fields = form.querySelectorAll('input, select, textarea');
            
            fields.forEach(field => {
                if (!this.validateField(field)) {
                    isValid = false;
                }
            });
            
            return isValid;
        }
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            EnhancedValidation.init();
        });
    } else {
        EnhancedValidation.init();
    }
    
    // Make available globally
    window.EnhancedValidation = EnhancedValidation;
    
    // Add CSS animations
    var style = document.createElement('style');
    style.textContent = `
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .error-field {
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    `;
    document.head.appendChild(style);
    
    console.log('🎨 Enhanced Validation System Loaded');
    
})();
