<?php
/**
 * Agora Token Server
 * Generates tokens for video calling sessions
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

include_once '../databaseConn.php';
include_once 'agora-config.php';

$DatabaseCo = new DatabaseConn();

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * Generate token for video call
 */
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $action = $input['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'generate_token':
            generateToken($input);
            break;
            
        case 'initiate_call':
            initiateCall($input);
            break;
            
        case 'accept_call':
            acceptCall($input);
            break;
            
        case 'reject_call':
            rejectCall($input);
            break;
            
        case 'end_call':
            endCall($input);
            break;
            
        case 'get_call_status':
            getCallStatus($input);
            break;
            
        default:
            sendResponse(false, 'Invalid action');
    }
}

/**
 * Generate Agora token
 */
function generateToken($data) {
    $channelName = $data['channel'] ?? '';
    $uid = $data['uid'] ?? 0;
    $role = $data['role'] ?? 'publisher';
    
    if (empty($channelName)) {
        sendResponse(false, 'Channel name is required');
        return;
    }
    
    try {
        $token = AgoraTokenGenerator::generateRtcToken($channelName, $uid, $role);
        
        sendResponse(true, 'Token generated successfully', [
            'token' => $token,
            'appId' => AgoraConfig::getAppId(),
            'channel' => $channelName,
            'uid' => $uid,
            'expireTime' => time() + AgoraConfig::getTokenExpirationTime()
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Failed to generate token: ' . $e->getMessage());
    }
}

/**
 * Initiate video call
 */
function initiateCall($data) {
    global $DatabaseCo;
    
    $callerId = $data['caller_id'] ?? '';
    $receiverId = $data['receiver_id'] ?? '';
    
    if (empty($callerId) || empty($receiverId)) {
        sendResponse(false, 'Caller ID and Receiver ID are required');
        return;
    }
    
    // Check if users are blocked
    if (AgoraConfig::areUsersBlocked($callerId, $receiverId)) {
        sendResponse(false, 'Cannot call this user');
        return;
    }
    
    // Check caller permissions
    $permissions = AgoraConfig::canMakeVideoCall($callerId, $receiverId);
    if (!$permissions['allowed']) {
        sendResponse(false, $permissions['reason']);
        return;
    }
    
    // Check daily call limit for free users
    $dailyLimit = AgoraConfig::checkDailyCallLimit($callerId);
    if (!$dailyLimit['allowed']) {
        sendResponse(false, 'Daily call limit reached. Upgrade to premium for unlimited calls.');
        return;
    }
    
    // Generate channel name
    $channelName = AgoraConfig::generateChannelName($callerId, $receiverId);
    
    // Log call attempt
    $callId = AgoraConfig::logCallAttempt($callerId, $receiverId, $channelName);
    
    // Generate tokens for both users
    $callerToken = AgoraTokenGenerator::generateRtcToken($channelName, $callerId);
    $receiverToken = AgoraTokenGenerator::generateRtcToken($channelName, $receiverId);
    
    // Get receiver details
    $receiverQuery = $DatabaseCo->dbLink->query("
        SELECT username, photo1, mobile 
        FROM register 
        WHERE matri_id = '$receiverId'
    ");
    
    $receiverData = null;
    if ($receiverQuery && $receiverRow = mysqli_fetch_object($receiverQuery)) {
        $receiverData = [
            'name' => $receiverRow->username,
            'photo' => $receiverRow->photo1 ?: 'default-avatar.png',
            'mobile' => $receiverRow->mobile
        ];
    }
    
    // Send notification to receiver (implement push notification here)
    sendCallNotification($receiverId, $callerId, $callId, $channelName);
    
    sendResponse(true, 'Call initiated successfully', [
        'call_id' => $callId,
        'channel' => $channelName,
        'caller_token' => $callerToken,
        'receiver_token' => $receiverToken,
        'app_id' => AgoraConfig::getAppId(),
        'max_duration' => $permissions['duration'],
        'receiver' => $receiverData,
        'daily_limit' => $dailyLimit
    ]);
}

/**
 * Accept video call
 */
function acceptCall($data) {
    $callId = $data['call_id'] ?? 0;
    $receiverId = $data['receiver_id'] ?? '';
    
    if (empty($callId) || empty($receiverId)) {
        sendResponse(false, 'Call ID and Receiver ID are required');
        return;
    }
    
    // Update call status
    AgoraConfig::updateCallStatus($callId, 'accepted');
    
    // Send notification to caller
    notifyCallAccepted($callId, $receiverId);
    
    sendResponse(true, 'Call accepted successfully');
}

/**
 * Reject video call
 */
function rejectCall($data) {
    $callId = $data['call_id'] ?? 0;
    $reason = $data['reason'] ?? 'rejected';
    
    if (empty($callId)) {
        sendResponse(false, 'Call ID is required');
        return;
    }
    
    // Update call status
    AgoraConfig::updateCallStatus($callId, 'rejected');
    
    // Notify caller
    notifyCallRejected($callId, $reason);
    
    sendResponse(true, 'Call rejected successfully');
}

/**
 * End video call
 */
function endCall($data) {
    $callId = $data['call_id'] ?? 0;
    $duration = $data['duration'] ?? 0;
    
    if (empty($callId)) {
        sendResponse(false, 'Call ID is required');
        return;
    }
    
    // Update call status with duration
    AgoraConfig::updateCallStatus($callId, 'ended', $duration);
    
    sendResponse(true, 'Call ended successfully', [
        'duration' => $duration
    ]);
}

/**
 * Get call status
 */
function getCallStatus($data) {
    global $DatabaseCo;
    
    $callId = $data['call_id'] ?? 0;
    
    if (empty($callId)) {
        sendResponse(false, 'Call ID is required');
        return;
    }
    
    $query = $DatabaseCo->dbLink->query("
        SELECT * FROM video_calls WHERE call_id = $callId
    ");
    
    if ($query && $row = mysqli_fetch_object($query)) {
        sendResponse(true, 'Call status retrieved', [
            'call_id' => $row->call_id,
            'status' => $row->call_status,
            'duration' => $row->call_duration,
            'created_at' => $row->created_at,
            'ended_at' => $row->ended_at
        ]);
    } else {
        sendResponse(false, 'Call not found');
    }
}

/**
 * Send call notification to receiver
 */
function sendCallNotification($receiverId, $callerId, $callId, $channelName) {
    global $DatabaseCo;
    
    // Get caller details
    $callerQuery = $DatabaseCo->dbLink->query("
        SELECT username, photo1 FROM register WHERE matri_id = '$callerId'
    ");
    
    if ($callerQuery && $callerRow = mysqli_fetch_object($callerQuery)) {
        // Insert notification
        $DatabaseCo->dbLink->query("
            INSERT INTO notification (sender_id, receiver_id, notification, notification_type, seen, date) 
            VALUES ('$callerId', '$receiverId', 'Incoming video call from {$callerRow->username}', 'video_call', 'No', NOW())
        ");
        
        // Here you would implement push notification
        // For now, we'll just log it
        error_log("Video call notification sent to $receiverId from $callerId");
    }
}

/**
 * Notify caller that call was accepted
 */
function notifyCallAccepted($callId, $receiverId) {
    // Implement real-time notification (WebSocket, Server-Sent Events, etc.)
    error_log("Call $callId accepted by $receiverId");
}

/**
 * Notify caller that call was rejected
 */
function notifyCallRejected($callId, $reason) {
    // Implement real-time notification
    error_log("Call $callId rejected: $reason");
}

/**
 * Send JSON response
 */
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => time()
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
    exit();
}
?>
