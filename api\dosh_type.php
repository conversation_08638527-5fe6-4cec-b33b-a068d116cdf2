<?php
include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();
$getDoshQry = $DatabaseCo->dbLink->query("SELECT * FROM `dosh` WHERE status = 'APPROVED'");
if(mysqli_num_rows($getDoshQry) > 0){
	$count=0;
	while($contact_res = mysqli_fetch_object($getDoshQry)){
		$count++;
		$response = array('id' => $contact_res->dosh_id,'dosh_type' => $contact_res->dosh,'status'=>"1");
		$data["response"][] = $response; 
	}
	echo json_encode($data);
	exit;
}else{
	$data['status'] = "0";
	echo json_encode($data);
	exit;
}
?>
