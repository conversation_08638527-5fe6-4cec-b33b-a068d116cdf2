

/* HeaderWrap */

#headerwrap {

	background-color: #52B1E7;

	margin-top:68px;

	background-attachment: relative;

	background-position: center center;

	min-height: 430px;

	width: 100%;

	

    -webkit-background-size: 100%;

    -moz-background-size: 100%;

    -o-background-size: 100%;

    background-size: 100%;



    -webkit-background-size: cover;

    -moz-background-size: cover;

    -o-background-size: cover;

    background-size: cover;

	background-image:url(../img/headerBg.jpg);

	background-attachment:fixed;

}



.logoHeader{

	font-family: '<PERSON>', cursive;

	font-size: 145px;

	line-height: 31px;

	margin: 105px   0 7px;

	color: #FFF;

	display: block;

	position: relative;

	z-index: 1;

	text-shadow: 8px 8px 0px rgba(0,0,0,0.1);

	text-align: center;

	width: 430px;

}



#headerwrap h2 {

	margin-top: 82px;

	margin-bottom: 28px;

	opacity: 0.8;

	color: rgb(255, 255, 255);

	font-size: 33px;

	text-shadow: 2px 2px 0px rgba(0,0,0,0.2);

	text-align: center;

	width: 430px;

	font-weight: 300;

	letter-spacing: 1px;

}





.logoImg{

	width: 130px;

	margin: 0 145px 7px;

}



.downloadButton{

	color: #FFFFFF;

	margin: 10px 10px;

	padding: 20px 50px;

	width: 408px;

	display: none;

	text-align: center;

	font-weight: 300;

	background: transparent;

	border-radius: 3px;

	font-size: 38px;

}



.downloadButton sup{

	font-family: 'Lato', sans-serif;

	font-size:15px;

}



.cropHeaderWrapper{

	overflow:hidden;

	height: 420px;

}



#cropContainerHeaderButton{

	color: #FFF;

	margin: 10px 0 30px 71px;

	padding: 15px 50px;

	width: 407px;

	display: block;

	text-align: center;

	font-weight: 400;

	background: #2CE987;

	text-transform: uppercase;

	border-radius: 2px;

	box-shadow: 8px 8px 0px rgba(0,0,0,0.1);

	font-size: 20px;

	

}



#cropContainerHeaderButton:hover {

	background: #1CD139;

}



.optionsDiv{

	

	border-radius:2px;

	position:absolute;

}



.optionsDiv a{

	width:50%;

	margin-right:20%;

	float:left;

	margin-bottom:10px;

	cursor:pointer;

}



#optionsFloating.fixed{

	position:fixed;

	top:100px;

}



.downloadLink{

	font-size: 22px;

	color: #666;

	text-align: center;

	display: block;

	width: 320px;

	width: 320px;

	padding: 10px;

	border: 1px solid #CCC;

	height: 62px;

	margin: 0 auto;

}



.downloadLink:hover {

	background:#FFF;

}



.downloadIcon{

	width:75px;

	height:75px;

	display:block;

	background-size:75px 150px;

	background-position:0px 0px;

	background-image: url(../img/downloadIcon.png);

}



.downloadLink:hover .downloadIcon{

	background-position:0px -75px;

}



.downloadIcon.small{

	width:25px;

	height:25px;

	display:block;

	background-size:25px 50px;

	background-position:0px 0px;

	background-image: url(../img/downloadIcon.png);

}



.downloadLink:hover .downloadIcon.small{

	background-position:0px -25px;

}



.downloadLink .downloadIcon {

	float:left;

}



.downloadLink span{

	line-height: 40px;

	float:left;

}



#cropContainerModal{ width:100%; height:200px; position: relative; border:1px solid #ccc;}

#cropContaineroutput{ width:100%; height:145px; position: relative; border:1px solid #ccc;}

#cropContainerEyecandy{ width:100%; height:200px; position: relative; border:1px solid #ccc;}







#web_by {position:absolute; right:10px; font-size:10px; color:#aaa; padding:13px; cursor:pointer;  }

#web_by  label{  display:none; position:absolute; right:5px; top:-32px; z-index:10000; font-size:10px; color:#ffa200; padding:10px 9px; cursor:pointer; width:60px; height:40px; background-image:url('../img/jobotic_bubble.png'); background-repeat:no-repeat; }

#jobotic {position:fixed; right:-100px; bottom:-3px; font-size:10px; color:#aaa; padding:13px; cursor:pointer; z-index:9999;}

	

	

	#kontakt { float:left; width:100%; height:auto; z-index:450; position:relative; }

	#kontakt_bg { position:absolute; top:0; left:0; width:100%; height:auto; z-index:450;}

	#copyright {float:left; width:100%; height:45px; background-color:#FFF; margin-top:6px;}

	#copyright  p{float:left; width:100%; font-size:12px; color:#999; text-align:center; background-color:#FFF; margin-top:10px;}

