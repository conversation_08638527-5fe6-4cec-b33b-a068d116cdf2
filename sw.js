/**
 * Service Worker for Vasavi Matrimony PWA
 * Handles caching, offline functionality, and push notifications
 */

const CACHE_NAME = 'vasavi-matrimony-v1.0.0';
const OFFLINE_URL = 'pwa/offline-fallback.html';

// Files to cache for offline functionality
const STATIC_CACHE_URLS = [
    '/',
    '/index.php',
    '/login.php',
    '/search.php',
    '/view-profile.php',
    '/call-history.php',
    '/css/bootstrap.css',
    '/css/custom.css',
    '/css/custom-responsive.css',
    '/css/video-call.css',
    '/js/jquery.min.js',
    '/js/bootstrap.js',
    '/js/green.js',
    '/js/video-call.js',
    '/img/male.jpg',
    '/img/female.jpg',
    '/img/default-avatar.png',
    OFFLINE_URL
];

// API endpoints to cache
const API_CACHE_URLS = [
    '/api/check_call_permissions.php',
    '/api/get_call_history.php',
    '/api/check_incoming_calls.php'
];

// Install event - cache static resources
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('Service Worker: Skip waiting');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Cache failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Claiming clients');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Handle navigation requests
    if (request.mode === 'navigate') {
        event.respondWith(
            fetch(request)
                .then(response => {
                    // If online, cache the response and return it
                    if (response.status === 200) {
                        const responseClone = response.clone();
                        caches.open(CACHE_NAME)
                            .then(cache => cache.put(request, responseClone));
                    }
                    return response;
                })
                .catch(() => {
                    // If offline, try to serve from cache
                    return caches.match(request)
                        .then(cachedResponse => {
                            if (cachedResponse) {
                                return cachedResponse;
                            }
                            // If no cached version, serve offline page
                            return caches.match(OFFLINE_URL);
                        });
                })
        );
        return;
    }
    
    // Handle API requests with network-first strategy
    if (url.pathname.startsWith('/api/')) {
        event.respondWith(
            fetch(request)
                .then(response => {
                    // Cache successful API responses
                    if (response.status === 200) {
                        const responseClone = response.clone();
                        caches.open(CACHE_NAME)
                            .then(cache => cache.put(request, responseClone));
                    }
                    return response;
                })
                .catch(() => {
                    // If offline, try to serve from cache
                    return caches.match(request)
                        .then(cachedResponse => {
                            if (cachedResponse) {
                                return cachedResponse;
                            }
                            // Return offline response for API calls
                            return new Response(
                                JSON.stringify({
                                    success: false,
                                    message: 'You are offline. Please check your internet connection.',
                                    offline: true
                                }),
                                {
                                    status: 503,
                                    headers: { 'Content-Type': 'application/json' }
                                }
                            );
                        });
                })
        );
        return;
    }
    
    // Handle static assets with cache-first strategy
    if (request.destination === 'style' || 
        request.destination === 'script' || 
        request.destination === 'image' ||
        url.pathname.includes('/css/') ||
        url.pathname.includes('/js/') ||
        url.pathname.includes('/img/')) {
        
        event.respondWith(
            caches.match(request)
                .then(cachedResponse => {
                    if (cachedResponse) {
                        return cachedResponse;
                    }
                    
                    return fetch(request)
                        .then(response => {
                            // Cache the fetched resource
                            if (response.status === 200) {
                                const responseClone = response.clone();
                                caches.open(CACHE_NAME)
                                    .then(cache => cache.put(request, responseClone));
                            }
                            return response;
                        })
                        .catch(() => {
                            // Return placeholder for failed image requests
                            if (request.destination === 'image') {
                                return caches.match('/img/default-avatar.png');
                            }
                            throw new Error('Resource not available offline');
                        });
                })
        );
        return;
    }
    
    // For all other requests, try network first, then cache
    event.respondWith(
        fetch(request)
            .then(response => {
                if (response.status === 200) {
                    const responseClone = response.clone();
                    caches.open(CACHE_NAME)
                        .then(cache => cache.put(request, responseClone));
                }
                return response;
            })
            .catch(() => {
                return caches.match(request);
            })
    );
});

// Push notification event
self.addEventListener('push', event => {
    console.log('Service Worker: Push notification received');
    
    let notificationData = {
        title: 'Vasavi Matrimony',
        body: 'You have a new notification',
        icon: '/img/pwa/icon-192x192.png',
        badge: '/img/pwa/badge-72x72.png',
        tag: 'general',
        requireInteraction: false,
        actions: []
    };
    
    if (event.data) {
        try {
            const data = event.data.json();
            notificationData = { ...notificationData, ...data };
            
            // Add actions based on notification type
            if (data.type === 'video_call') {
                notificationData.actions = [
                    { action: 'answer', title: 'Answer', icon: '/img/pwa/action-answer.png' },
                    { action: 'reject', title: 'Reject', icon: '/img/pwa/action-reject.png' }
                ];
                notificationData.requireInteraction = true;
                notificationData.tag = 'video_call';
            } else if (data.type === 'message') {
                notificationData.actions = [
                    { action: 'reply', title: 'Reply', icon: '/img/pwa/action-reply.png' },
                    { action: 'view', title: 'View', icon: '/img/pwa/action-view.png' }
                ];
            } else if (data.type === 'interest') {
                notificationData.actions = [
                    { action: 'accept', title: 'Accept', icon: '/img/pwa/action-accept.png' },
                    { action: 'view_profile', title: 'View Profile', icon: '/img/pwa/action-profile.png' }
                ];
            }
        } catch (error) {
            console.error('Service Worker: Error parsing push data', error);
        }
    }
    
    event.waitUntil(
        self.registration.showNotification(notificationData.title, notificationData)
    );
});

// Notification click event
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification clicked', event);
    
    event.notification.close();
    
    const action = event.action;
    const data = event.notification.data || {};
    
    let url = '/';
    
    // Handle different notification actions
    switch (action) {
        case 'answer':
            if (data.call_id) {
                url = `/video-call/video-call-room.php?call_id=${data.call_id}&channel=${data.channel}&incoming=true`;
            }
            break;
        case 'reject':
            // Handle call rejection via API
            handleCallRejection(data.call_id);
            return;
        case 'reply':
            url = `/chat.php?user_id=${data.sender_id}`;
            break;
        case 'view':
            url = `/inboxMessages.php`;
            break;
        case 'accept':
            url = `/exp-interest.php`;
            break;
        case 'view_profile':
            url = `/profile.php?user_id=${data.sender_id}`;
            break;
        default:
            // Default click behavior
            if (data.url) {
                url = data.url;
            } else if (data.type === 'video_call') {
                url = '/call-history.php';
            } else if (data.type === 'message') {
                url = '/inboxMessages.php';
            } else if (data.type === 'interest') {
                url = '/exp-interest.php';
            }
    }
    
    event.waitUntil(
        clients.matchAll({ type: 'window', includeUncontrolled: true })
            .then(clientList => {
                // Check if there's already a window/tab open
                for (const client of clientList) {
                    if (client.url.includes(url.split('?')[0]) && 'focus' in client) {
                        return client.focus();
                    }
                }
                
                // If no existing window, open a new one
                if (clients.openWindow) {
                    return clients.openWindow(url);
                }
            })
    );
});

// Background sync event
self.addEventListener('sync', event => {
    console.log('Service Worker: Background sync triggered', event.tag);
    
    if (event.tag === 'background-sync-messages') {
        event.waitUntil(syncMessages());
    } else if (event.tag === 'background-sync-profile-views') {
        event.waitUntil(syncProfileViews());
    }
});

// Helper function to handle call rejection
async function handleCallRejection(callId) {
    try {
        await fetch('/video-call/agora-token-server.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'reject_call',
                call_id: callId,
                reason: 'rejected_from_notification'
            })
        });
    } catch (error) {
        console.error('Service Worker: Failed to reject call', error);
    }
}

// Helper function to sync messages
async function syncMessages() {
    try {
        // Sync pending messages when back online
        const pendingMessages = await getStoredData('pending_messages');
        if (pendingMessages && pendingMessages.length > 0) {
            for (const message of pendingMessages) {
                await fetch('/api/send_message.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(message)
                });
            }
            await clearStoredData('pending_messages');
        }
    } catch (error) {
        console.error('Service Worker: Failed to sync messages', error);
    }
}

// Helper function to sync profile views
async function syncProfileViews() {
    try {
        // Sync profile view analytics when back online
        const pendingViews = await getStoredData('pending_profile_views');
        if (pendingViews && pendingViews.length > 0) {
            for (const view of pendingViews) {
                await fetch('/api/track_profile_view.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(view)
                });
            }
            await clearStoredData('pending_profile_views');
        }
    } catch (error) {
        console.error('Service Worker: Failed to sync profile views', error);
    }
}

// Helper functions for IndexedDB storage
async function getStoredData(key) {
    // Implementation for getting data from IndexedDB
    // This would be implemented based on your storage needs
    return [];
}

async function clearStoredData(key) {
    // Implementation for clearing data from IndexedDB
    // This would be implemented based on your storage needs
}
