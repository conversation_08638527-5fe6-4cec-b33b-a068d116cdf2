<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit03e6a1bcd963940132c262db95b64b9e
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            '<PERSON><PERSON><PERSON><PERSON><PERSON>\\PHPMailer\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit03e6a1bcd963940132c262db95b64b9e::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit03e6a1bcd963940132c262db95b64b9e::$prefixDirsPsr4;

        }, null, ClassLoader::class);
    }
}
