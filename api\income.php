<?php
include_once 'databaseConn.php';
$DatabaseCo = new DatabaseConn();
$getIncomeQry = $DatabaseCo->dbLink->query("SELECT * FROM `income` WHERE status = 'APPROVED'");
if(mysqli_num_rows($getIncomeQry) > 0){
	$count=0;
	while($contact_res = mysqli_fetch_object($getIncomeQry)){
		$count++;
		$response = array('id' => $contact_res->id,'income' => $contact_res->income,'status'=>"1");
		$data["response"][] = $response; 
	}
	echo json_encode($data);
	exit;
}else{
	$data['status'] = "0";
	echo json_encode($data);
	exit;
}
?>
