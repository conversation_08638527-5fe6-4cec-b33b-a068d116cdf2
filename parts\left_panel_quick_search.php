<div class="gt-panel gt-panel-orange">
                	<div class="gt-panel-head">
                    	<div class="gt-panel-title text-center">
                        <?php echo $lang['QUICK SEARCH']; ?>
                        </div>
                    </div>
                    <div class="gt-quick-search">
                    	<div class="row">
                    	<form action="search_result" method="post">	
                            <div class="form-group">
                            	<label>
                                <?php echo $lang['Age']; ?>
                                </label>
                                <div class="row">
                                	<div class="col-xxl-6 col-xl-6 col-xs-6 col-lg-6 col-md-6 col-sm-6">
                                    	<select class="gt-form-control flat" name="fromage">
                                        	<option value="18">18 Years</option>
                                                                <option value="19">19 Years</option>
                                                                <option value="20" selected="selected">20 Years</option>
                                                                <option value="21">21 Years</option>
                                                                <option value="22">22 Years</option>
                                                                <option value="23">23 Years</option>
                                                                <option value="24">24 Years</option>
                                                                <option value="25">25 Years</option>
                                                                <option value="26">26 Years</option>
                                                                <option value="27">27 Years</option>
                                                                <option value="28">28 Years</option>
                                                                <option value="29">29 Years</option>
                                                                <option value="30">30 Years</option>
                                                                <option value="31">31 Years</option>
                                                                <option value="32">32 Years</option>
                                                                <option value="33">33 Years</option>
                                                                <option value="34">34 Years</option>
                                                                <option value="35">35 Years</option>
                                                                <option value="36">36 Years</option>
                                                                <option value="37">37 Years</option>
                                                                <option value="38">38 Years</option>
                                                                <option value="39">39 Years</option>
                                                                <option value="40">40 Years</option>
                                                                <option value="41">41 Years</option>
                                                                <option value="42">42 Years</option>
                                                                <option value="43">43 Years</option>
                                                                <option value="44">44 Years</option>
                                                                <option value="45">45 Years</option>
                                                                <option value="46">46 Years</option>
                                                                <option value="47">47 Years</option>
                                                                <option value="48">48 Years</option>
                                                                <option value="49">49 Years</option>
                                                                <option value="50">50 Years</option>
                                                                <option value="51">51 Years</option>
                                                                <option value="52">52 Years</option>
                                                                <option value="53">53 Years</option>
                                                                <option value="54">54 Years</option>
                                                                <option value="55">55 Years</option>
                                                                <option value="56">56 Years</option>
                                                                <option value="57">57 Years</option>
                                                                <option value="58">58 Years</option>
                                                                <option value="59">59 Years</option>
                                                                <option value="60">60 Years</option>
                                        </select>
                                    </div>
                                    <div class="col-xxl-4 col-xl-4 col-xs-4 col-lg-4 col-md-4 col-sm-4 text-center gt-margin-bottom-5 gt-margin-top-5">
                                    <?php echo $lang['To']; ?>
                                    </div>
                                    <div class="col-xxl-6 col-xl-6 col-xs-6 col-lg-6 col-md-6 col-sm-6">
                                    	<select class="gt-form-control flat" name="toage">
                                        	 <option value="18">18 Years</option>
                                                                <option value="19">19 Years</option>
                                                                <option value="20">20 Years</option>
                                                                <option value="21">21 Years</option>
                                                                <option value="22">22 Years</option>
                                                                <option value="23">23 Years</option>
                                                                <option value="24">24 Years</option>
                                                                <option value="25">25 Years</option>
                                                                <option value="26">26 Years</option>
                                                                <option value="27">27 Years</option>
                                                                <option value="28">28 Years</option>
                                                                <option value="29">29 Years</option>
                                                                <option value="30" selected="selected">30 Years</option>
                                                                <option value="31">31 Years</option>
                                                                <option value="32">32 Years</option>
                                                                <option value="33">33 Years</option>
                                                                <option value="34">34 Years</option>
                                                                <option value="35">35 Years</option>
                                                                <option value="36">36 Years</option>
                                                                <option value="37">37 Years</option>
                                                                <option value="38">38 Years</option>
                                                                <option value="39">39 Years</option>
                                                                <option value="40">40 Years</option>
                                                                <option value="41">41 Years</option>
                                                                <option value="42">42 Years</option>
                                                                <option value="43">43 Years</option>
                                                                <option value="44">44 Years</option>
                                                                <option value="45">45 Years</option>
                                                                <option value="46">46 Years</option>
                                                                <option value="47">47 Years</option>
                                                                <option value="48">48 Years</option>
                                                                <option value="49">49 Years</option>
                                                                <option value="50">50 Years</option>
                                                                <option value="51">51 Years</option>
                                                                <option value="52">52 Years</option>
                                                                <option value="53">53 Years</option>
                                                                <option value="54">54 Years</option>
                                                                <option value="55">55 Years</option>
                                                                <option value="56">56 Years</option>
                                                                <option value="57">57 Years</option>
                                                                <option value="58">58 Years</option>
                                                                <option value="59">59 Years</option>
                                                                <option value="60">60 Years</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                            	<label>
                                <?php echo $lang['Height']; ?>
                                </label>
                                <div class="row">
                                	<div class="col-xxl-6 col-xl-6 col-xs-6 col-lg-6 col-md-6 col-sm-6">
                                    	<select class="gt-form-control flat" name="fromheight">
                                        	<option value="48">Below 4ft</option>
                                                            <option value="54" selected>4ft 06in</option>
                                                            <option value="55">4ft 07in</option>
                                                            <option value="56">4ft 08in</option>
                                                            <option value="57">4ft 09in</option>
                                                            <option value="58">4ft 10in</option>
                                                            <option value="59">4ft 11in</option>
                                                            <option value="60">5ft</option>
                                                            <option value="61">5ft 01in</option>
                                                            <option value="62">5ft 02in</option>
                                                            <option value="63">5ft 03in</option>
                                                            <option value="64">5ft 04in</option>
                                                            <option value="65">5ft 05in</option>
                                                            <option value="66">5ft 06in</option>
                                                            <option value="67">5ft 07in</option>
                                                            <option value="68">5ft 08in</option>
                                                            <option value="69">5ft 09in</option>
                                                            <option value="70">5ft 10in</option>
                                                            <option value="71">5ft 11in</option>
                                                            <option value="72">6ft</option>
                                                            <option value="73">6ft 01in</option>
                                                            <option value="74">6ft 02in</option>
                                                            <option value="75">6ft 03in</option>
                                                            <option value="76">6ft 04in</option>
                                                            <option value="77">6ft 05in</option>
                                                            <option value="78">6ft 06in</option>
                                                            <option value="79">6ft 07in</option>
                                                            <option value="80">6ft 08in</option>
                                                            <option value="81">6ft 09in</option>
                                                            <option value="82">6ft 10in</option>
                                                            <option value="83">6ft 11in</option>
                                                            <option value="84">7ft</option>
                                                            <option value="85">Above 7ft</option>
                                        </select>
                                    </div>
                                    <div class="col-xxl-4 col-xl-4 col-xs-4 col-lg-4 col-md-4 col-sm-4 text-center gt-margin-bottom-5 gt-margin-top-5">
                                    <?php echo $lang['To']; ?>
                                    </div>
                                    <div class="col-xxl-6 col-xl-6 col-xs-6 col-lg-6 col-md-6 col-sm-6">
                                    	<select class="gt-form-control flat" name="toheight">
                                        					<option value="48">Below 4ft</option>
                                                            <option value="54">4ft 06in</option>
                                                            <option value="55">4ft 07in</option>
                                                            <option value="56">4ft 08in</option>
                                                            <option value="57">4ft 09in</option>
                                                            <option value="58">4ft 10in</option>
                                                            <option value="59">4ft 11in</option>
                                                            <option value="60">5ft</option>
                                                            <option value="61">5ft 01in</option>
                                                            <option value="62">5ft 02in</option>
                                                            <option value="63">5ft 03in</option>
                                                            <option value="64" selected>5ft 04in</option>
                                                            <option value="65">5ft 05in</option>
                                                            <option value="66">5ft 06in</option>
                                                            <option value="67">5ft 07in</option>
                                                            <option value="68">5ft 08in</option>
                                                            <option value="69">5ft 09in</option>
                                                            <option value="70">5ft 10in</option>
                                                            <option value="71">5ft 11in</option>
                                                            <option value="72">6ft</option>
                                                            <option value="73">6ft 01in</option>
                                                            <option value="74">6ft 02in</option>
                                                            <option value="75">6ft 03in</option>
                                                            <option value="76">6ft 04in</option>
                                                            <option value="77">6ft 05in</option>
                                                            <option value="78">6ft 06in</option>
                                                            <option value="79">6ft 07in</option>
                                                            <option value="80">6ft 08in</option>
                                                            <option value="81">6ft 09in</option>
                                                            <option value="82">6ft 10in</option>
                                                            <option value="83">6ft 11in</option>
                                                            <option value="84">7ft</option>
                                                            <option value="85">Above 7ft</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                            	<label>
                                <?php echo $lang['Religion']; ?>
                                </label>
                                <div class="row">
                                	<div class="col-xxl-16 col-xl-16 col-xs-16">
                                		
                                        <select data-placeholder="Choose a Religion..." class="chosen-select gt-form-control flat" multiple tabindex="5" id="religion_id" name="religion[]">
                                                        <?php
														   $SQL_STATEMENT_preligion =  $DatabaseCo->dbLink->query("SELECT * FROM religion WHERE status='APPROVED' ORDER BY religion_name ASC");
														  
														   while($DatabaseCo->dbRow = mysqli_fetch_object($SQL_STATEMENT_preligion))
														   {
														?>
															 <option value="<?php echo $DatabaseCo->dbRow->religion_id; ?>"><?php echo $DatabaseCo->dbRow->religion_name; ?></option>
														<?php 
															} 
														?>
                                                    </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                            	<label>
                                <?php echo $lang['Status']; ?>
                                </label>
                                <div class="row">
                                	<div class="col-xxl-16 col-xl-16 col-xs-16">
                                		<select data-placeholder="Choose a Marital Status..." class="chosen-select gt-form-control flat" multiple tabindex="4" name="m_status[]">
                                                       
                                                       <option value="Unmarried">Unmarried</option>
                                                        <option value="Widow/Widower">Widow/Widower</option>
                                                        <option value="Divorced">Divorced</option>
                                                        <option value="Separated">Separated</option>
                                                </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                            	<input type="submit" class="btn gt-btn-orange flat gt-btn-xl btn-block ripplelink" value="<?php echo $lang['Search']; ?>">
                            </div>
                         </form>   
                        </div>
                    </div>
                </div>