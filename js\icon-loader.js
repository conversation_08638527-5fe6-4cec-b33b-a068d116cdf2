/**
 * Icon Loader Script
 * Ensures all Font Awesome icons load correctly on frontend
 */

(function() {
    'use strict';
    
    // Icon loading configuration
    var IconLoader = {
        // Font Awesome Kit URL
        kitUrl: 'https://kit.fontawesome.com/48403ccd1a.js',
        
        // CDN Fallback URLs
        fallbackUrls: [
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
            'https://use.fontawesome.com/releases/v6.4.0/css/all.css'
        ],
        
        // Icons that must be available
        requiredIcons: [
            'fa-home', 'fa-search', 'fa-envelope', 'fa-phone', 'fa-heart',
            'fa-camera', 'fa-upload', 'fa-download', 'fa-cog', 'fa-edit',
            'fa-trash', 'fa-plus', 'fa-minus', 'fa-check', 'fa-times',
            'fa-crown', 'fa-user', 'fa-shield-alt', 'fa-mobile-alt',
            'fa-clock', 'fa-headset', 'fa-lock', 'fa-credit-card',
            'fa-arrow-left', 'fa-spinner', 'fa-exclamation-triangle',
            'fa-check-circle', 'fa-times-circle', 'fa-info-circle',
            'fa-rupee-sign', 'fa-money-bill', 'fa-wallet', 'fa-receipt'
        ],
        
        // Brand icons
        brandIcons: [
            'fa-facebook-square', 'fa-twitter-square', 'fa-linkedin',
            'fa-pinterest-square', 'fa-instagram', 'fa-youtube',
            'fa-whatsapp', 'fa-google'
        ],
        
        // Initialize icon loader
        init: function() {
            console.log('🎨 Icon Loader: Initializing...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', this.loadIcons.bind(this));
            } else {
                this.loadIcons();
            }
        },
        
        // Load icons
        loadIcons: function() {
            var self = this;
            
            // Check if Font Awesome is already loaded
            setTimeout(function() {
                if (!self.isFontAwesomeLoaded()) {
                    console.log('⚠️ Icon Loader: Font Awesome not detected, loading fallback...');
                    self.loadFallback();
                } else {
                    console.log('✅ Icon Loader: Font Awesome detected and working');
                    self.verifyIcons();
                }
            }, 1000);
        },
        
        // Check if Font Awesome is loaded
        isFontAwesomeLoaded: function() {
            try {
                // Create test element
                var testElement = document.createElement('i');
                testElement.className = 'fa fa-home';
                testElement.style.position = 'absolute';
                testElement.style.left = '-9999px';
                document.body.appendChild(testElement);
                
                // Check computed style
                var computedStyle = window.getComputedStyle(testElement, ':before');
                var isLoaded = computedStyle.content && 
                              computedStyle.content !== 'none' && 
                              computedStyle.content !== '""';
                
                // Clean up
                document.body.removeChild(testElement);
                
                return isLoaded;
            } catch (e) {
                console.error('Icon Loader: Error checking Font Awesome:', e);
                return false;
            }
        },
        
        // Load fallback CSS
        loadFallback: function() {
            var self = this;
            var fallbackIndex = 0;
            
            function tryFallback() {
                if (fallbackIndex >= self.fallbackUrls.length) {
                    console.error('❌ Icon Loader: All fallbacks failed');
                    self.showIconError();
                    return;
                }
                
                var url = self.fallbackUrls[fallbackIndex];
                console.log('🔄 Icon Loader: Trying fallback ' + (fallbackIndex + 1) + ':', url);
                
                var link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = url;
                
                link.onload = function() {
                    console.log('✅ Icon Loader: Fallback loaded successfully');
                    setTimeout(function() {
                        if (self.isFontAwesomeLoaded()) {
                            self.verifyIcons();
                        } else {
                            fallbackIndex++;
                            tryFallback();
                        }
                    }, 500);
                };
                
                link.onerror = function() {
                    console.log('❌ Icon Loader: Fallback failed, trying next...');
                    fallbackIndex++;
                    tryFallback();
                };
                
                document.head.appendChild(link);
            }
            
            tryFallback();
        },
        
        // Verify required icons
        verifyIcons: function() {
            var self = this;
            var missingIcons = [];
            
            // Check regular icons
            this.requiredIcons.forEach(function(iconClass) {
                if (!self.isIconAvailable(iconClass)) {
                    missingIcons.push(iconClass);
                }
            });
            
            // Check brand icons
            this.brandIcons.forEach(function(iconClass) {
                if (!self.isIconAvailable(iconClass, true)) {
                    missingIcons.push(iconClass + ' (brand)');
                }
            });
            
            if (missingIcons.length > 0) {
                console.warn('⚠️ Icon Loader: Missing icons:', missingIcons);
                this.fixMissingIcons(missingIcons);
            } else {
                console.log('✅ Icon Loader: All required icons available');
            }
            
            // Apply icon fixes
            this.applyIconFixes();
        },
        
        // Check if specific icon is available
        isIconAvailable: function(iconClass, isBrand) {
            try {
                var testElement = document.createElement('i');
                testElement.className = (isBrand ? 'fab ' : 'fa ') + iconClass;
                testElement.style.position = 'absolute';
                testElement.style.left = '-9999px';
                document.body.appendChild(testElement);
                
                var computedStyle = window.getComputedStyle(testElement, ':before');
                var isAvailable = computedStyle.content && 
                                 computedStyle.content !== 'none' && 
                                 computedStyle.content !== '""';
                
                document.body.removeChild(testElement);
                return isAvailable;
            } catch (e) {
                return false;
            }
        },
        
        // Fix missing icons with CSS
        fixMissingIcons: function(missingIcons) {
            console.log('🔧 Icon Loader: Applying fixes for missing icons...');
            
            // Create style element for fixes
            var style = document.createElement('style');
            style.id = 'icon-loader-fixes';
            
            var css = '/* Icon Loader Fixes */\n';
            
            // Add fallback styles for missing icons
            missingIcons.forEach(function(iconClass) {
                var cleanClass = iconClass.replace(' (brand)', '');
                var isBrand = iconClass.includes('(brand)');
                
                css += '.' + (isBrand ? 'fab' : 'fa') + '.' + cleanClass + ':before {\n';
                css += '  font-family: "Font Awesome 6 ' + (isBrand ? 'Brands' : 'Free') + '" !important;\n';
                css += '  font-weight: ' + (isBrand ? '400' : '900') + ' !important;\n';
                css += '  content: "\\f000"; /* Generic icon */\n';
                css += '}\n';
            });
            
            style.textContent = css;
            document.head.appendChild(style);
        },
        
        // Apply general icon fixes
        applyIconFixes: function() {
            // Ensure all FA classes have proper font family
            var style = document.createElement('style');
            style.id = 'icon-loader-general-fixes';
            
            var css = `
                /* General Icon Fixes */
                .fa, .fas, .far, .fal {
                    font-family: "Font Awesome 6 Free" !important;
                    font-weight: 900 !important;
                    font-style: normal !important;
                    font-variant: normal !important;
                    text-rendering: auto !important;
                    line-height: 1 !important;
                    -webkit-font-smoothing: antialiased !important;
                    -moz-osx-font-smoothing: grayscale !important;
                }
                
                .fab {
                    font-family: "Font Awesome 6 Brands" !important;
                    font-weight: 400 !important;
                }
                
                .far {
                    font-weight: 400 !important;
                }
                
                .fal {
                    font-weight: 300 !important;
                }
                
                /* Spinner animation */
                .fa-spin {
                    animation: fa-spin 1s linear infinite !important;
                }
                
                @keyframes fa-spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            
            style.textContent = css;
            document.head.appendChild(style);
            
            console.log('✅ Icon Loader: General fixes applied');
        },
        
        // Show error message for icon loading failure
        showIconError: function() {
            console.error('❌ Icon Loader: Failed to load icons');
            
            // Create error notification
            var errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: #f8d7da;
                color: #721c24;
                padding: 10px 15px;
                border: 1px solid #f5c6cb;
                border-radius: 5px;
                z-index: 9999;
                font-size: 14px;
                max-width: 300px;
            `;
            errorDiv.innerHTML = '⚠️ Some icons may not display correctly. Please refresh the page.';
            
            document.body.appendChild(errorDiv);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        }
    };
    
    // Initialize when script loads
    IconLoader.init();
    
    // Make available globally for debugging
    window.IconLoader = IconLoader;
    
})();

// Console message
console.log('🎨 Icon Loader Script Loaded');
console.log('📡 Monitoring Font Awesome icon availability...');
