<?php
session_start();
include_once 'databaseConn.php';
include_once './class/Config.class.php';

$DatabaseCo = new DatabaseConn();
$configObj = new Config();
$mid = $_SESSION['user_id'] ? $_SESSION['user_id'] : '';

// Check if user is logged in
if (empty($mid)) {
    header('Location: login.php');
    exit();
}

// Get user data
$userQuery = $DatabaseCo->dbLink->query("
    SELECT username, photo1, status 
    FROM register 
    WHERE matri_id = '$mid'
");
$userData = mysqli_fetch_object($userQuery);

// Check if user can access video calling
$canAccessVideoCalls = false;
$videoCallQuery = $DatabaseCo->dbLink->query("
    SELECT r.status, p.chat, p.video_calls 
    FROM register r 
    LEFT JOIN payments p ON r.matri_id = p.pmatri_id 
    WHERE r.matri_id = '$mid'
");

if ($videoCallQuery && ($videoCallData = mysqli_fetch_object($videoCallQuery))) {
    $canAccessVideoCalls = ($videoCallData->status === 'Paid' && $videoCallData->chat === 'Yes') || 
                          ($videoCallData->status === 'Active');
}

if (!$canAccessVideoCalls) {
    header('Location: membership.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Video Call History - <?php echo $configObj->getConfigTitle(); ?></title>
    <meta name="keyword" content="<?php echo $configObj->getConfigKeyword(); ?>" />
    <meta name="description" content="<?php echo $configObj->getConfigDescription(); ?>" />
    <link type="image/x-icon" href="img/<?php echo $configObj->getConfigFevicon(); ?>" rel="shortcut icon"/>

    <!-- Theme Color -->
    <meta name="theme-color" content="#549a11">
    <meta name="msapplication-navbutton-color" content="#549a11">
    <meta name="apple-mobile-web-app-status-bar-style" content="#549a11">
    
    <!-- Bootstrap & Custom CSS-->
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/custom-responsive.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/video-call.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>

    <!-- Google Fonts -->
    <?php include('parts/google_fonts.php');?>
    
    <style>
        .call-history-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .call-history-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .call-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .call-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-completed { background: #d4edda; color: #155724; }
        .status-missed { background: #f8d7da; color: #721c24; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .status-ongoing { background: #d1ecf1; color: #0c5460; }
        
        .call-duration {
            font-weight: 600;
            color: #28a745;
        }
        
        .call-time {
            color: #6c757d;
            font-size: 13px;
        }
        
        .call-direction {
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .direction-incoming { color: #28a745; }
        .direction-outgoing { color: #007bff; }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stats-item {
            text-align: center;
            padding: 10px;
        }
        
        .stats-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }
        
        .stats-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .filter-tabs {
            margin-bottom: 20px;
        }
        
        .filter-tabs .nav-link {
            border-radius: 20px;
            margin-right: 10px;
            border: 2px solid #e0e0e0;
            color: #666;
        }
        
        .filter-tabs .nav-link.active {
            background: #549a11;
            border-color: #549a11;
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- Loader -->
    <div class="preloader-wrapper text-center">
        <div class="loader"></div>
        <h5>Loading...</h5>
    </div>
    
    <div id="body" style="display:none">
        <div id="wrap">
            <div id="main">
                <!-- Header & Menu -->
                <?php include "parts/header.php"; ?>
                <?php include "parts/menu.php"; ?>
                
                <div class="container">
                    <div class="row">
                        <div class="col-xs-16 col-lg-16 col-xxl-16 col-xl-16 mb-20 text-center">
                            <h2 class="inPageTitle fontMerriWeather inThemeOrange">
                                <i class="fas fa-video gt-margin-right-10"></i>Video Call History
                            </h2>
                            <p class="inPageSubTitle">View your video call history and statistics</p>
                        </div>
                    </div>
                    
                    <!-- Statistics Cards -->
                    <div class="row" id="call-statistics">
                        <div class="col-xs-16">
                            <div class="stats-card">
                                <div class="row">
                                    <div class="col-xs-4 col-sm-4">
                                        <div class="stats-item">
                                            <span class="stats-number" id="total-calls">0</span>
                                            <span class="stats-label">Total Calls</span>
                                        </div>
                                    </div>
                                    <div class="col-xs-4 col-sm-4">
                                        <div class="stats-item">
                                            <span class="stats-number" id="completed-calls">0</span>
                                            <span class="stats-label">Completed</span>
                                        </div>
                                    </div>
                                    <div class="col-xs-4 col-sm-4">
                                        <div class="stats-item">
                                            <span class="stats-number" id="missed-calls">0</span>
                                            <span class="stats-label">Missed</span>
                                        </div>
                                    </div>
                                    <div class="col-xs-4 col-sm-4">
                                        <div class="stats-item">
                                            <span class="stats-number" id="total-time">0m</span>
                                            <span class="stats-label">Total Time</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Filter Tabs -->
                    <div class="row">
                        <div class="col-xs-16">
                            <ul class="nav nav-pills filter-tabs">
                                <li class="nav-item">
                                    <a class="nav-link active" href="#" data-filter="all">All Calls</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" data-filter="incoming">Incoming</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" data-filter="outgoing">Outgoing</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" data-filter="missed">Missed</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" data-filter="answered">Answered</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Call History List -->
                    <div class="row">
                        <div class="col-xs-16">
                            <div id="call-history-container">
                                <!-- Call history will be loaded here -->
                            </div>
                            
                            <!-- Load More Button -->
                            <div class="text-center" id="load-more-container" style="display: none;">
                                <button class="btn btn-primary" id="load-more-btn">
                                    <i class="fas fa-spinner fa-spin" style="display: none;"></i>
                                    Load More Calls
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <?php include "parts/footer.php"; ?>
    </div>

    <!-- jQuery -->
    <script src="js/jquery.min.js"></script>
    <!-- Bootstrap -->
    <script src="js/bootstrap.js"></script>
    <script src="js/green.js"></script>
    <!-- Video Call JS -->
    <script src="js/video-call.js"></script>
    
    <script>
        let currentFilter = 'all';
        let currentOffset = 0;
        let isLoading = false;
        let hasMore = true;
        
        $(document).ready(function() {
            $('#body').show();
            $('.preloader-wrapper').hide();
            
            // Set current user ID
            window.currentUserId = '<?php echo $mid; ?>';
            
            // Load initial data
            loadCallHistory();
            
            // Filter tabs
            $('.filter-tabs .nav-link').click(function(e) {
                e.preventDefault();
                
                if ($(this).hasClass('active')) return;
                
                $('.filter-tabs .nav-link').removeClass('active');
                $(this).addClass('active');
                
                currentFilter = $(this).data('filter');
                currentOffset = 0;
                hasMore = true;
                
                $('#call-history-container').empty();
                loadCallHistory();
            });
            
            // Load more button
            $('#load-more-btn').click(function() {
                if (!isLoading && hasMore) {
                    loadCallHistory();
                }
            });
        });
        
        async function loadCallHistory() {
            if (isLoading) return;
            
            isLoading = true;
            $('#load-more-btn .fa-spinner').show();
            $('#load-more-btn').prop('disabled', true);
            
            try {
                const response = await fetch('api/get_call_history.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: window.currentUserId,
                        limit: 10,
                        offset: currentOffset,
                        filter: currentFilter
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // Update statistics on first load
                    if (currentOffset === 0) {
                        updateStatistics(result.data.statistics);
                    }
                    
                    // Render call history
                    renderCallHistory(result.data.calls);
                    
                    // Update pagination
                    currentOffset += result.data.calls.length;
                    hasMore = result.data.pagination.has_more;
                    
                    if (hasMore) {
                        $('#load-more-container').show();
                    } else {
                        $('#load-more-container').hide();
                    }
                    
                    // Show empty state if no calls
                    if (result.data.calls.length === 0 && currentOffset === 0) {
                        showEmptyState();
                    }
                } else {
                    console.error('Failed to load call history:', result.message);
                }
                
            } catch (error) {
                console.error('Error loading call history:', error);
            } finally {
                isLoading = false;
                $('#load-more-btn .fa-spinner').hide();
                $('#load-more-btn').prop('disabled', false);
            }
        }
        
        function updateStatistics(stats) {
            $('#total-calls').text(stats.total_calls);
            $('#completed-calls').text(stats.completed_calls);
            $('#missed-calls').text(stats.missed_calls);
            $('#total-time').text(stats.formatted_talk_time);
        }
        
        function renderCallHistory(calls) {
            calls.forEach(call => {
                const callHtml = createCallHistoryItem(call);
                $('#call-history-container').append(callHtml);
            });
        }
        
        function createCallHistoryItem(call) {
            const statusClass = `status-${call.status === 'ended' ? 'completed' : call.status}`;
            const directionClass = `direction-${call.direction}`;
            const directionIcon = call.direction === 'incoming' ? 'fa-arrow-down' : 'fa-arrow-up';
            
            return `
                <div class="call-history-item">
                    <div class="row">
                        <div class="col-xs-3 col-sm-2">
                            <img src="my_photos/${call.other_user.photo}" 
                                 alt="${call.other_user.name}" 
                                 class="call-avatar"
                                 onerror="this.src='img/default-avatar.png'">
                        </div>
                        <div class="col-xs-9 col-sm-10">
                            <div class="row">
                                <div class="col-xs-12 col-sm-8">
                                    <h5 class="mb-5">${call.other_user.name}</h5>
                                    <div class="call-direction ${directionClass}">
                                        <i class="fas ${directionIcon}"></i>
                                        ${call.direction.charAt(0).toUpperCase() + call.direction.slice(1)} Call
                                    </div>
                                    <span class="call-status ${statusClass}">${call.status_display}</span>
                                    ${call.duration > 0 ? `<span class="call-duration ml-10">${call.formatted_duration}</span>` : ''}
                                </div>
                                <div class="col-xs-4 col-sm-4 text-right">
                                    <div class="call-time">${call.time_ago}</div>
                                    ${call.can_callback ? `
                                        <button class="btn btn-sm btn-primary video-call-btn mt-5" 
                                                data-user-id="${call.other_user.id}">
                                            <i class="fas fa-video"></i> Call Back
                                        </button>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function showEmptyState() {
            const emptyHtml = `
                <div class="empty-state">
                    <i class="fas fa-video"></i>
                    <h4>No Video Calls Yet</h4>
                    <p>Start connecting with potential matches through video calls!</p>
                    <a href="search" class="btn btn-primary">
                        <i class="fas fa-search"></i> Find Matches
                    </a>
                </div>
            `;
            $('#call-history-container').html(emptyHtml);
        }
    </script>
</body>
</html>
