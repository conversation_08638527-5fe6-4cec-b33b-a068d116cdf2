/**
 * Enhanced Chat System with Real-time Features
 * Matrimony Platform - Advanced Messaging
 */

class EnhancedChatManager {
    constructor() {
        this.currentChatUser = null;
        this.isTyping = false;
        this.typingTimeout = null;
        this.lastMessageTime = 0;
        this.messagePollingInterval = null;
        this.typingIndicatorInterval = null;
        this.unreadCounts = {};
        this.chatContainer = null;
        this.messageInput = null;
        this.fileInput = null;
        this.emojiPicker = null;
        this.isEmojiPickerOpen = false;
        
        this.init();
    }
    
    /**
     * Initialize enhanced chat system
     */
    init() {
        this.setupChatInterface();
        this.setupEventListeners();
        this.setupFileUpload();
        this.setupEmojiPicker();
        this.startMessagePolling();
        this.loadChatHistory();
    }
    
    /**
     * Setup chat interface elements
     */
    setupChatInterface() {
        this.chatContainer = document.getElementById('chat-messages');
        this.messageInput = document.getElementById('message-input');
        this.fileInput = document.getElementById('file-input');
        
        if (!this.chatContainer) {
            console.error('Chat container not found');
            return;
        }
        
        // Add enhanced chat controls
        this.addChatControls();
    }
    
    /**
     * Add enhanced chat controls
     */
    addChatControls() {
        const chatControlsHTML = `
            <div class="chat-controls">
                <div class="chat-input-container">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <button class="btn btn-outline-secondary emoji-btn" type="button" title="Add Emoji">
                                <i class="fas fa-smile"></i>
                            </button>
                            <button class="btn btn-outline-secondary file-btn" type="button" title="Attach File">
                                <i class="fas fa-paperclip"></i>
                            </button>
                        </div>
                        <input type="text" class="form-control" id="message-input" 
                               placeholder="Type your message..." maxlength="1000">
                        <div class="input-group-append">
                            <button class="btn btn-primary send-btn" type="button" title="Send Message">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                    <input type="file" id="file-input" style="display: none;" 
                           accept="image/*,video/*,.pdf,.doc,.docx">
                </div>
                <div class="typing-indicator" id="typing-indicator" style="display: none;">
                    <span class="typing-dots">
                        <span></span><span></span><span></span>
                    </span>
                    <span class="typing-text">User is typing...</span>
                </div>
                <div class="emoji-picker" id="emoji-picker" style="display: none;"></div>
            </div>
        `;
        
        const chatFooter = document.querySelector('.chat-footer') || 
                          document.querySelector('#chat-container');
        
        if (chatFooter) {
            chatFooter.insertAdjacentHTML('beforeend', chatControlsHTML);
        }
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Message input events
        if (this.messageInput) {
            this.messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
            
            this.messageInput.addEventListener('input', () => {
                this.handleTyping();
            });
            
            this.messageInput.addEventListener('focus', () => {
                this.markMessagesAsRead();
            });
        }
        
        // Send button
        const sendBtn = document.querySelector('.send-btn');
        if (sendBtn) {
            sendBtn.addEventListener('click', () => this.sendMessage());
        }
        
        // File button
        const fileBtn = document.querySelector('.file-btn');
        if (fileBtn) {
            fileBtn.addEventListener('click', () => this.openFileDialog());
        }
        
        // Emoji button
        const emojiBtn = document.querySelector('.emoji-btn');
        if (emojiBtn) {
            emojiBtn.addEventListener('click', () => this.toggleEmojiPicker());
        }
        
        // Window focus/blur events for read receipts
        window.addEventListener('focus', () => {
            this.markMessagesAsRead();
        });
        
        // Scroll to load more messages
        if (this.chatContainer) {
            this.chatContainer.addEventListener('scroll', () => {
                if (this.chatContainer.scrollTop === 0) {
                    this.loadMoreMessages();
                }
            });
        }
    }
    
    /**
     * Setup file upload functionality
     */
    setupFileUpload() {
        if (this.fileInput) {
            this.fileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handleFileUpload(file);
                }
            });
        }
    }
    
    /**
     * Setup emoji picker
     */
    setupEmojiPicker() {
        const emojis = [
            '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
            '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
            '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
            '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
            '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
            '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
            '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
            '👍', '👎', '👌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈',
            '👉', '👆', '🖕', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖'
        ];
        
        const emojiPickerContainer = document.getElementById('emoji-picker');
        if (emojiPickerContainer) {
            const emojiHTML = emojis.map(emoji => 
                `<span class="emoji-item" data-emoji="${emoji}">${emoji}</span>`
            ).join('');
            
            emojiPickerContainer.innerHTML = `
                <div class="emoji-grid">
                    ${emojiHTML}
                </div>
            `;
            
            // Add emoji click handlers
            emojiPickerContainer.addEventListener('click', (e) => {
                if (e.target.classList.contains('emoji-item')) {
                    this.insertEmoji(e.target.dataset.emoji);
                }
            });
        }
    }
    
    /**
     * Send message
     */
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || !this.currentChatUser) return;
        
        // Clear input immediately for better UX
        this.messageInput.value = '';
        this.stopTyping();
        
        // Add message to UI optimistically
        this.addMessageToUI({
            message: message,
            sender_id: this.getCurrentUserId(),
            receiver_id: this.currentChatUser,
            timestamp: new Date().toISOString(),
            status: 'sending',
            message_type: 'text'
        });
        
        try {
            const response = await fetch('api/send_message.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    receiver_id: this.currentChatUser,
                    message: message,
                    message_type: 'text'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Update message status
                this.updateMessageStatus(result.data.message_id, 'sent');
                this.playNotificationSound('sent');
            } else {
                this.updateMessageStatus('temp', 'failed');
                this.showError('Failed to send message: ' + result.message);
            }
            
        } catch (error) {
            console.error('Error sending message:', error);
            this.updateMessageStatus('temp', 'failed');
            this.showError('Failed to send message. Please try again.');
        }
    }
    
    /**
     * Handle file upload
     */
    async handleFileUpload(file) {
        if (!this.currentChatUser) return;
        
        // Validate file
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            this.showError('File size must be less than 10MB');
            return;
        }
        
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 
                             'application/pdf', 'application/msword', 
                             'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        
        if (!allowedTypes.includes(file.type)) {
            this.showError('File type not supported');
            return;
        }
        
        // Show upload progress
        this.showUploadProgress(file.name);
        
        const formData = new FormData();
        formData.append('file', file);
        formData.append('receiver_id', this.currentChatUser);
        formData.append('message_type', file.type.startsWith('image/') ? 'image' : 'file');
        
        try {
            const response = await fetch('api/upload_chat_file.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.addMessageToUI({
                    message: result.data.file_path,
                    sender_id: this.getCurrentUserId(),
                    receiver_id: this.currentChatUser,
                    timestamp: new Date().toISOString(),
                    status: 'sent',
                    message_type: result.data.message_type,
                    file_name: file.name,
                    file_size: file.size
                });
                
                this.playNotificationSound('sent');
            } else {
                this.showError('Failed to upload file: ' + result.message);
            }
            
        } catch (error) {
            console.error('Error uploading file:', error);
            this.showError('Failed to upload file. Please try again.');
        } finally {
            this.hideUploadProgress();
            this.fileInput.value = '';
        }
    }
    
    /**
     * Handle typing indicator
     */
    handleTyping() {
        if (!this.isTyping) {
            this.isTyping = true;
            this.sendTypingStatus(true);
        }
        
        // Clear existing timeout
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
        }
        
        // Set new timeout
        this.typingTimeout = setTimeout(() => {
            this.stopTyping();
        }, 3000);
    }
    
    /**
     * Stop typing
     */
    stopTyping() {
        if (this.isTyping) {
            this.isTyping = false;
            this.sendTypingStatus(false);
        }
        
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
            this.typingTimeout = null;
        }
    }
    
    /**
     * Send typing status
     */
    async sendTypingStatus(isTyping) {
        if (!this.currentChatUser) return;
        
        try {
            await fetch('api/typing_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    receiver_id: this.currentChatUser,
                    is_typing: isTyping
                })
            });
        } catch (error) {
            console.error('Error sending typing status:', error);
        }
    }
    
    /**
     * Start message polling
     */
    startMessagePolling() {
        // Poll for new messages every 3 seconds
        this.messagePollingInterval = setInterval(() => {
            this.checkForNewMessages();
            this.checkTypingStatus();
        }, 3000);
    }
    
    /**
     * Check for new messages
     */
    async checkForNewMessages() {
        if (!this.currentChatUser) return;
        
        try {
            const response = await fetch('api/get_new_messages.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    chat_user_id: this.currentChatUser,
                    last_message_time: this.lastMessageTime
                })
            });
            
            const result = await response.json();
            
            if (result.success && result.data.messages.length > 0) {
                result.data.messages.forEach(message => {
                    this.addMessageToUI(message);
                    this.lastMessageTime = Math.max(this.lastMessageTime, 
                                                   new Date(message.timestamp).getTime());
                });
                
                // Play notification sound for received messages
                const receivedMessages = result.data.messages.filter(
                    msg => msg.sender_id !== this.getCurrentUserId()
                );
                
                if (receivedMessages.length > 0) {
                    this.playNotificationSound('received');
                    this.markMessagesAsRead();
                }
            }
            
        } catch (error) {
            console.error('Error checking for new messages:', error);
        }
    }
    
    /**
     * Check typing status
     */
    async checkTypingStatus() {
        if (!this.currentChatUser) return;
        
        try {
            const response = await fetch('api/get_typing_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: this.currentChatUser
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.updateTypingIndicator(result.data.is_typing, result.data.user_name);
            }
            
        } catch (error) {
            console.error('Error checking typing status:', error);
        }
    }
    
    /**
     * Add message to UI
     */
    addMessageToUI(message) {
        if (!this.chatContainer) return;
        
        const isOwnMessage = message.sender_id === this.getCurrentUserId();
        const messageClass = isOwnMessage ? 'message-sent' : 'message-received';
        const statusIcon = this.getStatusIcon(message.status);
        
        let messageContent = '';
        
        if (message.message_type === 'image') {
            messageContent = `
                <div class="message-image">
                    <img src="${message.message}" alt="Shared image" class="chat-image" 
                         onclick="openImageModal('${message.message}')">
                </div>
            `;
        } else if (message.message_type === 'file') {
            messageContent = `
                <div class="message-file">
                    <i class="fas fa-file"></i>
                    <a href="${message.message}" target="_blank" download>
                        ${message.file_name || 'Download File'}
                    </a>
                    <span class="file-size">(${this.formatFileSize(message.file_size)})</span>
                </div>
            `;
        } else {
            messageContent = `<div class="message-text">${this.formatMessageText(message.message)}</div>`;
        }
        
        const messageHTML = `
            <div class="chat-message ${messageClass}" data-message-id="${message.message_id || 'temp'}">
                <div class="message-content">
                    ${messageContent}
                    <div class="message-meta">
                        <span class="message-time">${this.formatTime(message.timestamp)}</span>
                        ${isOwnMessage ? `<span class="message-status">${statusIcon}</span>` : ''}
                    </div>
                </div>
            </div>
        `;
        
        this.chatContainer.insertAdjacentHTML('beforeend', messageHTML);
        this.scrollToBottom();
    }
    
    /**
     * Format message text (handle emojis, links, etc.)
     */
    formatMessageText(text) {
        // Convert URLs to links
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        text = text.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener">$1</a>');
        
        // Convert line breaks
        text = text.replace(/\n/g, '<br>');
        
        return text;
    }
    
    /**
     * Get status icon
     */
    getStatusIcon(status) {
        switch (status) {
            case 'sending':
                return '<i class="fas fa-clock text-muted"></i>';
            case 'sent':
                return '<i class="fas fa-check text-muted"></i>';
            case 'delivered':
                return '<i class="fas fa-check-double text-muted"></i>';
            case 'read':
                return '<i class="fas fa-check-double text-primary"></i>';
            case 'failed':
                return '<i class="fas fa-exclamation-triangle text-danger"></i>';
            default:
                return '';
        }
    }
    
    /**
     * Update message status
     */
    updateMessageStatus(messageId, status) {
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
            const statusElement = messageElement.querySelector('.message-status');
            if (statusElement) {
                statusElement.innerHTML = this.getStatusIcon(status);
            }
        }
    }
    
    /**
     * Update typing indicator
     */
    updateTypingIndicator(isTyping, userName) {
        const indicator = document.getElementById('typing-indicator');
        if (!indicator) return;
        
        if (isTyping) {
            indicator.querySelector('.typing-text').textContent = `${userName} is typing...`;
            indicator.style.display = 'block';
        } else {
            indicator.style.display = 'none';
        }
    }
    
    /**
     * Toggle emoji picker
     */
    toggleEmojiPicker() {
        const picker = document.getElementById('emoji-picker');
        if (!picker) return;
        
        this.isEmojiPickerOpen = !this.isEmojiPickerOpen;
        picker.style.display = this.isEmojiPickerOpen ? 'block' : 'none';
    }
    
    /**
     * Insert emoji
     */
    insertEmoji(emoji) {
        if (this.messageInput) {
            const cursorPos = this.messageInput.selectionStart;
            const textBefore = this.messageInput.value.substring(0, cursorPos);
            const textAfter = this.messageInput.value.substring(cursorPos);
            
            this.messageInput.value = textBefore + emoji + textAfter;
            this.messageInput.focus();
            this.messageInput.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);
        }
        
        this.toggleEmojiPicker();
    }
    
    /**
     * Open file dialog
     */
    openFileDialog() {
        if (this.fileInput) {
            this.fileInput.click();
        }
    }
    
    /**
     * Show upload progress
     */
    showUploadProgress(fileName) {
        const progressHTML = `
            <div class="upload-progress" id="upload-progress">
                <div class="progress-info">
                    <i class="fas fa-upload"></i>
                    <span>Uploading ${fileName}...</span>
                </div>
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         style="width: 100%"></div>
                </div>
            </div>
        `;
        
        if (this.chatContainer) {
            this.chatContainer.insertAdjacentHTML('beforeend', progressHTML);
            this.scrollToBottom();
        }
    }
    
    /**
     * Hide upload progress
     */
    hideUploadProgress() {
        const progress = document.getElementById('upload-progress');
        if (progress) {
            progress.remove();
        }
    }
    
    /**
     * Mark messages as read
     */
    async markMessagesAsRead() {
        if (!this.currentChatUser) return;
        
        try {
            await fetch('api/mark_messages_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sender_id: this.currentChatUser
                })
            });
        } catch (error) {
            console.error('Error marking messages as read:', error);
        }
    }
    
    /**
     * Load chat history
     */
    async loadChatHistory() {
        if (!this.currentChatUser) return;
        
        try {
            const response = await fetch('api/get_chat_history.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    chat_user_id: this.currentChatUser,
                    limit: 50
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Clear existing messages
                if (this.chatContainer) {
                    this.chatContainer.innerHTML = '';
                }
                
                // Add messages to UI
                result.data.messages.forEach(message => {
                    this.addMessageToUI(message);
                    this.lastMessageTime = Math.max(this.lastMessageTime, 
                                                   new Date(message.timestamp).getTime());
                });
                
                this.scrollToBottom();
            }
            
        } catch (error) {
            console.error('Error loading chat history:', error);
        }
    }
    
    /**
     * Load more messages (pagination)
     */
    async loadMoreMessages() {
        // Implementation for loading older messages
        console.log('Loading more messages...');
    }
    
    /**
     * Play notification sound
     */
    playNotificationSound(type) {
        try {
            const audio = new Audio(`sounds/${type}.mp3`);
            audio.volume = 0.3;
            audio.play().catch(e => console.log('Could not play sound:', e));
        } catch (error) {
            console.log('Notification sound not available');
        }
    }
    
    /**
     * Utility functions
     */
    getCurrentUserId() {
        return window.currentUserId || document.querySelector('meta[name="user-id"]')?.content;
    }
    
    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    scrollToBottom() {
        if (this.chatContainer) {
            this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
        }
    }
    
    showError(message) {
        // You can customize this to use your preferred notification system
        console.error(message);
        alert(message);
    }
    
    /**
     * Set current chat user
     */
    setChatUser(userId) {
        this.currentChatUser = userId;
        this.lastMessageTime = 0;
        this.loadChatHistory();
    }
    
    /**
     * Cleanup
     */
    destroy() {
        if (this.messagePollingInterval) {
            clearInterval(this.messagePollingInterval);
        }
        
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
        }
        
        this.stopTyping();
    }
}

// Initialize enhanced chat when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('chat-messages')) {
        window.enhancedChatManager = new EnhancedChatManager();
    }
});

// Open image modal function
function openImageModal(imageSrc) {
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.innerHTML = `
        <div class="image-modal-content">
            <span class="image-modal-close">&times;</span>
            <img src="${imageSrc}" alt="Full size image">
        </div>
    `;
    
    document.body.appendChild(modal);
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal || e.target.classList.contains('image-modal-close')) {
            modal.remove();
        }
    });
}
