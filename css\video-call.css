/**
 * Video Call CSS Styles
 * Matrimony Platform - Video Call Integration
 */

/* Video Call Room Styles */
.video-call-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    background: #000;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Video Elements */
.remote-video-container {
    position: relative;
    width: 100%;
    height: 100%;
    background: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remote-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background: #000;
}

.local-video-container {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 200px;
    height: 150px;
    border-radius: 12px;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    z-index: 10;
    transition: all 0.3s ease;
}

.local-video-container:hover {
    border-color: rgba(255, 255, 255, 0.6);
    transform: scale(1.05);
}

.local-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background: #333;
}

/* Call Controls */
.call-controls {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
    z-index: 20;
    background: rgba(0, 0, 0, 0.3);
    padding: 15px 25px;
    border-radius: 50px;
    backdrop-filter: blur(10px);
}

.control-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s ease;
}

.control-btn:hover::before {
    transform: scale(1);
}

.control-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.control-btn:active {
    transform: translateY(-1px);
}

/* Control Button Types */
.btn-mute {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.btn-mute.muted {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    animation: pulse 1.5s infinite;
}

.btn-video {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

.btn-video.disabled {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.btn-end {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    width: 70px;
    height: 70px;
}

.btn-end:hover {
    background: linear-gradient(135deg, #d32f2f, #b71c1c);
}

/* Call Information */
.call-info {
    position: absolute;
    top: 20px;
    left: 20px;
    color: white;
    z-index: 15;
    background: rgba(0, 0, 0, 0.5);
    padding: 15px 20px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.call-timer {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #4CAF50;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
}

.user-name {
    font-size: 16px;
    font-weight: 500;
}

/* Incoming Call Screen */
.incoming-call {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(30, 30, 30, 0.9));
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 30;
    backdrop-filter: blur(20px);
}

.incoming-avatar {
    width: 180px;
    height: 180px;
    border-radius: 50%;
    margin-bottom: 30px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 50px rgba(255, 255, 255, 0.2);
    animation: glow 2s ease-in-out infinite alternate;
}

.incoming-name {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 10px;
    text-align: center;
}

.incoming-text {
    font-size: 18px;
    margin-bottom: 50px;
    opacity: 0.8;
    text-align: center;
    animation: fadeInOut 2s ease-in-out infinite;
}

.incoming-controls {
    display: flex;
    gap: 60px;
}

.btn-accept {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    width: 80px;
    height: 80px;
    font-size: 28px;
}

.btn-reject {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    width: 80px;
    height: 80px;
    font-size: 28px;
}

/* Connection Status */
.connection-status {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    text-align: center;
    z-index: 25;
    background: rgba(0, 0, 0, 0.7);
    padding: 30px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.connection-status .spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: 15px;
}

/* Incoming Call Notification */
.incoming-call-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    padding: 20px;
    z-index: 9999;
    min-width: 320px;
    max-width: 400px;
    animation: slideInRight 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    border-left: 5px solid #4CAF50;
}

.call-notification-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.caller-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.caller-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #4CAF50;
}

.caller-details h4 {
    margin: 0 0 5px 0;
    font-size: 18px;
    color: #333;
    font-weight: 600;
}

.caller-details p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.call-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.call-actions .btn {
    flex: 1;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.call-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Video Call Button */
.video-call-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.video-call-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
    background: linear-gradient(135deg, #0056b3, #004085);
}

.video-call-btn:active {
    transform: translateY(0);
}

.video-call-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.video-call-btn i {
    font-size: 16px;
}

/* Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
    }
    to {
        box-shadow: 0 0 50px rgba(255, 255, 255, 0.4);
    }
}

@keyframes fadeInOut {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .local-video-container {
        width: 120px;
        height: 90px;
        top: 15px;
        right: 15px;
    }
    
    .call-controls {
        bottom: 20px;
        gap: 15px;
        padding: 12px 20px;
    }
    
    .control-btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .btn-end {
        width: 60px;
        height: 60px;
    }
    
    .call-info {
        top: 15px;
        left: 15px;
        padding: 12px 15px;
    }
    
    .call-timer {
        font-size: 18px;
    }
    
    .user-avatar {
        width: 35px;
        height: 35px;
    }
    
    .user-name {
        font-size: 14px;
    }
    
    .incoming-call-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        min-width: auto;
        max-width: none;
    }
    
    .incoming-avatar {
        width: 120px;
        height: 120px;
        margin-bottom: 20px;
    }
    
    .incoming-name {
        font-size: 24px;
    }
    
    .incoming-text {
        font-size: 16px;
        margin-bottom: 30px;
    }
    
    .incoming-controls {
        gap: 40px;
    }
    
    .btn-accept,
    .btn-reject {
        width: 70px;
        height: 70px;
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .local-video-container {
        width: 100px;
        height: 75px;
    }
    
    .control-btn {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    .btn-end {
        width: 55px;
        height: 55px;
    }
    
    .call-controls {
        gap: 12px;
        padding: 10px 15px;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* Call Quality Indicator */
.call-quality-indicator {
    position: absolute;
    top: 80px;
    left: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    background: rgba(0, 0, 0, 0.5);
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    z-index: 15;
}

.quality-bars {
    display: flex;
    gap: 2px;
}

.quality-bar {
    width: 3px;
    height: 12px;
    background: #666;
    border-radius: 2px;
}

.quality-bar.active {
    background: #4CAF50;
}

.quality-bar.medium {
    background: #FF9800;
}

.quality-bar.poor {
    background: #f44336;
}

/* Swipe Profiles CSS */
.swipe-container {
    max-width: 400px;
    margin: 0 auto;
    background: #f8f9fa;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    height: 700px;
}

.swipe-header {
    background: linear-gradient(135deg, #549a11, #4a8a0f);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.swipe-filters {
    display: flex;
    gap: 10px;
}

.filter-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn.active,
.filter-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.swipe-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.swipe-cards-container {
    position: relative;
    height: 500px;
    overflow: hidden;
}

.profile-card {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    cursor: grab;
    user-select: none;
    overflow: hidden;
}

.profile-card:active {
    cursor: grabbing;
}

.card-image-container {
    position: relative;
    height: 60%;
    overflow: hidden;
}

.card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-badges {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.online-badge {
    background: rgba(40, 167, 69, 0.9);
    color: white;
}

.premium-badge {
    background: rgba(255, 193, 7, 0.9);
    color: #333;
}

.verified-badge {
    background: rgba(0, 123, 255, 0.9);
    color: white;
}

.card-gradient {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}

.card-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    color: white;
}

.card-header {
    margin-bottom: 15px;
}

.card-name {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 5px 0;
}

.card-location {
    font-size: 14px;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 5px;
}

.card-details {
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    font-size: 13px;
    opacity: 0.9;
}

.detail-item i {
    width: 16px;
    text-align: center;
}

.card-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.card-action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.card-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.swipe-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 10;
}

.swipe-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    font-size: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.reject-btn {
    background: #f44336;
    color: white;
}

.like-btn {
    background: #4CAF50;
    color: white;
}

.super-like-btn {
    background: #2196F3;
    color: white;
}

.video-call-btn {
    background: #9C27B0;
    color: white;
}

.swipe-btn:hover {
    transform: scale(1.1);
}

.swipe-indicators {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 5;
}

.indicator {
    position: absolute;
    padding: 20px;
    border-radius: 15px;
    font-size: 24px;
    font-weight: bold;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.reject-indicator {
    left: 20px;
    background: rgba(244, 67, 54, 0.9);
    color: white;
}

.like-indicator {
    right: 20px;
    background: rgba(76, 175, 80, 0.9);
    color: white;
}

.super-like-indicator {
    left: 50%;
    transform: translateX(-50%) translateY(-50%) scale(0.8);
    background: rgba(33, 150, 243, 0.9);
    color: white;
}

.indicator.active {
    opacity: 1;
    transform: scale(1) translateY(-50%);
}

.super-like-indicator.active {
    transform: translateX(-50%) translateY(-50%) scale(1);
}

.loading-spinner,
.no-more-profiles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(248, 249, 250, 0.95);
    z-index: 20;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e0e0e0;
    border-top: 4px solid #549a11;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.no-profiles-content {
    text-align: center;
    padding: 40px;
}

.no-profiles-content i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}

.swipe-feedback {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 15px 25px;
    border-radius: 25px;
    font-size: 18px;
    font-weight: bold;
    color: white;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.swipe-feedback.success {
    background: #4CAF50;
}

.swipe-feedback.super {
    background: #2196F3;
}

.swipe-feedback.show {
    opacity: 1;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 480px) {
    .swipe-container {
        max-width: 100%;
        height: 100vh;
        border-radius: 0;
    }

    .swipe-filters {
        gap: 5px;
    }

    .filter-btn {
        padding: 4px 8px;
        font-size: 11px;
    }

    .profile-card {
        top: 10px;
        left: 10px;
        right: 10px;
        bottom: 10px;
    }

    .card-name {
        font-size: 20px;
    }

    .swipe-controls {
        gap: 10px;
    }

    .swipe-btn {
        width: 48px;
        height: 48px;
        font-size: 18px;
    }
}
