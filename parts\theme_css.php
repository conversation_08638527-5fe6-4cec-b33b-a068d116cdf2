<?php
/**
 * Dynamic Theme CSS Include
 * This file loads custom theme colors from database and generates CSS
 */

// Include database connection if not already included
if (!class_exists('DatabaseConn')) {
    include_once 'databaseConn.php';
}

// Get theme settings from database
$theme_css = '';
try {
    $DatabaseCo = new DatabaseConn();
    $theme_sql = $DatabaseCo->dbLink->query("SELECT * FROM theme_settings WHERE id='1' AND is_active='1'");
    
    if ($theme_sql && mysqli_num_rows($theme_sql) > 0) {
        $theme = mysqli_fetch_object($theme_sql);
        
        // Generate CSS variables and overrides
        $theme_css = "
        <style>
        :root {
            --primary-color: {$theme->primary_color};
            --secondary-color: {$theme->secondary_color};
            --accent-color: {$theme->accent_color};
            --success-color: {$theme->success_color};
            --warning-color: {$theme->warning_color};
            --danger-color: {$theme->danger_color};
            --dark-color: {$theme->dark_color};
            --light-color: {$theme->light_color};
        }
        
        /* Primary Theme Colors */
        .inThemeGreen, .gt-text-green { color: {$theme->primary_color} !important; }
        .inThemeOrange, .gt-text-orange { color: {$theme->secondary_color} !important; }
        .gt-text-blue { color: {$theme->accent_color} !important; }
        
        /* Background Colors */
        .gt-bg-green { background: {$theme->primary_color} !important; }
        .gt-bg-orange { background: {$theme->secondary_color} !important; }
        .gt-bg-blue { background: {$theme->accent_color} !important; }
        
        /* Button Colors */
        .btn.gt-btn-green, .gt-btn-green { 
            background-color: {$theme->button_primary_color} !important; 
            border-color: {$theme->button_primary_color} !important; 
        }
        .btn.gt-btn-orange, .gt-btn-orange { 
            background-color: {$theme->button_secondary_color} !important; 
            border-color: {$theme->button_secondary_color} !important; 
        }
        .btn.gt-btn-green:hover { 
            background-color: " . adjustBrightness($theme->button_primary_color, -20) . " !important; 
        }
        .btn.gt-btn-orange:hover { 
            background-color: " . adjustBrightness($theme->button_secondary_color, -20) . " !important; 
        }
        
        /* Link Colors */
        a { color: {$theme->link_color}; }
        a:hover, a:focus { color: {$theme->link_hover_color}; }
        
        /* Header and Footer */
        .gt-header { background-color: {$theme->header_bg_color} !important; }
        .gt-footer { background-color: {$theme->footer_bg_color} !important; }
        
        /* Theme Meta Color Update */
        meta[name='theme-color'] { content: '{$theme->primary_color}'; }
        </style>
        
        <script>
        // Update meta theme color dynamically
        document.addEventListener('DOMContentLoaded', function() {
            var metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');
            if (metaThemeColor) {
                metaThemeColor.setAttribute('content', '{$theme->primary_color}');
            }
            
            var metaNavButton = document.querySelector('meta[name=\"msapplication-navbutton-color\"]');
            if (metaNavButton) {
                metaNavButton.setAttribute('content', '{$theme->primary_color}');
            }
            
            var metaApple = document.querySelector('meta[name=\"apple-mobile-web-app-status-bar-style\"]');
            if (metaApple) {
                metaApple.setAttribute('content', '{$theme->primary_color}');
            }
        });
        </script>
        ";
    }
} catch (Exception $e) {
    // Silently fail if theme settings are not available
    $theme_css = '';
}

// Function to adjust color brightness
function adjustBrightness($hex, $steps) {
    $steps = max(-255, min(255, $steps));
    $hex = str_replace('#', '', $hex);
    
    if (strlen($hex) == 3) {
        $hex = str_repeat(substr($hex,0,1), 2).str_repeat(substr($hex,1,1), 2).str_repeat(substr($hex,2,1), 2);
    }
    
    $color_parts = str_split($hex, 2);
    $return = '#';
    
    foreach ($color_parts as $color) {
        $color = hexdec($color);
        $color = max(0, min(255, $color + $steps));
        $return .= str_pad(dechex($color), 2, '0', STR_PAD_LEFT);
    }
    
    return $return;
}

// Output the CSS
echo $theme_css;
?>
